{"name": "@befarmer-platform/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "npx nx run-many --target=serve", "build": "npx nx run-many --target=build", "test": "npx nx run-many --target=test", "lint": "npx nx run-many --target=lint", "e2e": "npx nx run-many --target=e2e"}, "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@reduxjs/toolkit": "^2.8.2", "@types/mapbox-gl": "^3.4.1", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "mapbox-gl": "^3.12.0", "react": "19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-redux": "^9.2.0", "react-router-dom": "^6.29.0", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "tailwind-merge": "^3.3.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/js": "^9.8.0", "@nrwl/react": "^19.8.4", "@nx/cypress": "21.1.2", "@nx/eslint": "21.1.2", "@nx/eslint-plugin": "21.1.2", "@nx/jest": "21.1.2", "@nx/js": "21.1.3", "@nx/react": "21.1.2", "@nx/rollup": "21.1.2", "@nx/vite": "^21.1.3", "@nx/web": "21.1.3", "@nx/webpack": "^21.1.3", "@nx/workspace": "21.1.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-url": "^8.0.2", "@storybook/addon-essentials": "^8.6.11", "@storybook/addon-interactions": "^8.6.11", "@storybook/core-server": "^8.6.11", "@storybook/jest": "^0.2.3", "@storybook/react-webpack5": "^8.6.11", "@storybook/test-runner": "^0.22.0", "@storybook/testing-library": "^0.2.2", "@svgr/rollup": "^8.1.0", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/jest": "^29.5.14", "@types/node": "^20.17.57", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "core-js": "^3.36.1", "css-loader": "^7.1.2", "cypress": "^14.2.1", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "jest-environment-node": "^29.7.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "21.1.2", "postcss": "^8.5.4", "postcss-loader": "^8.1.1", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "rollup": "^4.14.0", "sass": "^1.55.0", "style-loader": "^4.0.0", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vitest": "^3.0.0", "webpack-cli": "^5.1.4"}, "workspaces": ["apps/*", "seller-admin-app", "libs/*", "shared-utils/src/*"]}