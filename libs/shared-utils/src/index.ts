// Utility functions and environment configuration
export * from './lib/shared-utils';

// Redux store, slices, hooks, and API
export * from './lib/redux';

// Axios clients and configuration
export * from './lib/axios/instance';

// Authentication utilities (Phase 1.2)
export * from './lib/auth';

// Common utilities (Phase 1.2)
export * from './lib/common';

// Services - Export all services
export * from './lib/backend/services';

// Backend integration (Phase 1.3)
export * from './lib/backend/apiGateway';
export * from './lib/backend/errorHandling';
export { AuthService } from './lib/backend/authService';
export { ProfileService } from './lib/backend/profileService';

// Types - Export all types
export * from './lib/types';
