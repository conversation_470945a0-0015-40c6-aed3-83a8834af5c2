/**
 * API Gateway Configuration for BeFarma Agricultural Platform
 * Centralized configuration for all microservice endpoints and API management
 */

export interface APIEndpoint {
  baseUrl: string;
  port: number;
  version: string;
  healthCheck: string;
  timeout: number;
  retryConfig: RetryConfig;
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffFactor: number;
  retryableStatusCodes: readonly number[];
}

export interface APIGatewayConfig {
  environment: 'development' | 'staging' | 'production';
  protocol: 'http' | 'https';
  host: string;
  apiKey?: string;
  timeout: number;
  retries: RetryConfig;
  monitoring: {
    enabled: boolean;
    endpoint?: string;
  };
}

/**
 * Microservice endpoints configuration aligned with development planning
 */
export const MICROSERVICE_ENDPOINTS = {
  // Authentication & User Management
  SELLER_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3001,
    version: 'v1',
    healthCheck: '/health',
    timeout: 30000,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Admin Management
  ADMIN_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3002,
    version: 'v1',
    healthCheck: '/health',
    timeout: 30000,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Analytics & Reporting  
  ANALYTICS_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3003,
    version: 'v1',
    healthCheck: '/health',
    timeout: 45000, // Longer timeout for analytics queries
    retryConfig: {
      maxRetries: 2,
      retryDelay: 2000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Crop Management
  CROP_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3004,
    version: 'v1',
    healthCheck: '/health',
    timeout: 30000,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Farm Management
  FARM_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3005,
    version: 'v1',
    healthCheck: '/health',
    timeout: 30000,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Financial Management
  FINANCIAL_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3006,
    version: 'v1',
    healthCheck: '/health',
    timeout: 30000,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Inventory Management
  INVENTORY_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3007,
    version: 'v1',
    healthCheck: '/health',
    timeout: 30000,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Notification Service
  NOTIFICATION_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3008,
    version: 'v1',
    healthCheck: '/health',
    timeout: 15000, // Shorter timeout for notifications
    retryConfig: {
      maxRetries: 2,
      retryDelay: 500,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

  // Order Management
  ORDER_SERVICE: {
    baseUrl: 'http://localhost',
    port: 3009,
    version: 'v1',
    healthCheck: '/health',
    timeout: 30000,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
  },

} as const;

/**
 * Environment-specific configurations
 */
export const ENVIRONMENT_CONFIGS: Record<string, APIGatewayConfig> = {
  development: {
    environment: 'development',
    protocol: 'http',
    host: 'localhost',
    timeout: 30000,
    retries: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
    monitoring: {
      enabled: true,
      endpoint: 'http://localhost:9090/metrics',
    },
  },

  staging: {
    environment: 'staging',
    protocol: 'https',
    host: 'staging-api.befarma.com',
    timeout: 30000,
    retries: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
    monitoring: {
      enabled: true,
      endpoint: 'https://staging-monitoring.befarma.com/metrics',
    },
  },

  production: {
    environment: 'production',
    protocol: 'https',
    host: 'api.befarma.com',
    timeout: 30000,
    retries: {
      maxRetries: 2,
      retryDelay: 1000,
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    },
    monitoring: {
      enabled: true,
      endpoint: 'https://monitoring.befarma.com/metrics',
    },
  },
};

/**
 * API Gateway utilities
 */
export class APIGateway {
  private config: APIGatewayConfig;
  private endpoints: typeof MICROSERVICE_ENDPOINTS;

  constructor(environment = 'development') {
    this.config = ENVIRONMENT_CONFIGS[environment] || ENVIRONMENT_CONFIGS['development'];
    this.endpoints = MICROSERVICE_ENDPOINTS;
  }

  /**
   * Get full URL for a microservice endpoint
   */
  getServiceUrl(serviceName: keyof typeof MICROSERVICE_ENDPOINTS, path = ''): string {
    const endpoint = this.endpoints[serviceName];
    const baseUrl = this.config.environment === 'development' 
      ? `${endpoint.baseUrl}:${endpoint.port}`
      : `${this.config.protocol}://${this.config.host}`;
    
    const versionedPath = path.startsWith('/') ? path : `/${path}`;
    return `${baseUrl}/api/${endpoint.version}${versionedPath}`;
  }

  /**
   * Get health check URL for a service
   */
  getHealthCheckUrl(serviceName: keyof typeof MICROSERVICE_ENDPOINTS): string {
    const endpoint = this.endpoints[serviceName];
    return this.getServiceUrl(serviceName, endpoint.healthCheck);
  }

  /**
   * Get service configuration
   */
  getServiceConfig(serviceName: keyof typeof MICROSERVICE_ENDPOINTS): Readonly<APIEndpoint> {
    return this.endpoints[serviceName];
  }

  /**
   * Get all service endpoints
   */
  getAllServiceUrls(): Record<string, string> {
    const services: Record<string, string> = {};
    
    Object.keys(this.endpoints).forEach((serviceName) => {
      const key = serviceName as keyof typeof MICROSERVICE_ENDPOINTS;
      services[serviceName] = this.getServiceUrl(key);
    });
    
    return services;
  }

  /**
   * Check if service is available (health check)
   */
  async checkServiceHealth(serviceName: keyof typeof MICROSERVICE_ENDPOINTS): Promise<{
    service: string;
    status: 'healthy' | 'unhealthy' | 'unknown';
    responseTime?: number;
    error?: string;
  }> {
    const startTime = Date.now();
    
    try {
      const healthUrl = this.getHealthCheckUrl(serviceName);
      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000), // 5 second timeout for health checks
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          service: serviceName,
          status: 'healthy',
          responseTime,
        };
      } else {
        return {
          service: serviceName,
          status: 'unhealthy',
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`,
        };
      }
    } catch (error) {
      return {
        service: serviceName,
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check health of all services
   */
  async checkAllServicesHealth(): Promise<Record<string, any>> {
    const healthChecks: Record<string, Promise<any>> = {};
    
    Object.keys(this.endpoints).forEach((serviceName) => {
      const key = serviceName as keyof typeof MICROSERVICE_ENDPOINTS;
      healthChecks[serviceName] = this.checkServiceHealth(key);
    });
    
    const results = await Promise.allSettled(Object.values(healthChecks));
    const healthStatus: Record<string, any> = {};
    
    Object.keys(healthChecks).forEach((serviceName, index) => {
      const result = results[index];
      if (result.status === 'fulfilled') {
        healthStatus[serviceName] = result.value;
      } else {
        healthStatus[serviceName] = {
          service: serviceName,
          status: 'unknown',
          error: 'Health check failed',
        };
      }
    });
    
    return healthStatus;
  }

  /**
   * Get current configuration
   */
  getConfig(): APIGatewayConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<APIGatewayConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

/**
 * Service-specific URL builders
 */
export const serviceUrlBuilders = {
  /**
   * Seller service URLs (Phase 3.1, 3.2)
   */
  seller: {
    register: () => '/sellers/register',
    login: () => '/sellers/login',
    profile: (sellerId?: string) => sellerId ? `/sellers/${sellerId}` : '/sellers/profile',
    documents: (sellerId: string) => `/sellers/${sellerId}/documents`,
    verification: (sellerId: string) => `/sellers/${sellerId}/verification`,
  },

  /**
   * Farm service URLs (Phase 3.3)
   */
  farm: {
    farms: () =>   '/farms',
    farmDetails: (farmId: string) => `/farms/${farmId}`,
    farmPhotos: (farmId: string) => `/farms/${farmId}/photos`,
    farmLocation: (farmId: string) => `/farms/${farmId}/location`,
  },


  /**
   * Crop service URLs (Phase 4.1-4.4)
   */
  crop: {
    crops: (farmId?: string) => farmId ? `/crops?farmId=${farmId}` : '/crops',
    cropDetails: (cropId: string) => `/crops/${cropId}`,
    cropMonitoring: (cropId: string) => `/crops/${cropId}/monitoring`,
    cropHealth: (cropId: string) => `/crops/${cropId}/health`,
    cropStages: (cropId: string) => `/crops/${cropId}/stages`,
  },

  /**
   * Order service URLs (Phase 5.1)
   */
  order: {
    orders: (sellerId?: string) => sellerId ? `/orders?sellerId=${sellerId}` : '/orders',
    orderDetails: (orderId: string) => `/orders/${orderId}`,
    createOrder: () => '/orders',
    orderStatus: (orderId: string) => `/orders/${orderId}/status`,
  },

  /**
   * Financial service URLs (Phase 5.2)
   */
  financial: {
    transactions: (sellerId?: string) => sellerId ? `/transactions?sellerId=${sellerId}` : '/transactions',
    revenue: (sellerId: string) => `/revenue/${sellerId}`,
    payments: (sellerId: string) => `/payments/${sellerId}`,
    reports: (sellerId: string) => `/reports/${sellerId}`,
  },

  /**
   * Analytics service URLs (Phase 6.1, 6.2)
   */
  analytics: {
    dashboard: (sellerId: string) => `/analytics/dashboard/${sellerId}`,
    performance: (sellerId: string) => `/analytics/performance/${sellerId}`,
    reports: () => '/analytics/reports',
    insights: (sellerId: string) => `/analytics/insights/${sellerId}`,
  },

  /**
   * Notification service URLs (Phase 7.1, 7.2)
   */
  notification: {
    notifications: (userId: string) => `/notifications/${userId}`,
    send: () => '/notifications/send',
    preferences: (userId: string) => `/notifications/${userId}/preferences`,
    markRead: (notificationId: string) => `/notifications/${notificationId}/read`,
  },

  /**
   * Admin service URLs (Phase 8.1-8.4)
   */
  admin: {
    dashboard: () => '/admin/dashboard',
    users: () => '/admin/users',
    verification: () => '/admin/verification',
    analytics: () => '/admin/analytics',
    reports: () => '/admin/reports',
    userDetails: (userId: string) => `/admin/users/${userId}`,
  },
};

// Singleton instance for global use
export const apiGateway = new APIGateway(process.env['NODE_ENV'] || 'development');

export default {
  APIGateway,
  MICROSERVICE_ENDPOINTS,
  ENVIRONMENT_CONFIGS,
  serviceUrlBuilders,
  apiGateway,
}; 