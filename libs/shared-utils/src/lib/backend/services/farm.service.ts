import { AxiosInstance } from 'axios';
import { farmServiceClient } from '../../axios/instance';
import { FarmDetails } from '../../types/farm.types';

interface FarmResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, string[]>;
  };
}

interface FarmsListResponse {
  farms: FarmDetails[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

class FarmService {
  private axios: AxiosInstance;

  constructor() {
    // ✅ UPDATED: Now using farmServiceClient for direct microservice access
    // Base URL: http://localhost:3005/api/farms (matches postman collection)
    this.axios = farmServiceClient;
  }

  // CRUD Operations - Updated to match postman farm-service.json
  async getFarms(params?: any): Promise<FarmsListResponse> {
    const response = await this.axios.get('/', { params });
    return response.data;
  }

  async getFarmDetails(id: string): Promise<FarmResponse<FarmDetails>> {
    const response = await this.axios.get(`/${id}`);
    return response.data;
  }

  async createFarm(data: Partial<FarmDetails>): Promise<FarmResponse<FarmDetails>> {
    const response = await this.axios.post('/', data);
    return response.data;
  }

  async updateFarm(id: string, data: Partial<FarmDetails>): Promise<FarmResponse<FarmDetails>> {
    const response = await this.axios.put(`/${id}`, data);
    return response.data;
  }

  async deleteFarm(id: string): Promise<FarmResponse> {
    const response = await this.axios.delete(`/${id}`);
    return response.data;
  }

  // Search Operations - Updated to match postman endpoints
  async searchFarms(params: any): Promise<FarmsListResponse> {
    const response = await this.axios.get('/search', { params });
    return response.data;
  }

  async getFarmSuggestions(query: string, limit: number = 5): Promise<FarmResponse<any[]>> {
    const response = await this.axios.get('/suggestions', {
      params: { q: query, limit }
    });
    return response.data;
  }

  async getNearbyFarms(params: {
    latitude: number;
    longitude: number;
    radius: number;
    limit?: number;
  }): Promise<FarmsListResponse> {
    const response = await this.axios.get('/nearby', { params });
    return response.data;
  }

  // Location-based search (legacy method)
  async searchFarmsByLocation(params: {
    latitude: number;
    longitude: number;
    radius: number;
    filters?: any;
  }): Promise<FarmsListResponse> {
    return this.getNearbyFarms(params);
  }

  // Analytics - Updated to match postman endpoints
  async getFarmAnalytics(farmId: string): Promise<FarmResponse<any>> {
    const response = await this.axios.get(`/${farmId}/analytics`);
    return response.data;
  }

  async getFarmPerformance(farmId: string, params?: { period?: string; year?: number }): Promise<FarmResponse<any>> {
    const response = await this.axios.get(`/${farmId}/performance`, { params });
    return response.data;
  }

  async getFarmStatistics(params?: { groupBy?: string; organicOnly?: boolean }): Promise<FarmResponse<any>> {
    const response = await this.axios.get('/statistics', { params });
    return response.data;
  }

  // Legacy method
  async getSellerFarmAnalytics(sellerId: string): Promise<FarmResponse<any>> {
    const response = await this.axios.get(`/analytics/${sellerId}`);
    return response.data;
  }

  // Farm Crops Management - Updated to match postman endpoints
  async getFarmCrops(farmId: string): Promise<FarmResponse<any[]>> {
    const response = await this.axios.get(`/${farmId}/crops`);
    return response.data;
  }

  async addCropToFarm(farmId: string, cropData: any): Promise<FarmResponse<any>> {
    const response = await this.axios.post(`/${farmId}/crops`, cropData);
    return response.data;
  }

  async updateFarmCrop(farmId: string, cropId: string, data: any): Promise<FarmResponse<any>> {
    const response = await this.axios.put(`/${farmId}/crops/${cropId}`, data);
    return response.data;
  }

  // Farm Verification - New endpoints from postman
  async submitForVerification(farmId: string, data: any): Promise<FarmResponse<any>> {
    const response = await this.axios.post(`/${farmId}/verification`, data);
    return response.data;
  }

  async getVerificationStatus(farmId: string): Promise<FarmResponse<any>> {
    const response = await this.axios.get(`/${farmId}/verification`);
    return response.data;
  }


  // Farm Photos/Media
  async uploadPhoto(farmId: string, photo: File): Promise<FarmResponse> {
    const formData = new FormData();
    formData.append('photo', photo);
    const response = await this.axios.post(`/farms/${farmId}/photos`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  }

  async uploadPhotos(farmId: string, photos: FormData): Promise<FarmResponse> {
    const response = await this.axios.post(`/farms/${farmId}/photos`, photos, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  }

  async getFarmPhotos(farmId: string): Promise<FarmResponse<string[]>> {
    const response = await this.axios.get(`/farms/${farmId}/photos`);
    return response.data;
  }

  async deletePhoto(farmId: string, photoId: string): Promise<FarmResponse> {
    const response = await this.axios.delete(`/farms/${farmId}/photos/${photoId}`);
    return response.data;
  }

  // Bulk Operations
  async bulkUpdateFarms(updates: Array<{
    farmId: string;
    data: Partial<FarmDetails>;
  }>): Promise<FarmResponse<any>> {
    const response = await this.axios.put('/farms/bulk-update', { updates });
    return response.data;
  }

  async bulkDeleteFarms(farmIds: string[]): Promise<FarmResponse> {
    const response = await this.axios.delete('/farms/bulk-delete', {
      data: { farmIds }
    });
    return response.data;
  }

  // Export Methods
  async exportFarms(params?: any, format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<Blob> {
    const response = await this.axios.get('/farms/export', {
      params: { ...params, format },
      responseType: 'blob'
    });
    return response.data;
  }

  // Utility Methods
  setAuthToken(token: string): void {
    this.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('authToken', token);
  }

  clearAuthToken(): void {
    delete this.axios.defaults.headers.common['Authorization'];
    localStorage.removeItem('authToken');
  }

  // Health Check
  async healthCheck(): Promise<any> {
    const response = await this.axios.get('/health');
    return response.data;
  }
}

export const farmService = new FarmService();
export default farmService;