/**
 * Admin Service
 * Service class for admin operations matching postman admin-service.json
 */

import { AxiosInstance } from 'axios';
import { adminServiceClient } from '../../axios/instance';
import {
  AdminLoginRequest,
  AdminLoginResponse,
  AdminDashboardResponse,
  AdminSystemHealthResponse,
  AdminSystemStatsResponse,
  AdminSellersListResponse,
  AdminSellerResponse,
  AdminSellersListParams,
  AdminVerifySellerRequest,
  AdminUpdateSellerRequest,
  AdminSuspendSellerRequest,
  AdminFarmsListResponse,
  AdminFarmResponse,
  AdminFarmsListParams,
  AdminVerifyFarmRequest,
  AdminCropsListResponse,
  AdminCropsListParams,
  AdminApproveCropRequest,
  AdminCropResponse,
} from '../../types/admin.types';

class AdminService {
  private axios: AxiosInstance;

  constructor() {
    // ✅ UPDATED: Using adminServiceClient for direct microservice access
    // Base URL: http://localhost:3002/api/v1 (matches postman collection)
    this.axios = adminServiceClient;
  }

  // ✅ Authentication Methods - Updated to match postman admin-service.json
  async login(credentials: AdminLoginRequest): Promise<AdminLoginResponse> {
    const response = await this.axios.post('/admin/login', credentials);
    return response.data;
  }

  async logout(): Promise<void> {
    await this.axios.post('/admin/logout');
  }

  // ✅ Dashboard & Analytics Methods - Updated to match postman admin-service.json
  async getDashboard(): Promise<AdminDashboardResponse> {
    const response = await this.axios.get('/admin/dashboard');
    return response.data;
  }

  async getSystemHealth(): Promise<AdminSystemHealthResponse> {
    const response = await this.axios.get('/admin/system/health');
    return response.data;
  }

  async getSystemStats(): Promise<AdminSystemStatsResponse> {
    const response = await this.axios.get('/admin/system/stats');
    return response.data;
  }

  // ✅ Seller Management Methods - Updated to match postman admin-service.json
  async getAllSellers(params: AdminSellersListParams): Promise<AdminSellersListResponse> {
    const response = await this.axios.get('/admin/sellers', { params });
    return response.data;
  }

  async getSellerById(sellerId: string): Promise<AdminSellerResponse> {
    const response = await this.axios.get(`/admin/sellers/${sellerId}`);
    return response.data;
  }

  async verifySeller(sellerId: string, data: AdminVerifySellerRequest): Promise<AdminSellerResponse> {
    const response = await this.axios.put(`/admin/sellers/${sellerId}/verify`, data);
    return response.data;
  }

  async updateSeller(sellerId: string, data: AdminUpdateSellerRequest): Promise<AdminSellerResponse> {
    const response = await this.axios.put(`/admin/sellers/${sellerId}`, data);
    return response.data;
  }

  async suspendSeller(sellerId: string, data: AdminSuspendSellerRequest): Promise<AdminSellerResponse> {
    const response = await this.axios.put(`/admin/sellers/${sellerId}/suspend`, data);
    return response.data;
  }

  // ✅ Farm Management Methods - Updated to match postman admin-service.json
  async getAllFarms(params: AdminFarmsListParams): Promise<AdminFarmsListResponse> {
    const response = await this.axios.get('/admin/farms', { params });
    return response.data;
  }

  async getFarmById(farmId: string): Promise<AdminFarmResponse> {
    const response = await this.axios.get(`/admin/farms/${farmId}`);
    return response.data;
  }

  async verifyFarm(farmId: string, data: AdminVerifyFarmRequest): Promise<AdminFarmResponse> {
    const response = await this.axios.put(`/admin/farms/${farmId}/verify`, data);
    return response.data;
  }

  // ✅ Crop Management Methods - Updated to match postman admin-service.json
  async getAllCrops(params: AdminCropsListParams): Promise<AdminCropsListResponse> {
    const response = await this.axios.get('/admin/crops', { params });
    return response.data;
  }

  async approveCrop(cropId: string, data: AdminApproveCropRequest): Promise<AdminCropResponse> {
    const response = await this.axios.put(`/admin/crops/${cropId}/approve`, data);
    return response.data;
  }
}

export const adminService = new AdminService();