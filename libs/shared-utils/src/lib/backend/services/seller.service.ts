import { sellerServiceClient } from '../../axios/instance';
import {
  Seller,
  SellerLoginRequest,
  SellerLoginResponse,
  SellerRegistrationRequest,
  SellerUpdateRequest,
  BankDetailsRequest,
  ChangePasswordRequest,
  SellerVerificationRequest,
  SellerKYCSubmissionRequest,
  SellerKYC,
  SellerDashboard,
  SellerSearchParams,
  SellersListResponse,
  SellerResponse
} from '../../types';

class SellerService {
  /**
   * ✅ FIXED: Now using sellerServiceClient which routes through API Gateway
   * This ensures all seller requests go through http://localhost:3000/api/v1/sellers/*
   * instead of directly to the microservice at http://localhost:3001
   */

  // Authentication Methods - Updated to match postman seller-service.json
  async login(credentials: SellerLoginRequest): Promise<SellerLoginResponse> {
    const response = await sellerServiceClient.post('/login', credentials);
    return response.data;
  }

  async register(data: SellerRegistrationRequest): Promise<SellerResponse<Seller>> {
    const response = await sellerServiceClient.post('/register', data);
    return response.data;
  }

  async forgotPassword(email: string): Promise<SellerResponse> {
    const response = await sellerServiceClient.post('/forgot-password', { email });
    return response.data;
  }

  async resetPassword(token: string, newPassword: string): Promise<SellerResponse> {
    const response = await sellerServiceClient.post('/reset-password', { token, newPassword });
    return response.data;
  }



  // Seller Management - Updated to match postman endpoints
  async getSellers(params?: SellerSearchParams): Promise<SellersListResponse> {
    const response = await sellerServiceClient.get('/', { params });
    return response.data;
  }

  async getSellerProfile(): Promise<SellerResponse<Seller>> {
    const response = await sellerServiceClient.get('/profile');
    return response.data;
  }

  async updateSellerProfile(data: SellerUpdateRequest): Promise<SellerResponse<Seller>> {
    const response = await sellerServiceClient.put('/profile', data);
    return response.data;
  }

  async updateBankDetails(data: BankDetailsRequest): Promise<SellerResponse<any>> {
    const response = await sellerServiceClient.put('/profile/bank-details', data);
    return response.data;
  }

  async changePassword(data: ChangePasswordRequest): Promise<SellerResponse<any>> {
    const response = await sellerServiceClient.put('/change-password', data);
    return response.data;
  }

  async deleteSellerAccount(): Promise<SellerResponse> {
    const response = await sellerServiceClient.delete('/profile');
    return response.data;
  }

  // Legacy methods for backward compatibility
  async getSellerById(sellerId: string): Promise<SellerResponse<Seller>> {
    const response = await sellerServiceClient.get(`/${sellerId}`);
    return response.data;
  }

  async updateSeller(sellerId: string, data: SellerUpdateRequest): Promise<SellerResponse<Seller>> {
    const response = await sellerServiceClient.put(`/${sellerId}`, data);
    return response.data;
  }

  async deleteSeller(sellerId: string): Promise<SellerResponse> {
    const response = await sellerServiceClient.delete(`/${sellerId}`);
    return response.data;
  }

  // Document Management - Updated to match postman endpoints
  async uploadDocuments(sellerId: string, documents: FormData): Promise<SellerResponse> {
    const response = await sellerServiceClient.post(`/${sellerId}/documents`, documents, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  }

  async getSellerDocuments(sellerId: string): Promise<SellerResponse<any[]>> {
    const response = await sellerServiceClient.get(`/${sellerId}/documents`);
    return response.data;
  }

  // Verification Methods
  async verifySeller(data: SellerVerificationRequest): Promise<SellerResponse<Seller>> {
    const response = await sellerServiceClient.put(`/${data.sellerId}/verify`, data);
    return response.data;
  }

  // KYC Methods - Updated to match postman endpoints
  async submitKYC(data: SellerKYCSubmissionRequest): Promise<SellerResponse<SellerKYC>> {
    const response = await sellerServiceClient.post('/kyc/submit', data);
    return response.data;
  }

  async getKYCStatus(): Promise<SellerResponse<SellerKYC>> {
    const response = await sellerServiceClient.get('/kyc/status');
    return response.data;
  }

  async verifyKYC(sellerId: string, status: 'VERIFIED' | 'REJECTED', reason?: string): Promise<SellerResponse<SellerKYC>> {
    const response = await sellerServiceClient.put(`/${sellerId}/kyc/verify`, { status, reason });
    return response.data;
  }

  // Dashboard Methods - Updated to match postman endpoints
  async getSellerDashboard(): Promise<SellerResponse<SellerDashboard>> {
    const response = await sellerServiceClient.get('/dashboard');
    return response.data;
  }



  // Farm Management (through seller service) - Updated endpoints
  async getSellerFarms(): Promise<SellerResponse<any[]>> {
    const response = await sellerServiceClient.get('/farms');
    return response.data;
  }

  async createSellerFarm(farmData: any): Promise<SellerResponse<any>> {
    const response = await sellerServiceClient.post('/farms', farmData);
    return response.data;
  }

  // Analytics Methods - Updated to match postman endpoints
  async getSellerAnalytics(sellerId: string, params?: any): Promise<SellerResponse<any>> {
    const response = await sellerServiceClient.get(`/${sellerId}/analytics`, { params });
    return response.data;
  }

  // Search Methods - Updated to match postman endpoints
  async searchSellers(query: string, filters?: any): Promise<SellersListResponse> {
    const response = await sellerServiceClient.get('/search', {
      params: { q: query, ...filters }
    });
    return response.data;
  }

  // Utility Methods
  setAuthToken(token: string): void {
    sellerServiceClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('authToken', token);
  }

  clearAuthToken(): void {
    delete sellerServiceClient.defaults.headers.common['Authorization'];
    localStorage.removeItem('authToken');
  }

  // Health Check
  async healthCheck(): Promise<any> {
    const response = await sellerServiceClient.get('/health');
    return response.data;
  }
}

export const sellerService = new SellerService();
export default sellerService;
