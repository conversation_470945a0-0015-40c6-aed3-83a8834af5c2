import { AxiosInstance } from 'axios';
import { analyticsServiceClient } from '../../axios/instance';
import {
  DashboardAnalyticsResponse,
  RevenueAnalytics,
  SalesAnalytics,
  CropAnalyticsSummary,
  UserAnalytics,
  MarketAnalytics,
  PerformanceAnalytics,
  AnalyticsReport,
  AnalyticsRequest,
  AnalyticsResponse
} from '../../types';

class AnalyticsService {
  private axios: AxiosInstance;

  constructor() {
    // ✅ FIXED: Now using analyticsServiceClient which routes through API Gateway
    // This ensures all analytics requests go through http://localhost:3000/api/v1/analytics/*
    // instead of directly to the microservice at http://localhost:3003
    this.axios = analyticsServiceClient;
  }

  // Dashboard Analytics
  async getDashboardAnalytics(filters?: any): Promise<AnalyticsResponse<DashboardAnalyticsResponse>> {
    const response = await this.axios.get('/analytics/dashboard', { params: filters });
    return response.data;
  }

  async getAdminDashboardAnalytics(filters?: any): Promise<AnalyticsResponse<DashboardAnalyticsResponse>> {
    const response = await this.axios.get('/analytics/admin/dashboard', { params: filters });
    return response.data;
  }

  // Revenue Analytics
  async getRevenueAnalytics(filters?: any): Promise<AnalyticsResponse<RevenueAnalytics>> {
    const response = await this.axios.get('/analytics/revenue', { params: filters });
    return response.data;
  }

  async getRevenueByPeriod(period: 'day' | 'week' | 'month' | 'quarter' | 'year', filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get(`/analytics/revenue/${period}`, { params: filters });
    return response.data;
  }

  async getRevenueByLocation(filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/revenue/location', { params: filters });
    return response.data;
  }

  // Sales Analytics
  async getSalesAnalytics(filters?: any): Promise<AnalyticsResponse<SalesAnalytics>> {
    const response = await this.axios.get('/analytics/sales', { params: filters });
    return response.data;
  }

  async getSalesTrends(period: string, filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/sales/trends', { 
      params: { period, ...filters } 
    });
    return response.data;
  }

  async getTopSellingProducts(limit = 20, filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/sales/top-products', { 
      params: { limit, ...filters } 
    });
    return response.data;
  }

  // Crop Analytics
  async getCropAnalytics(filters?: any): Promise<AnalyticsResponse<CropAnalyticsSummary>> {
    const response = await this.axios.get('/analytics/crops', { params: filters });
    return response.data;
  }

  async getCropPerformance(cropId: string): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get(`/analytics/crops/${cropId}/performance`);
    return response.data;
  }

  async getCropYieldAnalytics(filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/crops/yield', { params: filters });
    return response.data;
  }

  async getCropQualityAnalytics(filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/crops/quality', { params: filters });
    return response.data;
  }

  // User Analytics
  async getUserAnalytics(filters?: any): Promise<AnalyticsResponse<UserAnalytics>> {
    const response = await this.axios.get('/analytics/users', { params: filters });
    return response.data;
  }

  async getUserEngagement(filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/users/engagement', { params: filters });
    return response.data;
  }

  async getUserRetention(filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/users/retention', { params: filters });
    return response.data;
  }

  // Market Analytics
  async getMarketAnalytics(filters?: any): Promise<AnalyticsResponse<MarketAnalytics>> {
    const response = await this.axios.get('/analytics/market', { params: filters });
    return response.data;
  }

  async getPriceAnalytics(cropType?: string, location?: string): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/market/prices', { 
      params: { cropType, location } 
    });
    return response.data;
  }

  async getDemandSupplyAnalysis(filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/market/demand-supply', { params: filters });
    return response.data;
  }

  // Performance Analytics
  async getPerformanceAnalytics(filters?: any): Promise<AnalyticsResponse<PerformanceAnalytics>> {
    const response = await this.axios.get('/analytics/performance', { params: filters });
    return response.data;
  }

  async getSystemMetrics(): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/performance/system');
    return response.data;
  }

  async getBusinessMetrics(filters?: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/performance/business', { params: filters });
    return response.data;
  }

  // Report Generation
  async generateReport(request: AnalyticsRequest): Promise<AnalyticsResponse<AnalyticsReport>> {
    const response = await this.axios.post('/analytics/reports/generate', request);
    return response.data;
  }

  async getReports(filters?: any): Promise<AnalyticsResponse<AnalyticsReport[]>> {
    const response = await this.axios.get('/analytics/reports', { params: filters });
    return response.data;
  }

  async getReportById(reportId: string): Promise<AnalyticsResponse<AnalyticsReport>> {
    const response = await this.axios.get(`/analytics/reports/${reportId}`);
    return response.data;
  }

  async downloadReport(reportId: string, format: 'pdf' | 'excel' | 'csv' = 'pdf'): Promise<Blob> {
    const response = await this.axios.get(`/analytics/reports/${reportId}/download`, {
      params: { format },
      responseType: 'blob'
    });
    return response.data;
  }

  async deleteReport(reportId: string): Promise<AnalyticsResponse> {
    const response = await this.axios.delete(`/analytics/reports/${reportId}`);
    return response.data;
  }

  // Custom Analytics
  async getCustomAnalytics(query: any): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.post('/analytics/custom', query);
    return response.data;
  }

  // Real-time Analytics
  async getRealTimeMetrics(): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/realtime');
    return response.data;
  }

  async getLiveStats(): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.get('/analytics/live-stats');
    return response.data;
  }

  // Comparative Analytics
  async getComparativeAnalytics(params: {
    metric: string;
    periods: string[];
    filters?: any;
  }): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.post('/analytics/comparative', params);
    return response.data;
  }

  // Predictive Analytics
  async getPredictiveAnalytics(params: {
    type: 'revenue' | 'demand' | 'yield' | 'price';
    horizon: number; // days
    filters?: any;
  }): Promise<AnalyticsResponse<any>> {
    const response = await this.axios.post('/analytics/predictive', params);
    return response.data;
  }

  // Export Methods
  async exportAnalytics(params: any, format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<Blob> {
    const response = await this.axios.get('/analytics/export', {
      params: { ...params, format },
      responseType: 'blob'
    });
    return response.data;
  }

  // Utility Methods
  setAuthToken(token: string): void {
    this.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken(): void {
    delete this.axios.defaults.headers.common['Authorization'];
  }

  // Health Check
  async healthCheck(): Promise<any> {
    const response = await this.axios.get('/health');
    return response.data;
  }
}

export const analyticsService = new AnalyticsService();
export default analyticsService;
