import { AxiosInstance } from 'axios';
import { orderServiceClient } from '../../axios/instance';
import {
  Order,
  CreateOrderRequest,
  UpdateOrderRequest,
  OrderStatusUpdateRequest,
  RefundRequest,
  ShippingUpdateRequest,
  OrderSearchParams,
  OrderAnalytics,
  OrderResponse,
  OrdersListResponse,
  OrderStatsResponse
} from '../../types';

class OrderService {
  private axios: AxiosInstance;

  constructor() {
    // ✅ UPDATED: Now using orderServiceClient for direct microservice access
    // Base URL: http://localhost:3009/api/orders (matches postman collection)
    this.axios = orderServiceClient;
  }

  // Order CRUD Operations - Updated to match postman order-service.json
  async getOrders(params?: OrderSearchParams): Promise<OrdersListResponse> {
    const response = await this.axios.get('/', { params });
    return response.data;
  }

  async getOrderById(orderId: string): Promise<OrderResponse<Order>> {
    const response = await this.axios.get(`/${orderId}`);
    return response.data;
  }

  async createOrder(request: CreateOrderRequest): Promise<OrderResponse<Order>> {
    const response = await this.axios.post('/', request);
    return response.data;
  }

  async updateOrder(orderId: string, request: UpdateOrderRequest): Promise<OrderResponse<Order>> {
    const response = await this.axios.put(`/${orderId}`, request);
    return response.data;
  }

  async deleteOrder(orderId: string): Promise<OrderResponse> {
    const response = await this.axios.delete(`/${orderId}`);
    return response.data;
  }

  // Order Lifecycle Management - Updated to match postman endpoints
  async acceptOrder(orderId: string, data?: { estimatedDelivery?: string; notes?: string }): Promise<OrderResponse<Order>> {
    const response = await this.axios.put(`/${orderId}/accept`, data);
    return response.data;
  }

  async rejectOrder(orderId: string, data: { reason: string; alternativeOptions?: any[] }): Promise<OrderResponse<Order>> {
    const response = await this.axios.put(`/${orderId}/reject`, data);
    return response.data;
  }

  async markAsShipped(orderId: string, shippingData: any): Promise<OrderResponse<Order>> {
    const response = await this.axios.put(`/${orderId}/ship`, shippingData);
    return response.data;
  }

  async markAsDelivered(orderId: string, deliveryData: any): Promise<OrderResponse<Order>> {
    const response = await this.axios.put(`/${orderId}/deliver`, deliveryData);
    return response.data;
  }

  async cancelOrder(orderId: string, data: { reason: string; refundAmount?: number; cancelledBy?: string }): Promise<OrderResponse<Order>> {
    const response = await this.axios.put(`/${orderId}/cancel`, data);
    return response.data;
  }

  // Legacy methods for backward compatibility
  async updateOrderStatus(orderId: string, request: OrderStatusUpdateRequest): Promise<OrderResponse<Order>> {
    const response = await this.axios.put(`/${orderId}/status`, request);
    return response.data;
  }

  async confirmOrder(orderId: string, notes?: string): Promise<OrderResponse<Order>> {
    return this.acceptOrder(orderId, { notes });
  }

  async fulfillOrder(orderId: string, fulfillmentData: any): Promise<OrderResponse<Order>> {
    return this.markAsShipped(orderId, fulfillmentData);
  }

  // Payment Processing - Updated to match postman endpoints
  async processPayment(orderId: string, paymentData: any): Promise<OrderResponse<any>> {
    const response = await this.axios.post(`/${orderId}/payment`, paymentData);
    return response.data;
  }

  async getPaymentStatus(orderId: string): Promise<OrderResponse<any>> {
    const response = await this.axios.get(`/${orderId}/payment`);
    return response.data;
  }

  async processRefund(orderId: string, refundData: any): Promise<OrderResponse<any>> {
    const response = await this.axios.post(`/${orderId}/refund`, refundData);
    return response.data;
  }

  // Legacy methods for backward compatibility
  async refundPayment(request: RefundRequest): Promise<OrderResponse<any>> {
    return this.processRefund(request.orderId, request);
  }

  async retryPayment(orderId: string, paymentData?: any): Promise<OrderResponse<any>> {
    return this.processPayment(orderId, paymentData);
  }

  // Order Tracking - Updated to match postman endpoints
  async getOrderTracking(orderId: string): Promise<OrderResponse<any>> {
    const response = await this.axios.get(`/${orderId}/tracking`);
    return response.data;
  }

  async updateTrackingStatus(orderId: string, trackingData: any): Promise<OrderResponse<any>> {
    const response = await this.axios.post(`/${orderId}/tracking`, trackingData);
    return response.data;
  }

  async getTrackingHistory(orderId: string): Promise<OrderResponse<any[]>> {
    const response = await this.axios.get(`/${orderId}/tracking/history`);
    return response.data;
  }

  // Legacy methods for backward compatibility
  async updateShipping(request: ShippingUpdateRequest): Promise<OrderResponse<Order>> {
    return this.updateTrackingStatus(request.orderId, request);
  }

  async trackOrder(orderId: string): Promise<OrderResponse<any>> {
    return this.getOrderTracking(orderId);
  }

  async updateTrackingInfo(orderId: string, trackingData: {
    trackingNumber: string;
    carrier: string;
    status: string;
    location?: string;
    estimatedDelivery?: string;
  }): Promise<OrderResponse<Order>> {
    return this.updateTrackingStatus(orderId, trackingData);
  }

  // Search and Filter
  async searchOrders(params: OrderSearchParams): Promise<OrdersListResponse> {
    const response = await this.axios.get('/orders/search', { params });
    return response.data;
  }

  async getSellerOrders(sellerId: string, params?: any): Promise<OrdersListResponse> {
    const response = await this.axios.get(`/orders/seller/${sellerId}`, { params });
    return response.data;
  }

  async getBuyerOrders(buyerId: string, params?: any): Promise<OrdersListResponse> {
    const response = await this.axios.get(`/orders/buyer/${buyerId}`, { params });
    return response.data;
  }

  async getOrdersByStatus(status: Order['status'], params?: any): Promise<OrdersListResponse> {
    const response = await this.axios.get(`/orders/status/${status}`, { params });
    return response.data;
  }

  // Analytics and Reports - Updated to match postman endpoints
  async getOrderAnalytics(params?: any): Promise<OrderResponse<OrderAnalytics>> {
    const response = await this.axios.get('/analytics', { params });
    return response.data;
  }

  async getRevenueAnalytics(params?: { period?: string; year?: number; groupBy?: string }): Promise<OrderResponse<any>> {
    const response = await this.axios.get('/analytics/revenue', { params });
    return response.data;
  }

  async getOrderPerformance(params?: { metric?: string; period?: string }): Promise<OrderResponse<any>> {
    const response = await this.axios.get('/analytics/performance', { params });
    return response.data;
  }

  // Legacy methods for backward compatibility
  async getOrderStats(filters?: any): Promise<OrderStatsResponse> {
    const analytics = await this.getOrderAnalytics(filters);
    // Transform analytics to stats format for backward compatibility
    const statusDistribution: Record<string, number> = {};
    analytics.data?.statusDistribution?.forEach((item: any) => {
      statusDistribution[item.status] = item.count;
    });

    return {
      totalOrders: analytics.data?.overview?.totalOrders || 0,
      totalRevenue: analytics.data?.overview?.totalRevenue || 0,
      averageOrderValue: analytics.data?.overview?.averageOrderValue || 0,
      recentOrders: [], // This would need to come from a separate endpoint
      statusDistribution,
      monthlyTrends: analytics.data?.trends?.map((trend: any) => ({
        month: trend.date,
        orders: trend.orders,
        revenue: trend.revenue
      })) || []
    };
  }

  async getSellerOrderAnalytics(sellerId: string, filters?: any): Promise<OrderResponse<any>> {
    const response = await this.axios.get(`/analytics/seller/${sellerId}`, { params: filters });
    return response.data;
  }

  async getOrderTrends(period: 'day' | 'week' | 'month' | 'quarter' | 'year', filters?: any): Promise<OrderResponse<any>> {
    return this.getOrderPerformance({ period, ...filters });
  }

  // Bulk Operations
  async bulkUpdateOrders(updates: Array<{
    orderId: string;
    data: UpdateOrderRequest;
  }>): Promise<OrderResponse<any>> {
    const response = await this.axios.put('/orders/bulk-update', { updates });
    return response.data;
  }

  async bulkUpdateStatus(orderIds: string[], status: Order['status'], notes?: string): Promise<OrderResponse<any>> {
    const response = await this.axios.put('/orders/bulk-status-update', { orderIds, status, notes });
    return response.data;
  }

  async bulkCancel(orderIds: string[], reason: string): Promise<OrderResponse<any>> {
    const response = await this.axios.post('/orders/bulk-cancel', { orderIds, reason });
    return response.data;
  }

  // Invoice and Documentation
  async generateInvoice(orderId: string): Promise<OrderResponse<any>> {
    const response = await this.axios.post(`/orders/${orderId}/invoice`);
    return response.data;
  }

  async downloadInvoice(orderId: string, format: 'pdf' | 'html' = 'pdf'): Promise<Blob> {
    const response = await this.axios.get(`/orders/${orderId}/invoice/download`, {
      params: { format },
      responseType: 'blob'
    });
    return response.data;
  }

  async generatePackingSlip(orderId: string): Promise<OrderResponse<any>> {
    const response = await this.axios.post(`/orders/${orderId}/packing-slip`);
    return response.data;
  }

  async downloadPackingSlip(orderId: string): Promise<Blob> {
    const response = await this.axios.get(`/orders/${orderId}/packing-slip/download`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Export Methods
  async exportOrders(params?: any, format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<Blob> {
    const response = await this.axios.get('/orders/export', {
      params: { ...params, format },
      responseType: 'blob'
    });
    return response.data;
  }

  // Order Timeline and History
  async getOrderTimeline(orderId: string): Promise<OrderResponse<any[]>> {
    const response = await this.axios.get(`/orders/${orderId}/timeline`);
    return response.data;
  }

  async addOrderNote(orderId: string, note: string, isInternal = false): Promise<OrderResponse> {
    const response = await this.axios.post(`/orders/${orderId}/notes`, { note, isInternal });
    return response.data;
  }

  // Dispute Management
  async createDispute(orderId: string, disputeData: {
    reason: string;
    description: string;
    evidence?: string[];
  }): Promise<OrderResponse<any>> {
    const response = await this.axios.post(`/orders/${orderId}/dispute`, disputeData);
    return response.data;
  }

  async resolveDispute(orderId: string, resolution: {
    resolution: string;
    refundAmount?: number;
    notes?: string;
  }): Promise<OrderResponse<any>> {
    const response = await this.axios.post(`/orders/${orderId}/dispute/resolve`, resolution);
    return response.data;
  }

  // Utility Methods
  setAuthToken(token: string): void {
    this.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken(): void {
    delete this.axios.defaults.headers.common['Authorization'];
  }

  // Health Check
  async healthCheck(): Promise<any> {
    const response = await this.axios.get('/health');
    return response.data;
  }
}

export const orderService = new OrderService();
export default orderService;
