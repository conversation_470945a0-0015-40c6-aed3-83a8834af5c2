import { AxiosInstance } from 'axios';
import { cropServiceClient } from '../../axios/instance';
import {
  Crop,
  CropCreateRequest,
  CropUpdateRequest,
  CropSearchParams,
  CropSearchResponse,
  CropResponse,
  CropsListResponse,
  // ✅ TEMPORARILY COMMENTED: Missing types that need to be implemented
  // GrowthStageUpdateRequest,
  // HealthStatusUpdateRequest,
  // HarvestRecordRequest,
  // MaintenanceActivity,
  // MarketplaceListing,
  // AnalyticsParams,
  // MarketInsightsParams,
  // PriceTrendsParams,
  // NearbyCropsParams,
  // QualityReportRequest
} from '../../types';

class CropService {
  private axios: AxiosInstance;

  constructor() {
    // ✅ UPDATED: Now using cropServiceClient for direct microservice access
    // Base URL: http://localhost:3004/api/crops (matches postman collection)
    this.axios = cropServiceClient;
  }

  // Health Check Methods
  async healthCheck(): Promise<any> {
    const response = await this.axios.get('/health');
    return response.data;
  }

  async testSearch(query: string): Promise<any> {
    const response = await this.axios.get('/test-search', { params: { q: query } });
    return response.data;
  }

  // CRUD Operations - Updated to match postman crop-service.json
  async getCrops(params?: CropSearchParams): Promise<CropsListResponse> {
    const response = await this.axios.get('/', { params });
    return response.data;
  }

  async getCropById(cropId: string): Promise<CropResponse<Crop>> {
    const response = await this.axios.get(`/${cropId}`);
    return response.data;
  }

  async createCrop(data: CropCreateRequest): Promise<CropResponse<Crop>> {
    const response = await this.axios.post('/', data);
    return response.data;
  }

  async updateCrop(cropId: string, data: CropUpdateRequest): Promise<CropResponse<Crop>> {
    const response = await this.axios.put(`/${cropId}`, data);
    return response.data;
  }

  async deleteCrop(cropId: string): Promise<CropResponse> {
    const response = await this.axios.delete(`/${cropId}`);
    return response.data;
  }

  // ✅ TEMPORARILY USING ANY: Crop Lifecycle Management Methods (from postman collection)
  async updateGrowthStage(cropId: string, data: any): Promise<CropResponse<Crop>> {
    const response = await this.axios.put(`/${cropId}/growth-stage`, data);
    return response.data;
  }

  async updateHealthStatus(cropId: string, data: any): Promise<CropResponse<Crop>> {
    const response = await this.axios.put(`/${cropId}/health`, data);
    return response.data;
  }

  async recordHarvest(cropId: string, data: any): Promise<CropResponse<Crop>> {
    const response = await this.axios.post(`/${cropId}/harvest`, data);
    return response.data;
  }

  async addMaintenanceActivity(cropId: string, data: any): Promise<CropResponse> {
    const response = await this.axios.post(`/${cropId}/maintenance`, data);
    return response.data;
  }

  async getCropAnalytics(sellerId: string, params?: any): Promise<CropResponse<any>> {
    const response = await this.axios.get(`/analytics/${sellerId}`, { params });
    return response.data;
  }

  async reindexAllCrops(): Promise<CropResponse> {
    const response = await this.axios.post('/reindex');
    return response.data;
  }

  // Search & Discovery Methods - Updated to match postman endpoints
  async searchCrops(params: CropSearchParams): Promise<CropSearchResponse> {
    const response = await this.axios.get('/search', { params });
    return response.data;
  }

  async getCropSuggestions(query: string, limit = 5): Promise<CropResponse<string[]>> {
    const response = await this.axios.get('/suggestions', {
      params: { q: query, limit }
    });
    return response.data;
  }

  async getPopularCrops(limit = 10, category?: string): Promise<CropResponse<Crop[]>> {
    const response = await this.axios.get('/popular', {
      params: { limit, category }
    });
    return response.data;
  }

  async getNearbyCrops(params: any): Promise<CropSearchResponse> {
    const response = await this.axios.get('/nearby', { params });
    return response.data;
  }

  // ✅ TEMPORARILY USING ANY: Marketplace Methods - Updated to match postman endpoints
  async getMarketplaceListings(params?: {
    category?: string;
    available?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<CropSearchResponse> {
    const response = await this.axios.get('/marketplace', { params });
    return response.data;
  }

  async createMarketplaceListing(data: any): Promise<CropResponse<any>> {
    const response = await this.axios.post('/marketplace', data);
    return response.data;
  }

  async updateMarketplaceListing(listingId: string, data: any): Promise<CropResponse<any>> {
    const response = await this.axios.put(`/marketplace/${listingId}`, data);
    return response.data;
  }

  // Analytics & Insights Methods - Updated to match postman endpoints
  async getCropAnalyticsById(cropId: string): Promise<CropResponse<any>> {
    const response = await this.axios.get(`/${cropId}/analytics`);
    return response.data;
  }

  async getMarketInsights(params?: any): Promise<CropResponse<any>> {
    const response = await this.axios.get('/insights/market', { params });
    return response.data;
  }

  async getPriceTrends(params?: any): Promise<CropResponse<any>> {
    const response = await this.axios.get('/insights/price-trends', { params });
    return response.data;
  }

  async getCropStatistics(params?: {
    groupBy?: string;
    organic?: boolean;
    period?: string;
  }): Promise<CropResponse<any>> {
    const response = await this.axios.get('/statistics', { params });
    return response.data;
  }

  // ✅ TEMPORARILY USING ANY: Quality & Certification Methods - Updated to match postman endpoints
  async submitQualityReport(data: any): Promise<CropResponse<any>> {
    const response = await this.axios.post(`/${data.cropId}/quality`, data);
    return response.data;
  }

  async getQualityReport(cropId: string): Promise<CropResponse<any>> {
    const response = await this.axios.get(`/${cropId}/quality`);
    return response.data;
  }

  // Legacy methods for backward compatibility
  async getSellerCropAnalytics(sellerId: string, params?: any): Promise<CropResponse<any>> {
    const response = await this.axios.get(`/analytics/${sellerId}`, { params });
    return response.data;
  }

  async getFarmCropAnalytics(farmId: string, params?: any): Promise<CropResponse<any>> {
    const response = await this.axios.get(`/farm/${farmId}/analytics`, { params });
    return response.data;
  }

  async getCropTrends(params?: {
    cropType?: string;
    location?: string;
    timeRange?: string;
  }): Promise<CropResponse<any>> {
    return this.getPriceTrends({ crop: params?.cropType, region: params?.location, period: params?.timeRange });
  }

  // Legacy Market Intelligence Methods (keeping for backward compatibility)
  async getLegacyMarketInsights(cropType: string, location?: string): Promise<CropResponse<any>> {
    return this.getMarketInsights({ category: cropType, region: location });
  }

  async getPriceAnalytics(cropType: string, params?: any): Promise<CropResponse<any>> {
    const response = await this.axios.get('/market/price-analytics', {
      params: { cropType, ...params }
    });
    return response.data;
  }

  async getDemandSupplyAnalysis(cropType: string, location?: string): Promise<CropResponse<any>> {
    const response = await this.axios.get('/market/demand-supply', {
      params: { cropType, location }
    });
    return response.data;
  }

  // Bulk Operations
  async bulkUpdateCrops(updates: Array<{
    cropId: string;
    data: CropUpdateRequest;
  }>): Promise<CropResponse<any>> {
    const response = await this.axios.put('/crops/bulk-update', { updates });
    return response.data;
  }

  async bulkDeleteCrops(cropIds: string[]): Promise<CropResponse> {
    const response = await this.axios.delete('/crops/bulk-delete', {
      data: { cropIds }
    });
    return response.data;
  }

  // Export Methods
  async exportCrops(params?: any, format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<Blob> {
    const response = await this.axios.get('/crops/export', {
      params: { ...params, format },
      responseType: 'blob'
    });
    return response.data;
  }

  // Utility Methods
  setAuthToken(token: string): void {
    this.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken(): void {
    delete this.axios.defaults.headers.common['Authorization'];
  }
}

export const cropService = new CropService();
export default cropService;
