import { AxiosInstance } from 'axios';
import { notificationServiceClient } from '../../axios/instance';
import {
  Notification,
  NotificationTemplate,
  NotificationPreferences,
  BulkNotification,
  NotificationCampaign,
  SendNotificationRequest,
  BulkNotificationRequest,
  NotificationPreferencesUpdateRequest,
  CreateTemplateRequest,
  UpdateTemplateRequest,
  NotificationSearchParams,
  NotificationAnalytics,
  NotificationResponse,
  NotificationsListResponse,
  NotificationStatsResponse
} from '../../types';

class NotificationService {
  private axios: AxiosInstance;

  constructor() {
    // ✅ FIXED: Now using notificationServiceClient which routes through API Gateway
    // This ensures all notification requests go through http://localhost:3000/api/v1/notifications/*
    // instead of directly to the microservice at http://localhost:3008
    this.axios = notificationServiceClient;
  }

  // Notification CRUD
  async getNotifications(params?: NotificationSearchParams): Promise<NotificationsListResponse> {
    const response = await this.axios.get('/notifications', { params });
    return response.data;
  }

  async getNotificationById(notificationId: string): Promise<NotificationResponse<Notification>> {
    const response = await this.axios.get(`/notifications/${notificationId}`);
    return response.data;
  }

  async getUserNotifications(userId: string, params?: any): Promise<NotificationsListResponse> {
    const response = await this.axios.get(`/notifications/user/${userId}`, { params });
    return response.data;
  }

  async getNotificationStats(userId?: string): Promise<NotificationStatsResponse> {
    const response = await this.axios.get('/notifications/stats', { 
      params: userId ? { userId } : undefined 
    });
    return response.data;
  }

  // Send Notifications
  async sendNotification(request: SendNotificationRequest): Promise<NotificationResponse<Notification>> {
    const response = await this.axios.post('/notifications/send', request);
    return response.data;
  }

  async sendBulkNotification(request: BulkNotificationRequest): Promise<NotificationResponse<BulkNotification>> {
    const response = await this.axios.post('/notifications/bulk-send', request);
    return response.data;
  }

  async sendTemplateNotification(templateId: string, data: {
    recipient: any;
    variables: Record<string, any>;
    channels: string[];
    scheduledAt?: string;
  }): Promise<NotificationResponse<Notification>> {
    const response = await this.axios.post(`/notifications/templates/${templateId}/send`, data);
    return response.data;
  }

  // Notification Actions
  async markAsRead(notificationId: string): Promise<NotificationResponse> {
    const response = await this.axios.put(`/notifications/${notificationId}/read`);
    return response.data;
  }

  async markAsUnread(notificationId: string): Promise<NotificationResponse> {
    const response = await this.axios.put(`/notifications/${notificationId}/unread`);
    return response.data;
  }

  async markAllAsRead(userId: string): Promise<NotificationResponse> {
    const response = await this.axios.put(`/notifications/user/${userId}/read-all`);
    return response.data;
  }

  async deleteNotification(notificationId: string): Promise<NotificationResponse> {
    const response = await this.axios.delete(`/notifications/${notificationId}`);
    return response.data;
  }

  async deleteUserNotifications(userId: string, notificationIds: string[]): Promise<NotificationResponse> {
    const response = await this.axios.delete(`/notifications/user/${userId}/bulk-delete`, {
      data: { notificationIds }
    });
    return response.data;
  }

  // Template Management
  async getTemplates(): Promise<NotificationResponse<NotificationTemplate[]>> {
    const response = await this.axios.get('/notifications/templates');
    return response.data;
  }

  async getTemplateById(templateId: string): Promise<NotificationResponse<NotificationTemplate>> {
    const response = await this.axios.get(`/notifications/templates/${templateId}`);
    return response.data;
  }

  async createTemplate(request: CreateTemplateRequest): Promise<NotificationResponse<NotificationTemplate>> {
    const response = await this.axios.post('/notifications/templates', request);
    return response.data;
  }

  async updateTemplate(templateId: string, request: UpdateTemplateRequest): Promise<NotificationResponse<NotificationTemplate>> {
    const response = await this.axios.put(`/notifications/templates/${templateId}`, request);
    return response.data;
  }

  async deleteTemplate(templateId: string): Promise<NotificationResponse> {
    const response = await this.axios.delete(`/notifications/templates/${templateId}`);
    return response.data;
  }

  async previewTemplate(templateId: string, variables: Record<string, any>): Promise<NotificationResponse<any>> {
    const response = await this.axios.post(`/notifications/templates/${templateId}/preview`, { variables });
    return response.data;
  }

  // Preferences Management
  async getUserPreferences(userId: string): Promise<NotificationResponse<NotificationPreferences>> {
    const response = await this.axios.get(`/notifications/preferences/${userId}`);
    return response.data;
  }

  async updateUserPreferences(userId: string, request: NotificationPreferencesUpdateRequest): Promise<NotificationResponse<NotificationPreferences>> {
    const response = await this.axios.put(`/notifications/preferences/${userId}`, request);
    return response.data;
  }

  async resetUserPreferences(userId: string): Promise<NotificationResponse<NotificationPreferences>> {
    const response = await this.axios.post(`/notifications/preferences/${userId}/reset`);
    return response.data;
  }

  // Campaign Management
  async getCampaigns(): Promise<NotificationResponse<NotificationCampaign[]>> {
    const response = await this.axios.get('/notifications/campaigns');
    return response.data;
  }

  async getCampaignById(campaignId: string): Promise<NotificationResponse<NotificationCampaign>> {
    const response = await this.axios.get(`/notifications/campaigns/${campaignId}`);
    return response.data;
  }

  async createCampaign(campaign: Partial<NotificationCampaign>): Promise<NotificationResponse<NotificationCampaign>> {
    const response = await this.axios.post('/notifications/campaigns', campaign);
    return response.data;
  }

  async updateCampaign(campaignId: string, campaign: Partial<NotificationCampaign>): Promise<NotificationResponse<NotificationCampaign>> {
    const response = await this.axios.put(`/notifications/campaigns/${campaignId}`, campaign);
    return response.data;
  }

  async deleteCampaign(campaignId: string): Promise<NotificationResponse> {
    const response = await this.axios.delete(`/notifications/campaigns/${campaignId}`);
    return response.data;
  }

  async startCampaign(campaignId: string): Promise<NotificationResponse> {
    const response = await this.axios.post(`/notifications/campaigns/${campaignId}/start`);
    return response.data;
  }

  async pauseCampaign(campaignId: string): Promise<NotificationResponse> {
    const response = await this.axios.post(`/notifications/campaigns/${campaignId}/pause`);
    return response.data;
  }

  async stopCampaign(campaignId: string): Promise<NotificationResponse> {
    const response = await this.axios.post(`/notifications/campaigns/${campaignId}/stop`);
    return response.data;
  }

  // Bulk Operations
  async getBulkNotifications(): Promise<NotificationResponse<BulkNotification[]>> {
    const response = await this.axios.get('/notifications/bulk');
    return response.data;
  }

  async getBulkNotificationById(bulkId: string): Promise<NotificationResponse<BulkNotification>> {
    const response = await this.axios.get(`/notifications/bulk/${bulkId}`);
    return response.data;
  }

  async cancelBulkNotification(bulkId: string): Promise<NotificationResponse> {
    const response = await this.axios.post(`/notifications/bulk/${bulkId}/cancel`);
    return response.data;
  }

  // Analytics
  async getNotificationAnalytics(params?: any): Promise<NotificationResponse<NotificationAnalytics>> {
    const response = await this.axios.get('/notifications/analytics', { params });
    return response.data;
  }

  async getCampaignAnalytics(campaignId: string): Promise<NotificationResponse<any>> {
    const response = await this.axios.get(`/notifications/campaigns/${campaignId}/analytics`);
    return response.data;
  }

  async getTemplateAnalytics(templateId: string): Promise<NotificationResponse<any>> {
    const response = await this.axios.get(`/notifications/templates/${templateId}/analytics`);
    return response.data;
  }

  // Device Token Management
  async registerDeviceToken(userId: string, token: string, platform: 'ios' | 'android' | 'web'): Promise<NotificationResponse> {
    const response = await this.axios.post('/notifications/device-tokens', {
      userId,
      token,
      platform
    });
    return response.data;
  }

  async unregisterDeviceToken(userId: string, token: string): Promise<NotificationResponse> {
    const response = await this.axios.delete('/notifications/device-tokens', {
      data: { userId, token }
    });
    return response.data;
  }

  // Utility Methods
  setAuthToken(token: string): void {
    this.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken(): void {
    delete this.axios.defaults.headers.common['Authorization'];
  }

  // Health Check
  async healthCheck(): Promise<any> {
    const response = await this.axios.get('/health');
    return response.data;
  }
}

export const notificationService = new NotificationService();
export default notificationService;
