/**
 * Error Handling and Logging for BeFarma Agricultural Platform
 * Centralized error management and logging for all microservice integrations
 */

export enum ErrorCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  NETWORK = 'network',
  SERVER = 'server',
  AGRICULTURAL = 'agricultural',
  FILE_UPLOAD = 'file_upload',
  PAYMENT = 'payment',
  EXTERNAL_API = 'external_api',
  UNKNOWN = 'unknown',
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface BeFarmaError {
  id: string;
  code: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context?: Record<string, any>;
  timestamp: Date;
  stack?: string;
  userFriendlyMessage?: string;
  actionRequired?: string;
  retryable?: boolean;
}

export interface ErrorContext {
  userId?: string;
  farmId?: string;
  cropId?: string;
  serviceName?: string;
  endpoint?: string;
  requestId?: string;
  userAgent?: string;
  sessionId?: string;
  stack?: string;
  componentStack?: string;
}

export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  message: string;
  timestamp: Date;
  context?: Record<string, any>;
  error?: BeFarmaError;
  service?: string;
  userId?: string;
  requestId?: string;
}

/**
 * Error codes for agricultural platform
 */
export const ERROR_CODES = {
  // Authentication errors
  AUTH_INVALID_CREDENTIALS: 'AUTH_001',
  AUTH_TOKEN_EXPIRED: 'AUTH_002',
  AUTH_TOKEN_INVALID: 'AUTH_003',
  AUTH_ACCESS_DENIED: 'AUTH_004',
  AUTH_ACCOUNT_LOCKED: 'AUTH_005',
  AUTH_VERIFICATION_REQUIRED: 'AUTH_006',

  // Validation errors
  VALIDATION_REQUIRED_FIELD: 'VAL_001',
  VALIDATION_INVALID_FORMAT: 'VAL_002',
  VALIDATION_OUT_OF_RANGE: 'VAL_003',
  VALIDATION_DUPLICATE_VALUE: 'VAL_004',
  VALIDATION_AGRICULTURAL_DATA: 'VAL_005',

  // Agricultural specific errors
  FARM_NOT_FOUND: 'FARM_001',
  FARM_ACCESS_DENIED: 'FARM_002',
  FARM_INVALID_LOCATION: 'FARM_003',
  CROP_INVALID_STAGE: 'CROP_001',
  CROP_MONITORING_FAILED: 'CROP_002',
  CROP_HEALTH_ALERT: 'CROP_003',
  PLOT_NOT_AVAILABLE: 'PLOT_001',
  PLOT_LEASE_CONFLICT: 'PLOT_002',

  // File upload errors
  FILE_TOO_LARGE: 'FILE_001',
  FILE_INVALID_TYPE: 'FILE_002',
  FILE_UPLOAD_FAILED: 'FILE_003',
  FILE_PROCESSING_ERROR: 'FILE_004',

  // Network errors
  NETWORK_TIMEOUT: 'NET_001',
  NETWORK_CONNECTION_FAILED: 'NET_002',
  NETWORK_SERVICE_UNAVAILABLE: 'NET_003',

  // Server errors
  SERVER_INTERNAL_ERROR: 'SRV_001',
  SERVER_MAINTENANCE: 'SRV_002',
  SERVER_OVERLOADED: 'SRV_003',

  // External API errors
  WEATHER_API_FAILED: 'EXT_001',
  PAYMENT_GATEWAY_ERROR: 'EXT_002',
  GPS_SERVICE_ERROR: 'EXT_003',
} as const;

/**
 * User-friendly error messages
 */
export const ERROR_MESSAGES = {
  [ERROR_CODES.AUTH_INVALID_CREDENTIALS]: 'Invalid email or password. Please check your credentials.',
  [ERROR_CODES.AUTH_TOKEN_EXPIRED]: 'Your session has expired. Please log in again.',
  [ERROR_CODES.AUTH_VERIFICATION_REQUIRED]: 'Account verification required. Please verify your account to continue.',
  
  [ERROR_CODES.FARM_NOT_FOUND]: 'Farm not found. Please check if you have access to this farm.',
  [ERROR_CODES.CROP_HEALTH_ALERT]: 'Crop health issue detected. Please check your crops immediately.',
  [ERROR_CODES.PLOT_NOT_AVAILABLE]: 'This plot is not available for lease at the moment.',
  
  [ERROR_CODES.FILE_TOO_LARGE]: 'File size is too large. Please upload a smaller file.',
  [ERROR_CODES.FILE_INVALID_TYPE]: 'Invalid file type. Please upload a supported file format.',
  
  [ERROR_CODES.NETWORK_SERVICE_UNAVAILABLE]: 'Service temporarily unavailable. Please try again later.',
  [ERROR_CODES.SERVER_MAINTENANCE]: 'System under maintenance. Please try again in a few minutes.',
} as const;

/**
 * Error handling utilities
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Create a BeFarma error
   */
  createError(
    code: string,
    message: string,
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: ErrorContext
  ): BeFarmaError {
    const error: BeFarmaError = {
      id: this.generateErrorId(),
      code,
      message,
      category,
      severity,
      context,
      timestamp: new Date(),
      userFriendlyMessage: ERROR_MESSAGES[code as keyof typeof ERROR_MESSAGES] || message,
      retryable: this.isRetryableError(code),
    };

    return error;
  }

  /**
   * Handle HTTP errors from API responses
   */
  handleHttpError(response: Response, context?: ErrorContext): BeFarmaError {
    let category = ErrorCategory.SERVER;
    let severity = ErrorSeverity.MEDIUM;
    let code = 'HTTP_ERROR';

    // Categorize by status code
    if (response.status >= 400 && response.status < 500) {
      category = ErrorCategory.VALIDATION;
      if (response.status === 401) {
        category = ErrorCategory.AUTHENTICATION;
        code = ERROR_CODES.AUTH_TOKEN_INVALID;
      } else if (response.status === 403) {
        category = ErrorCategory.AUTHORIZATION;
        code = ERROR_CODES.AUTH_ACCESS_DENIED;
      }
    } else if (response.status >= 500) {
      category = ErrorCategory.SERVER;
      severity = ErrorSeverity.HIGH;
      code = ERROR_CODES.SERVER_INTERNAL_ERROR;
    }

    const message = `HTTP ${response.status}: ${response.statusText}`;
    
    return this.createError(code, message, category, severity, {
      ...context,
      endpoint: response.url,
    });
  }

  /**
   * Handle network errors
   */
  handleNetworkError(error: Error, context?: ErrorContext): BeFarmaError {
    let code: string = ERROR_CODES.NETWORK_CONNECTION_FAILED;
    let severity = ErrorSeverity.HIGH;

    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      code = ERROR_CODES.NETWORK_TIMEOUT;
      severity = ErrorSeverity.MEDIUM;
    }

    return this.createError(
      code,
      error.message,
      ErrorCategory.NETWORK,
      severity,
      context
    );
  }

  /**
   * Handle agricultural-specific errors
   */
  handleAgriculturalError(
    errorType: 'farm' | 'crop' | 'plot',
    details: string,
    context?: ErrorContext
  ): BeFarmaError {
    let code: string;
    let severity = ErrorSeverity.MEDIUM;

    switch (errorType) {
      case 'farm':
        code = ERROR_CODES.FARM_NOT_FOUND;
        break;
      case 'crop':
        code = ERROR_CODES.CROP_MONITORING_FAILED;
        severity = ErrorSeverity.HIGH; // Crop issues are more critical
        break;
      default:
        code = 'AGR_UNKNOWN';
    }

    return this.createError(
      code,
      details,
      ErrorCategory.AGRICULTURAL,
      severity,
      context
    );
  }

  /**
   * Log an entry
   */
  log(
    level: LogEntry['level'],
    message: string,
    context?: Record<string, any>,
    error?: BeFarmaError
  ): void {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      error,
    };

    this.logs.push(logEntry);

    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console logging for development
    if (process.env['NODE_ENV'] === 'development') {
      this.consoleLog(logEntry);
    }

    // Send to external logging service in production
    if (process.env['NODE_ENV'] === 'production') {
      this.sendToLoggingService(logEntry);
    }
  }

  /**
   * Get recent logs
   */
  getLogs(
    filter?: {
      level?: LogEntry['level'];
      since?: Date;
      userId?: string;
      service?: string;
    }
  ): LogEntry[] {
    let filteredLogs = [...this.logs];

    if (filter) {
      if (filter.level) {
        filteredLogs = filteredLogs.filter(log => log.level === filter.level);
      }
      if (filter.since) {
        filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.since!);
      }
      if (filter.userId) {
        filteredLogs = filteredLogs.filter(log => log.userId === filter.userId);
      }
      if (filter.service) {
        filteredLogs = filteredLogs.filter(log => log.service === filter.service);
      }
    }

    return filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Clear logs
   */
  clearLogs(): void {
    this.logs = [];
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  private isRetryableError(code: string): boolean {
    const retryableCodes: string[] = [
      ERROR_CODES.NETWORK_TIMEOUT,
      ERROR_CODES.NETWORK_CONNECTION_FAILED,
      ERROR_CODES.SERVER_OVERLOADED,
      ERROR_CODES.WEATHER_API_FAILED,
    ];
    return retryableCodes.includes(code);
  }

  private consoleLog(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const level = entry.level.toUpperCase();
    
    const logMessage = `[${timestamp}] ${level}: ${entry.message}`;
    
    switch (entry.level) {
      case 'debug':
        console.debug(logMessage, entry.context);
        break;
      case 'info':
        console.info(logMessage, entry.context);
        break;
      case 'warn':
        console.warn(logMessage, entry.context);
        break;
      case 'error':
      case 'fatal':
        console.error(logMessage, entry.context, entry.error);
        break;
    }
  }

  private async sendToLoggingService(entry: LogEntry): Promise<void> {
    try {
      // This would typically send to a logging service like Sentry, LogRocket, etc.
      // For now, just a placeholder
      await fetch('/api/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(entry),
      });
    } catch (error) {
      // Avoid infinite loops - don't log logging errors
      console.error('Failed to send log to external service:', error);
    }
  }
}

/**
 * Global error handler functions
 */
export const errorHandler = ErrorHandler.getInstance();

export const createError = (
  code: string,
  message: string,
  category: ErrorCategory,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  context?: ErrorContext
): BeFarmaError => {
  return errorHandler.createError(code, message, category, severity, context);
};

export const handleHttpError = (response: Response, context?: ErrorContext): BeFarmaError => {
  return errorHandler.handleHttpError(response, context);
};

export const handleNetworkError = (error: Error, context?: ErrorContext): BeFarmaError => {
  return errorHandler.handleNetworkError(error, context);
};

export const handleAgriculturalError = (
  errorType: 'farm' | 'crop' | 'plot',
  details: string,
  context?: ErrorContext
): BeFarmaError => {
  return errorHandler.handleAgriculturalError(errorType, details, context);
};

export const logError = (error: BeFarmaError, context?: Record<string, any>): void => {
  errorHandler.log('error', error.message, context, error);
};

export const logInfo = (message: string, context?: Record<string, any>): void => {
  errorHandler.log('info', message, context);
};

export const logWarning = (message: string, context?: Record<string, any>): void => {
  errorHandler.log('warn', message, context);
};

export const logDebug = (message: string, context?: Record<string, any>): void => {
  errorHandler.log('debug', message, context);
};

/**
 * Error boundary for React components
 */
export class AgriculturalErrorBoundary {
  static handleError(error: Error, errorInfo: any): BeFarmaError {
    const beFarmaError = createError(
      'COMPONENT_ERROR',
      error.message,
      ErrorCategory.UNKNOWN,
      ErrorSeverity.HIGH,
      {
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      }
    );

    logError(beFarmaError);
    return beFarmaError;
  }
}

export default {
  ErrorHandler,
  errorHandler,
  createError,
  handleHttpError,
  handleNetworkError,
  handleAgriculturalError,
  logError,
  logInfo,
  logWarning,
  logDebug,
  AgriculturalErrorBoundary,
  ERROR_CODES,
  ERROR_MESSAGES,
  ErrorCategory,
  ErrorSeverity,
}; 