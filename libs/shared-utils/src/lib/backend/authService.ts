import axios from 'axios';
import { User } from '../redux/slices/authSlice';
import { sellerServiceClient } from '../axios/instance';

// Types
export interface LoginCredentials {
  email: string;
  password: string;
  isAdmin?: boolean;
}

export interface AuthSellerLoginResponse {
  status: string;
  data: {
    token: string;
    seller: {
      sellerId: string;
      name: string;
      email: string;
      contact?: string;
      verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
      address?: {
        addressLine1: string;
        state: string;
        city: string;
        pincode: string;
      };
    };
  };
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}

export interface RegisterData {
  name: string;
  email: string;
  contact: string;
  address: {
    addressLine1: string;
    state: string;
    city: string;
    pincode: string;
  };
  password: string;
}

export interface RegisterResponse {
  status: string;
  data: {
    sellerId: string;
    name: string;
    email: string;
    verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  };
}

// Auth service class using seller service client for authentication
export class AuthService {
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Use seller service client for authentication
      const response = await sellerServiceClient.post<AuthSellerLoginResponse>('/login', {
        email: credentials.email,
        password: credentials.password,
      });

      // Transform seller response to match expected LoginResponse format
      const responseData = response.data;

      if (responseData.status === 'success' && responseData.data) {
        const sellerData = responseData.data;
        return {
          user: {
            id: sellerData.seller.sellerId,
            email: sellerData.seller.email,
            name: sellerData.seller.name,
            role: 'seller' as const,
            farmerId: sellerData.seller.sellerId,
            verificationStatus: sellerData.seller.verificationStatus.toLowerCase() as 'pending' | 'verified' | 'rejected',
            profileComplete: !!sellerData.seller.contact && !!sellerData.seller.address,
            phone: sellerData.seller.contact,
            address: sellerData.seller.address ? {
              street: sellerData.seller.address.addressLine1,
              city: sellerData.seller.address.city,
              state: sellerData.seller.address.state,
              zipCode: sellerData.seller.address.pincode,
              country: 'India', // Default for now
            } : undefined,
          },
          token: sellerData.token,
          refreshToken: sellerData.token, // Using same token for now, should be separate in production
          expiresIn: 3600, // 1 hour
        };
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Login failed. Please try again.';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  static async register(data: RegisterData): Promise<RegisterResponse> {
    try {
      const response = await sellerServiceClient.post<RegisterResponse>('/register', data);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Registration failed. Please try again.';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  static async requestPasswordReset(email: string): Promise<void> {
    try {
      await sellerServiceClient.post('/forgot-password', { email });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to send password reset email';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  static async resetPassword(token: string, password: string): Promise<void> {
    try {
      await sellerServiceClient.post('/reset-password', { token, password });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Password reset failed';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  static async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      const response = await sellerServiceClient.post<RefreshTokenResponse>('/refresh-token', {
        refreshToken
      });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.error?.message || 'Token refresh failed');
      }
      throw error;
    }
  }

  static async logout(refreshToken: string): Promise<void> {
    try {
      await sellerServiceClient.post('/logout', { refreshToken });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Logout error:', error.response?.data);
      }
      // We don't throw on logout errors, just log them
    }
  }

  static async verifyEmail(token: string): Promise<void> {
    try {
      await sellerServiceClient.post('/verify-email', { token });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.message || 'Email verification failed');
      }
      throw error;
    }
  }
}

export default AuthService; 