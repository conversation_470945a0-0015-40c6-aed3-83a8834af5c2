import axios from 'axios';
import { sellerServiceClient } from '../axios/instance';

// Profile-related interfaces
export interface Address {
  addressLine1: string;
  state: string;
  city: string;
  pincode: string;
}

export interface BankDetails {
  accountNumber: string;
  bankName: string;
  ifscCode: string;
}

export interface SellerProfile {
  sellerId: string;
  name: string;
  email: string;
  contact: string;
  address: Address;
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  kycStatus?: 'PENDING' | 'VERIFIED' | 'REJECTED';
  bankDetails?: BankDetails;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateProfileData {
  name?: string;
  contact?: string;
  address?: Address;
}

export interface DashboardStats {
  totalFarms: number;
  totalCrops: number;
  totalOrders: number;
  totalRevenue: number;
}

export interface RecentOrder {
  orderId: string;
  buyerName: string;
  amount: number;
  status: string;
  createdAt: string;
}

export interface UpcomingHarvest {
  cropId: string;
  cropName: string;
  expectedHarvestDate: string;
  expectedYield: number;
}

export interface SellerDashboard {
  seller: {
    sellerId: string;
    name: string;
    verificationStatus: string;
    kycStatus: string;
  };
  stats: DashboardStats;
  recentOrders: RecentOrder[];
  upcomingHarvests: UpcomingHarvest[];
}

export interface ApiResponse<T> {
  status: string;
  data: T;
}

export class ProfileService {
  /**
   * ✅ CONFIRMED: Get seller profile by ID
   * Uses sellerServiceClient which routes through API Gateway: GET /api/v1/sellers/{sellerId}
   */
  static async getProfile(sellerId: string): Promise<SellerProfile> {
    try {
      const response = await sellerServiceClient.get<ApiResponse<SellerProfile>>(
        `/sellers/${sellerId}`
      );
      return response.data.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to fetch profile';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  /**
   * ✅ CONFIRMED: Update seller profile
   * Uses sellerServiceClient which routes through API Gateway: PUT /api/v1/sellers/{sellerId}
   */
  static async updateProfile(sellerId: string, data: UpdateProfileData): Promise<SellerProfile> {
    try {
      const response = await sellerServiceClient.put<ApiResponse<SellerProfile>>(
        `/sellers/${sellerId}`,
        data
      );
      return response.data.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to update profile';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  /**
   * Update bank details
   * Note: This endpoint pattern doesn't match API Gateway routes.
   * Based on gateway docs, this should be a PUT to /api/v1/sellers/{sellerId} with bank details in body
   * or a separate endpoint like /api/v1/sellers/{sellerId}/bank-details
   */
  static async updateBankDetails(sellerId: string, bankDetails: BankDetails): Promise<{ sellerId: string; bankDetails: BankDetails }> {
    try {
      // Using the seller profile update endpoint with bank details
      const response = await sellerServiceClient.put<ApiResponse<{ sellerId: string; bankDetails: BankDetails }>>(
        `/sellers/${sellerId}/bank-details`,
        bankDetails
      );
      return response.data.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to update bank details';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  /**
   * ✅ CONFIRMED: Get seller dashboard data
   * Uses sellerServiceClient which routes through API Gateway: GET /api/v1/sellers/dashboard/{sellerId}
   */
  static async getDashboard(sellerId: string): Promise<SellerDashboard> {
    try {
      const response = await sellerServiceClient.get<ApiResponse<SellerDashboard>>(
        `/sellers/dashboard/${sellerId}`
      );
      return response.data.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to fetch dashboard data';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  /**
   * Get current user profile (using seller ID from auth state)
   * Note: Since there's no /sellers/me endpoint in the API docs,
   * this method requires the sellerId to be passed from the calling component
   */
  static async getCurrentProfile(sellerId: string): Promise<SellerProfile> {
    return this.getProfile(sellerId);
  }

  /**
   * Upload documents for seller
   * API Gateway Route: POST /api/v1/sellers/{sellerId}/documents
   */
  static async uploadDocuments(sellerId: string, documents: {
    identityProof?: string;
    landOwnership?: string;
    certifications?: string[];
  }): Promise<{ sellerId: string; documentStatus: string }> {
    try {
      const response = await sellerServiceClient.post<ApiResponse<{ sellerId: string; documentStatus: string }>>(
        `/sellers/${sellerId}/documents`,
        documents
      );
      return response.data.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to upload documents';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  /**
   * Upload profile picture
   * Note: This endpoint is not documented in the API docs.
   * This is a placeholder for future implementation.
   */
  static async uploadProfilePicture(sellerId: string, file: File): Promise<{ profilePictureUrl: string }> {
    try {
      const formData = new FormData();
      formData.append('profilePicture', file);

      // This endpoint doesn't exist in the API docs - placeholder for future implementation
      const response = await sellerServiceClient.post<ApiResponse<{ profilePictureUrl: string }>>(
        `/sellers/${sellerId}/profile-picture`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to upload profile picture';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }

  /**
   * Delete seller account
   * API Gateway Route: DELETE /api/v1/sellers/{sellerId}
   */
  static async deleteAccount(sellerId: string): Promise<void> {
    try {
      await sellerServiceClient.delete(`/sellers/${sellerId}`);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.error?.message ||
                           error.response?.data?.message ||
                           'Failed to delete account';
        throw new Error(errorMessage);
      }
      throw error;
    }
  }
}

export default ProfileService;
