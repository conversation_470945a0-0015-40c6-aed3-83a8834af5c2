/**
 * Orders Slice for BeFarma Agricultural Platform
 * Handles order management and tracking
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Order {
  id: string;
  farmId: string;
  cropId: string;
  quantity: number;
  status: 'pending' | 'confirmed' | 'processing' | 'completed';
  totalAmount: number;
  orderDate: string;
}

export interface OrdersState {
  orders: Order[];
  selectedOrder: Order | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: OrdersState = {
  orders: [],
  selectedOrder: null,
  isLoading: false,
  error: null,
};

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setOrders: (state, action: PayloadAction<Order[]>) => {
      state.orders = action.payload;
    },
    addOrder: (state, action: PayloadAction<Order>) => {
      state.orders.push(action.payload);
    },
    updateOrder: (state, action: PayloadAction<{ id: string; updates: Partial<Order> }>) => {
      const { id, updates } = action.payload;
      const orderIndex = state.orders.findIndex(order => order.id === id);
      if (orderIndex !== -1) {
        state.orders[orderIndex] = { ...state.orders[orderIndex], ...updates };
      }
    },
    setSelectedOrder: (state, action: PayloadAction<Order | null>) => {
      state.selectedOrder = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const { setOrders, addOrder, updateOrder, setSelectedOrder, clearError } = ordersSlice.actions;

export const selectAllOrders = (state: { orders: OrdersState }) => state.orders.orders;
export const selectSelectedOrder = (state: { orders: OrdersState }) => state.orders.selectedOrder;

export default ordersSlice.reducer; 