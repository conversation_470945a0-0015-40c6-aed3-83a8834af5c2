/**
 * Crops Slice for BeFarma Agricultural Platform
 * Handles crop planning, monitoring, and management
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Types for agricultural crops
export interface Crop {
  id: string;
  name: string;
  type: string;
  farmId: string;
  numberOfPlots: number;
  plantingDate: string;
  expectedHarvestDate: string;
  growthStage: 'planting' | 'growing' | 'maturing' | 'ready';
  healthStatus: 'healthy' | 'warning' | 'critical';
  yieldExpected: number;
  yieldActual?: number;
}

export interface CropsState {
  crops: Crop[];
  selectedCrop: Crop | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: CropsState = {
  crops: [],
  selectedCrop: null,
  isLoading: false,
  error: null,
};

const cropsSlice = createSlice({
  name: 'crops',
  initialState,
  reducers: {
    setCrops: (state, action: PayloadAction<Crop[]>) => {
      state.crops = action.payload;
    },
    addCrop: (state, action: PayloadAction<Crop>) => {
      state.crops.push(action.payload);
    },
    updateCrop: (state, action: PayloadAction<{ id: string; updates: Partial<Crop> }>) => {
      const { id, updates } = action.payload;
      const cropIndex = state.crops.findIndex(crop => crop.id === id);
      if (cropIndex !== -1) {
        state.crops[cropIndex] = { ...state.crops[cropIndex], ...updates };
      }
    },
    setSelectedCrop: (state, action: PayloadAction<Crop | null>) => {
      state.selectedCrop = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const { setCrops, addCrop, updateCrop, setSelectedCrop, clearError } = cropsSlice.actions;

// Selectors
export const selectAllCrops = (state: { crops: CropsState }) => state.crops.crops;
export const selectSelectedCrop = (state: { crops: CropsState }) => state.crops.selectedCrop;
export const selectCropsLoading = (state: { crops: CropsState }) => state.crops.isLoading;

export default cropsSlice.reducer; 