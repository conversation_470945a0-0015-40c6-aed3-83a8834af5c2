/**
 * UI Slice for BeFarma Agricultural Platform
 * Handles UI state and user preferences
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  loading: {
    global: boolean;
    farms: boolean;
    crops: boolean;
    orders: boolean;
  };
  modals: {
    createFarm: boolean;
    createCrop: boolean;
    confirmDelete: boolean;
  };
  preferences: {
    language: 'en' | 'es' | 'hi';
    units: 'metric' | 'imperial';
    dateFormat: 'dd/mm/yyyy' | 'mm/dd/yyyy';
  };
}

const initialState: UIState = {
  theme: 'light',
  sidebarOpen: true,
  loading: {
    global: false,
    farms: false,
    crops: false,
    orders: false,
  },
  modals: {
    createFarm: false,
    createCrop: false,
    confirmDelete: false,
  },
  preferences: {
    language: 'en',
    units: 'metric',
    dateFormat: 'dd/mm/yyyy',
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    setLoading: (state, action: PayloadAction<{ key: keyof UIState['loading']; value: boolean }>) => {
      const { key, value } = action.payload;
      state.loading[key] = value;
    },
    openModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = true;
    },
    closeModal: (state, action: PayloadAction<keyof UIState['modals']>) => {
      state.modals[action.payload] = false;
    },
    updatePreferences: (state, action: PayloadAction<Partial<UIState['preferences']>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
  },
});

export const {
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  setLoading,
  openModal,
  closeModal,
  updatePreferences,
} = uiSlice.actions;

// Selectors
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen;
export const selectLoading = (state: { ui: UIState }) => state.ui.loading;
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectPreferences = (state: { ui: UIState }) => state.ui.preferences;

export default uiSlice.reducer; 