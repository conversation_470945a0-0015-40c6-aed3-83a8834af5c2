/**
 * Authentication Slice for BeFarma Agricultural Platform
 * Handles seller/admin authentication, JWT tokens, and role-based access
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AuthService } from '../../backend/authService';
import { JWTManager } from '../../auth/jwt';

// Types for agricultural platform users
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'seller' | 'admin';
  farmerId?: string; // For sellers
  verificationStatus: 'pending' | 'verified' | 'rejected';
  profileComplete: boolean;
  permissions: string[];
  avatar?: string;
  phone?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  loginAttempts: number;
  lastLoginAt: string | null;
  tokenExpiry: number | null;
}

// Initial state for authentication
const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isLoading: false,
  isAuthenticated: false,
  error: null,
  loginAttempts: 0,
  lastLoginAt: null,
  tokenExpiry: null,
};

// Async thunks for authentication actions
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await AuthService.login(credentials);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Login failed');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const refreshToken = state.auth.refreshToken;
      
      if (refreshToken) {
        await AuthService.logout(refreshToken);
      }
      return true;
    } catch {
      return rejectWithValue('Logout failed');
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const refreshTokenValue = state.auth.refreshToken;

      if (!refreshTokenValue) {
        throw new Error('No refresh token available');
      }

      const response = await AuthService.refreshToken(refreshTokenValue);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Token refresh failed');
    }
  }
);

// Initialize auth state from localStorage
export const initializeAuth = createAsyncThunk(
  'auth/initialize',
  async (_, { rejectWithValue }) => {
    try {
      // Check if user is authenticated via JWTManager
      if (JWTManager.isAuthenticated()) {
        const tokens = JWTManager.getTokens();
        const user = JWTManager.getCurrentUser();

        if (tokens && user) {
          // Transform JWT user data to match our User interface
          const transformedUser: User = {
            id: user.userId,
            email: user.email,
            name: `${user.firstName} ${user.lastName}`,
            role: user.role === 'farmer' ? 'seller' : user.role, // Map farmer to seller
            farmerId: user.farmId,
            verificationStatus: user.isVerified ? 'verified' : 'pending',
            profileComplete: true, // Assume complete if they have a token
            permissions: user.permissions,
          };

          return {
            user: transformedUser,
            token: tokens.token,
            refreshToken: tokens.refreshToken,
          };
        }
      }

      return rejectWithValue('No valid authentication found');
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Auth initialization failed');
    }
  }
);

// Authentication slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    
    setCredentials: (state, action: PayloadAction<{ user: User; token: string; refreshToken: string }>) => {
      const { user, token, refreshToken } = action.payload;
      state.user = user;
      state.token = token;
      state.refreshToken = refreshToken;
      state.isAuthenticated = true;
      state.error = null;
      state.lastLoginAt = new Date().toISOString();

      // Calculate token expiry (assuming 1 hour from environment config)
      state.tokenExpiry = Date.now() + (3600000); // 1 hour

      // Store tokens in localStorage via JWTManager
      JWTManager.setTokens({ token, refreshToken });
    },
    
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
    
    clearCredentials: (state) => {
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
      state.tokenExpiry = null;

      // Clear tokens from localStorage
      JWTManager.clearTokens();
    },
    
    incrementLoginAttempts: (state) => {
      state.loginAttempts += 1;
    },
    
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
    },
  },
  
  extraReducers: (builder) => {
    // Login user
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const { user, token, refreshToken } = action.payload;
        state.user = user;
        state.token = token;
        state.refreshToken = refreshToken;
        state.isAuthenticated = true;
        state.loginAttempts = 0;
        state.lastLoginAt = new Date().toISOString();
        state.tokenExpiry = Date.now() + (3600000); // 1 hour

        // Store tokens in localStorage via JWTManager
        JWTManager.setTokens({ token, refreshToken });
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.loginAttempts += 1;
      });
    
    // Logout user
    builder
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
        state.tokenExpiry = null;
        state.loginAttempts = 0;

        // Clear tokens from localStorage
        JWTManager.clearTokens();
      });
    
    // Refresh token
    builder
      .addCase(refreshToken.fulfilled, (state, action) => {
        const { token, refreshToken: newRefreshToken } = action.payload;
        state.token = token;
        state.refreshToken = newRefreshToken;
        state.tokenExpiry = Date.now() + (3600000); // 1 hour
      })
      .addCase(refreshToken.rejected, (state) => {
        // If refresh fails, clear credentials
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
        state.tokenExpiry = null;
      });

    // Initialize auth
    builder
      .addCase(initializeAuth.fulfilled, (state, action) => {
        const { user, token, refreshToken } = action.payload;
        state.user = user;
        state.token = token;
        state.refreshToken = refreshToken;
        state.isAuthenticated = true;
        state.tokenExpiry = Date.now() + (3600000); // 1 hour
        state.isLoading = false;
      })
      .addCase(initializeAuth.rejected, (state) => {
        state.isLoading = false;
        // Don't set error for initialization failure, just remain unauthenticated
      });
  },
});

// Export actions
export const {
  clearError,
  setCredentials,
  updateUser,
  clearCredentials,
  incrementLoginAttempts,
  resetLoginAttempts,
} = authSlice.actions;



// Selectors
export const selectCurrentUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.isLoading;
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error;
export const selectUserRole = (state: { auth: AuthState }) => state.auth.user?.role;
export const selectIsVerified = (state: { auth: AuthState }) => 
  state.auth.user?.verificationStatus === 'verified';

export default authSlice.reducer; 