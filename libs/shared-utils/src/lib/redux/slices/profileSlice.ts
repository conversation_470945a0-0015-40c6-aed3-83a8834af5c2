import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ProfileService, SellerProfile, UpdateProfileData, BankDetails, SellerDashboard } from '../../backend/profileService';

// Profile state interface
export interface ProfileState {
  profile: SellerProfile | null;
  dashboard: SellerDashboard | null;
  isLoading: boolean;
  isUpdating: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// Initial state
const initialState: ProfileState = {
  profile: null,
  dashboard: null,
  isLoading: false,
  isUpdating: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchProfile = createAsyncThunk(
  'profile/fetchProfile',
  async (sellerId: string, { rejectWithValue }) => {
    try {
      const profile = await ProfileService.getProfile(sellerId);
      return profile;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch profile');
    }
  }
);

export const fetchCurrentProfile = createAsyncThunk(
  'profile/fetchCurrentProfile',
  async (sellerId: string, { rejectWithValue }) => {
    try {
      const profile = await ProfileService.getCurrentProfile(sellerId);
      return profile;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch current profile');
    }
  }
);

export const updateProfile = createAsyncThunk(
  'profile/updateProfile',
  async ({ sellerId, data }: { sellerId: string; data: UpdateProfileData }, { rejectWithValue }) => {
    try {
      const updatedProfile = await ProfileService.updateProfile(sellerId, data);
      return updatedProfile;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update profile');
    }
  }
);

export const updateBankDetails = createAsyncThunk(
  'profile/updateBankDetails',
  async ({ sellerId, bankDetails }: { sellerId: string; bankDetails: BankDetails }, { rejectWithValue }) => {
    try {
      const result = await ProfileService.updateBankDetails(sellerId, bankDetails);
      return result;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update bank details');
    }
  }
);

export const fetchDashboard = createAsyncThunk(
  'profile/fetchDashboard',
  async (sellerId: string, { rejectWithValue }) => {
    try {
      const dashboard = await ProfileService.getDashboard(sellerId);
      return dashboard;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch dashboard data');
    }
  }
);

export const uploadProfilePicture = createAsyncThunk(
  'profile/uploadProfilePicture',
  async ({ sellerId, file }: { sellerId: string; file: File }, { rejectWithValue }) => {
    try {
      const result = await ProfileService.uploadProfilePicture(sellerId, file);
      return result;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to upload profile picture');
    }
  }
);

// Profile slice
const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearProfile: (state) => {
      state.profile = null;
      state.dashboard = null;
      state.error = null;
      state.lastUpdated = null;
    },
    updateProfileField: (state, action: PayloadAction<{ field: keyof SellerProfile; value: any }>) => {
      if (state.profile) {
        (state.profile as any)[action.payload.field] = action.payload.value;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch profile
    builder
      .addCase(fetchProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch current profile
    builder
      .addCase(fetchCurrentProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCurrentProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchCurrentProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update profile
    builder
      .addCase(updateProfile.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.profile = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Update bank details
    builder
      .addCase(updateBankDetails.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateBankDetails.fulfilled, (state, action) => {
        state.isUpdating = false;
        if (state.profile) {
          state.profile.bankDetails = action.payload.bankDetails;
        }
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(updateBankDetails.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Fetch dashboard
    builder
      .addCase(fetchDashboard.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.dashboard = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Upload profile picture
    builder
      .addCase(uploadProfilePicture.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(uploadProfilePicture.fulfilled, (state, action) => {
        state.isUpdating = false;
        if (state.profile) {
          (state.profile as any).profilePictureUrl = action.payload.profilePictureUrl;
        }
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(uploadProfilePicture.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { clearError, clearProfile, updateProfileField } = profileSlice.actions;

// Selectors
export const selectProfile = (state: { profile: ProfileState }) => state.profile.profile;
export const selectDashboard = (state: { profile: ProfileState }) => state.profile.dashboard;
export const selectProfileLoading = (state: { profile: ProfileState }) => state.profile.isLoading;
export const selectProfileUpdating = (state: { profile: ProfileState }) => state.profile.isUpdating;
export const selectProfileError = (state: { profile: ProfileState }) => state.profile.error;
export const selectLastUpdated = (state: { profile: ProfileState }) => state.profile.lastUpdated;

// Export reducer
export default profileSlice.reducer;
