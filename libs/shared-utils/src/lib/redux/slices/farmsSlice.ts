/**
 * Farms Slice for BeFarma Agricultural Platform
 * Handles farm registration, management, and monitoring
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types for agricultural farms - Updated to match actual API response
export interface Farm {
  _id: string;
  farmId: string;
  name: string;
  sellerId: string;
  location: {
    coordinates: {
      latitude: number;
      longitude: number;
    };
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2?: string;
  };
  totalArea: number; // in acres
  soilType: string; // e.g., "Alluvial", "Black Cotton"
  waterSource: string; // e.g., "Canal", "Deep Bore Well"
  infrastructure: string[];
  certifications: string[];
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING';
  plots: any[];
  currentCrops: Array<{
    cropId: string;
    numberOfPlots: number;
    name: string;
    variety: string;
    plantingDate: string;
    expectedHarvestDate: string;
    growthStage: 'PLANTED' | 'GROWING' | 'HARVESTED';
    season: 'KHARIF' | 'RABI' | 'ZAID';
    _id: string;
  }>;
  cropRotationPlan: Array<{
    numberOfPlots: number;
    season: 'KHARIF' | 'RABI' | 'ZAID';
    year: number;
    plannedCrop: string;
    variety: string;
    status: 'PLANTED' | 'PLANNED';
    _id: string;
  }>;
  farmingPractices: {
    primaryMethod: 'INTEGRATED' | 'CONVENTIONAL' | 'ORGANIC';
    irrigationSystems: string[];
    sustainabilityScore: number;
  };
  createdAt: string;
  updatedAt: string;
  __v: number;

  // Computed properties for backward compatibility
  id?: string;
  size?: number;
  organicCertified?: boolean;
  verificationStatus?: 'pending' | 'verified' | 'rejected';
  registrationDate?: string;
  lastUpdated?: string;
  isActive?: boolean;
  crops?: any[];
}

export interface FarmsState {
  farms: Farm[];
  selectedFarm: Farm | null;
  isLoading: boolean;
  error: string | null;
  totalFarms: number;
  activeFarms: number;
  verifiedFarms: number;
  totalArea: number; // Total area across all farms
  organicFarms: number; // Number of organic certified farms
}

// Initial state
const initialState: FarmsState = {
  farms: [],
  selectedFarm: null,
  isLoading: false,
  error: null,
  totalFarms: 0,
  activeFarms: 0,
  verifiedFarms: 0,
  totalArea: 0,
  organicFarms: 0,
};

// Async thunks for farm operations
export const createFarm = createAsyncThunk(
  'farms/createFarm',
  async (farmData: Omit<Farm, 'id' | 'registrationDate' | 'lastUpdated'>, { rejectWithValue }) => {
    try {
      // This will be implemented with Farm Service (Port 3005) in Phase 3.3
      const response = await fetch('/api/farms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(farmData),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create farm');
      }
      
      const farm = await response.json() as Farm;
      return farm;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create farm');
    }
  }
);

export const fetchFarms = createAsyncThunk(
  'farms/fetchFarms',
  async (params: { sellerId?: string } = {}, { rejectWithValue }) => {
    try {
      const { sellerId } = params;
      const url = sellerId ? `/api/farms?sellerId=${sellerId}` : '/api/farms';
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch farms');
      }
      
      const farms = await response.json() as Farm[];
      return farms;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch farms');
    }
  }
);

export const updateFarm = createAsyncThunk(
  'farms/updateFarm',
  async ({ id, updates }: { id: string; updates: Partial<Farm> }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/farms/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update farm');
      }
      
      const farm = await response.json() as Farm;
      return farm;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update farm');
    }
  }
);

export const deleteFarm = createAsyncThunk(
  'farms/deleteFarm',
  async (farmId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/farms/${farmId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete farm');
      }
      
      return farmId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete farm');
    }
  }
);

// Farms slice
const farmsSlice = createSlice({
  name: 'farms',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    
    setSelectedFarm: (state, action: PayloadAction<Farm | null>) => {
      state.selectedFarm = action.payload;
    },
    
    updateFarmLocally: (state, action: PayloadAction<{ id: string; updates: Partial<Farm> }>) => {
      const { id, updates } = action.payload;
      const farmIndex = state.farms.findIndex(farm => farm.id === id);
      
      if (farmIndex !== -1) {
        state.farms[farmIndex] = { ...state.farms[farmIndex], ...updates };
        
        // Update selected farm if it's the same one
        if (state.selectedFarm?.id === id) {
          state.selectedFarm = { ...state.selectedFarm, ...updates };
        }
      }
    },
    
    clearFarms: (state) => {
      state.farms = [];
      state.selectedFarm = null;
      state.totalFarms = 0;
      state.activeFarms = 0;
      state.verifiedFarms = 0;
      state.totalArea = 0;
      state.organicFarms = 0;
    },

    updateFarmStats: (state) => {
      state.totalFarms = state.farms.length;
      state.activeFarms = state.farms.filter(farm => farm.isActive).length;
      state.verifiedFarms = state.farms.filter(farm => farm.verificationStatus === 'verified').length;
      state.totalArea = state.farms.reduce((sum, farm) => sum + farm.totalArea || 0 , 0);
      state.organicFarms = state.farms.filter(farm => farm.organicCertified).length;
    },
  },
  
  extraReducers: (builder) => {
    // Create farm
    builder
      .addCase(createFarm.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createFarm.fulfilled, (state, action) => {
        state.isLoading = false;
        state.farms.push(action.payload);
        farmsSlice.caseReducers.updateFarmStats(state);
      })
      .addCase(createFarm.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // Fetch farms
    builder
      .addCase(fetchFarms.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFarms.fulfilled, (state, action) => {
        state.isLoading = false;
        state.farms = action.payload;
        farmsSlice.caseReducers.updateFarmStats(state);
      })
      .addCase(fetchFarms.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // Update farm
    builder
      .addCase(updateFarm.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateFarm.fulfilled, (state, action) => {
        state.isLoading = false;
        const updatedFarm = action.payload;
        const farmIndex = state.farms.findIndex(farm => farm.id === updatedFarm.id);
        
        if (farmIndex !== -1) {
          state.farms[farmIndex] = updatedFarm;
          
          if (state.selectedFarm?.id === updatedFarm.id) {
            state.selectedFarm = updatedFarm;
          }
        }
        
        farmsSlice.caseReducers.updateFarmStats(state);
      })
      .addCase(updateFarm.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // Delete farm
    builder
      .addCase(deleteFarm.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteFarm.fulfilled, (state, action) => {
        state.isLoading = false;
        const deletedFarmId = action.payload;
        state.farms = state.farms.filter(farm => farm.id !== deletedFarmId);
        
        if (state.selectedFarm?.id === deletedFarmId) {
          state.selectedFarm = null;
        }
        
        farmsSlice.caseReducers.updateFarmStats(state);
      })
      .addCase(deleteFarm.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  clearError,
  setSelectedFarm,
  updateFarmLocally,
  clearFarms,
  updateFarmStats,
} = farmsSlice.actions;

// Selectors
export const selectAllFarms = (state: { farms: FarmsState }) => state.farms.farms;
export const selectSelectedFarm = (state: { farms: FarmsState }) => state.farms.selectedFarm;
export const selectFarmsLoading = (state: { farms: FarmsState }) => state.farms.isLoading;
export const selectFarmsError = (state: { farms: FarmsState }) => state.farms.error;
export const selectFarmStats = (state: { farms: FarmsState }) => ({
  total: state.farms.totalFarms,
  active: state.farms.activeFarms,
  verified: state.farms.verifiedFarms,
  totalArea: state.farms.totalArea,
  organic: state.farms.organicFarms,
});
export const selectVerifiedFarms = (state: { farms: FarmsState }) =>
  state.farms.farms.filter(farm => farm.verificationStatus === 'verified');
export const selectActiveFarms = (state: { farms: FarmsState }) =>
  state.farms.farms.filter(farm => farm.isActive);
export const selectOrganicFarms = (state: { farms: FarmsState }) =>
  state.farms.farms.filter(farm => farm.organicCertified);

export default farmsSlice.reducer; 