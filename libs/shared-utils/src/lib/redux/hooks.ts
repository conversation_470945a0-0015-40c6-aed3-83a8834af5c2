/**
 * Typed Redux Hooks for BeFarma Agricultural Platform
 * Pre-typed versions of useDispatch and useSelector
 */

import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from './store';

// Typed version of useDispatch hook
export const useAppDispatch = () => useDispatch<AppDispatch>();

// Typed version of useSelector hook
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector; 