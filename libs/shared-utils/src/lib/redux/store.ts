/**
 * BeFarma Redux Store Configuration
 * Agricultural Platform State Management
 */

import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

// Import feature slices
import authSlice from './slices/authSlice';
import profileSlice from './slices/profileSlice';
import farmsSlice from './slices/farmsSlice';
import cropsSlice from './slices/cropsSlice';
import ordersSlice from './slices/ordersSlice';
import notificationsSlice from './slices/notificationsSlice';
import uiSlice from './slices/uiSlice';

// Import API slices (RTK Query)
import { apiSlice } from './api/apiSlice';

// Redux persist configuration
const persistConfig = {
  key: 'befarma-root',
  storage,
  whitelist: ['auth', 'profile', 'ui'], // Only persist auth, profile, and UI preferences
  blacklist: ['api'], // Don't persist API cache
};

// Root reducer combining all feature slices
const rootReducer = combineReducers({
  // Feature slices
  auth: authSlice,
  profile: profileSlice,
  farms: farmsSlice,
  crops: cropsSlice,
  orders: ordersSlice,
  notifications: notificationsSlice,
  ui: uiSlice,

  // API slice for RTK Query
  [apiSlice.reducerPath]: apiSlice.reducer,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store with agricultural platform middleware
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    })
    .concat(apiSlice.middleware),
  devTools: process.env['NODE_ENV'] !== 'production',
});

// Setup listeners for RTK Query
setupListeners(store.dispatch);

// Persistor for redux-persist
export const persistor = persistStore(store);

// Export types for TypeScript
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
