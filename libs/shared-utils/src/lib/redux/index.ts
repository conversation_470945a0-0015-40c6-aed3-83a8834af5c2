/**
 * Redux Module Exports for BeFarma Agricultural Platform
 * Centralized exports for store, slices, hooks, and API
 */

// Store and types
export { store, persistor } from './store';
export type { RootState, AppDispatch } from './store';

// Typed hooks
export { useAppDispatch, useAppSelector } from './hooks';

// RTK Query API
export { apiSlice } from './api/apiSlice';
export * from './api/apiSlice';

// Feature slice reducers (default exports)
export { default as authSlice } from './slices/authSlice';
export { default as farmsSlice } from './slices/farmsSlice';
export { default as cropsSlice } from './slices/cropsSlice';
export { default as ordersSlice } from './slices/ordersSlice';
export { default as notificationsSlice } from './slices/notificationsSlice';
export { default as uiSlice } from './slices/uiSlice';

// Auth slice exports
export type { User, AuthState } from './slices/authSlice';
export {
  loginUser,
  logoutUser,
  refreshToken,
  initializeAuth,
  setCredentials,
  updateUser,
  clearCredentials,
  clearError,
  selectCurrentUser,
  selectIsAuthenticated,
  selectAuthLoading,
  selectAuthError,
  selectUserRole,
  selectIsVerified,
} from './slices/authSlice';

// Profile exports
export {
  fetchProfile,
  fetchCurrentProfile,
  updateProfile,
  updateBankDetails,
  fetchDashboard,
  uploadProfilePicture,
  clearProfile,
  updateProfileField,
  selectProfile,
  selectDashboard,
  selectProfileLoading,
  selectProfileUpdating,
  selectProfileError,
  selectLastUpdated,
} from './slices/profileSlice';

// Farms slice exports
export type { Farm, FarmsState } from './slices/farmsSlice';
export {
  createFarm,
  fetchFarms,
  updateFarm,
  deleteFarm,
  setSelectedFarm,
  selectAllFarms,
  selectSelectedFarm,
  selectFarmsLoading,
  selectFarmsError,
  selectFarmStats,
  selectVerifiedFarms,
  selectActiveFarms,
} from './slices/farmsSlice';

// UI slice exports
export type { UIState } from './slices/uiSlice';
export {
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  setLoading,
  openModal,
  closeModal,
  updatePreferences,
  selectTheme,
  selectSidebarOpen,
  selectLoading,
  selectModals,
  selectPreferences,
} from './slices/uiSlice'; 