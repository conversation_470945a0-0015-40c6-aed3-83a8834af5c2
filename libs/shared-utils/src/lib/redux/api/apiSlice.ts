/**
 * RTK Query API Slice for BeFarma Agricultural Platform
 * Centralized API configuration for all microservices
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { environment } from '../../environment/environment.config';

// Base query with authentication - using seller service directly
const baseQuery = fetchBaseQuery({
  baseUrl: environment.services.sellerService,
  prepareHeaders: (headers, { getState }) => {
    // Add auth token from state
    const token = (getState() as any)?.auth?.token;
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    headers.set('content-type', 'application/json');
    return headers;
  },
});

// Enhanced base query with automatic token refresh
const baseQueryWithReauth = async (args: any, api: any, extraOptions: any) => {

  console.log('baseQueryWithReauth', args);

  let result = await baseQuery(args, api, extraOptions);

  // If we get 401, try to refresh token
  if (result.error && result.error.status === 401) {
    // Try to get a new token
    const refreshResult = await baseQuery('/auth/refresh', api, extraOptions);
    
    if (refreshResult.data) {
      // Store the new token
      api.dispatch({ type: 'auth/setCredentials', payload: refreshResult.data });
      // Retry the original query with new token
      result = await baseQuery(args, api, extraOptions);
    } else {
      // Refresh failed, logout user
      api.dispatch({ type: 'auth/clearCredentials' });
    }
  }

  return result;
};

// Main API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'User',
    'Farm',
    'Crop',
    'Order',
    'Notification',
    'Analytics',
    // ✅ ADDED: Admin service tag types
    'Admin',
    'Dashboard',
    'System',
    'Sellers',
    'Farms',
    'Crops'
  ],
  endpoints: (builder) => ({
    // ✅ UPDATED: Authentication endpoints matching postman seller-service.json
    login: builder.mutation({
      query: (credentials) => ({
        url: `/login`,
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['User'],
    }),

    logout: builder.mutation({
      query: () => ({
        url: `/logout`,
        method: 'POST',
      }),
    }),

    // ✅ UPDATED: Seller profile endpoints matching postman seller-service.json
    getSellerProfile: builder.query({
      query: () => ({
        url: `/profile`,
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    updateSellerProfile: builder.mutation({
      query: (data) => ({
        url: `/profile`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    updateBankDetails: builder.mutation({
      query: (data) => ({
        url: `/profile/bank-details`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    changePassword: builder.mutation({
      query: (data) => ({
        url: `/change-password`,
        method: 'PUT',
        body: data,
      }),
    }),

    getSellerDashboard: builder.query({
      query: () => ({
        url: `/dashboard`,
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // ✅ UPDATED: Farm endpoints matching postman farm-service.json
    getFarms: builder.query({
      query: (params?: { page?: number; limit?: number; sellerId?: string; state?: string; organicCertified?: boolean }) => ({
        url: `${environment.services.farmService}/`,
        method: 'GET',
        params,
      }),
      transformResponse: (response: any) => {
        // Transform the API response to match our expected structure
        return {
          farms: response.data || [],
          pagination: response.pagination || { page: 1, limit: 10, total: 0 },
          success: response.success || false
        };
      },
      providesTags: ['Farm'],
    }),

    getFarmById: builder.query({
      query: (farmId: string) => ({
        url: `${environment.services.farmService}/${farmId}`,
        method: 'GET',
      }),
      providesTags: ['Farm'],
    }),

    createFarm: builder.mutation({
      query: (farmData) => ({
        url: `${environment.services.farmService}/`,
        method: 'POST',
        body: farmData,
      }),
      invalidatesTags: ['Farm'],
    }),

    updateFarm: builder.mutation({
      query: ({ id, ...updates }) => ({
        url: `${environment.services.farmService}/${id}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['Farm'],
    }),

    deleteFarm: builder.mutation({
      query: (farmId: string) => ({
        url: `${environment.services.farmService}/${farmId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Farm'],
    }),

    searchFarms: builder.query({
      query: (params: { q?: string; location?: string; radius?: number; page?: number; limit?: number; soilType?: string; organicCertified?: boolean; minSize?: number; maxSize?: number }) => ({
        url: `${environment.services.farmService}/search`,
        method: 'GET',
        params,
      }),
      providesTags: ['Farm'],
    }),

    getFarmSuggestions: builder.query({
      query: (params: { q: string; limit?: number }) => ({
        url: `${environment.services.farmService}/suggestions`,
        method: 'GET',
        params,
      }),
      providesTags: ['Farm'],
    }),

    getNearbyFarms: builder.query({
      query: (params: { latitude: number; longitude: number; radius?: number; limit?: number }) => ({
        url: `${environment.services.farmService}/nearby`,
        method: 'GET',
        params,
      }),
      providesTags: ['Farm'],
    }),

    getFarmAnalytics: builder.query({
      query: (farmId: string) => ({
        url: `${environment.services.farmService}/${farmId}/analytics`,
        method: 'GET',
      }),
      providesTags: ['Analytics'],
    }),

    getFarmPerformance: builder.query({
      query: ({ farmId, period, year }: { farmId: string; period?: string; year?: number }) => ({
        url: `${environment.services.farmService}/${farmId}/performance`,
        method: 'GET',
        params: { period, year },
      }),
      providesTags: ['Analytics'],
    }),

    getFarmStatistics: builder.query({
      query: (params?: { groupBy?: string; organicOnly?: boolean }) => ({
        url: `${environment.services.farmService}/statistics`,
        method: 'GET',
        params,
      }),
      providesTags: ['Analytics'],
    }),

    getFarmCrops: builder.query({
      query: (farmId: string) => ({
        url: `${environment.services.farmService}/${farmId}/crops`,
        method: 'GET',
      }),
      providesTags: ['Crop'],
    }),

    addCropToFarm: builder.mutation({
      query: ({ farmId, cropData }: { farmId: string; cropData: any }) => ({
        url: `${environment.services.farmService}/${farmId}/crops`,
        method: 'POST',
        body: cropData,
      }),
      invalidatesTags: ['Crop', 'Farm'],
    }),

    updateFarmCrop: builder.mutation({
      query: ({ farmId, cropId, updates }: { farmId: string; cropId: string; updates: any }) => ({
        url: `${environment.services.farmService}/${farmId}/crops/${cropId}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['Crop', 'Farm'],
    }),

    submitFarmVerification: builder.mutation({
      query: ({ farmId, verificationData }: { farmId: string; verificationData: any }) => ({
        url: `${environment.services.farmService}/${farmId}/verification`,
        method: 'POST',
        body: verificationData,
      }),
      invalidatesTags: ['Farm'],
    }),

    getFarmVerificationStatus: builder.query({
      query: (farmId: string) => ({
        url: `${environment.services.farmService}/${farmId}/verification`,
        method: 'GET',
      }),
      providesTags: ['Farm'],
    }),

    // ✅ UPDATED: Crop endpoints matching postman crop-service.json
    getCrops: builder.query({
      query: (farmId?: string) => ({
        url: farmId ? `${environment.services.cropService}/?farmId=${farmId}` : `${environment.services.cropService}/`,
        method: 'GET',
      }),
      providesTags: ['Crop'],
    }),

    createCrop: builder.mutation({
      query: (cropData) => ({
        url: `${environment.services.cropService}/`,
        method: 'POST',
        body: cropData,
      }),
      invalidatesTags: ['Crop'],
    }),

    updateCropHealth: builder.mutation({
      query: ({ id, healthData }) => ({
        url: `${environment.services.cropService}/${id}`,
        method: 'PUT',
        body: healthData,
      }),
      invalidatesTags: ['Crop'],
    }),

    // ✅ UPDATED: Order endpoints matching postman order-service.json
    getOrders: builder.query({
      query: (params) => ({
        url: `${environment.services.orderService}/`,
        method: 'GET',
        params,
      }),
      providesTags: ['Order'],
    }),

    createOrder: builder.mutation({
      query: (orderData) => ({
        url: `${environment.services.orderService}/`,
        method: 'POST',
        body: orderData,
      }),
      invalidatesTags: ['Order'],
    }),

    // ✅ UPDATED: Notification endpoints matching postman notification-service.json
    getNotifications: builder.query({
      query: () => ({
        url: `${environment.services.notificationService}/`,
        method: 'GET',
      }),
      providesTags: ['Notification'],
    }),

    markNotificationRead: builder.mutation({
      query: (notificationId) => ({
        url: `${environment.services.notificationService}/${notificationId}/read`,
        method: 'PUT',
      }),
      invalidatesTags: ['Notification'],
    }),

    // ✅ UPDATED: Analytics endpoints matching postman analytics-service.json
    getAnalyticsFarmData: builder.query({
      query: (farmId) => ({
        url: `${environment.services.analyticsService}/farm/${farmId}`,
        method: 'GET',
      }),
      providesTags: ['Analytics'],
    }),

    getRevenueAnalytics: builder.query({
      query: (params) => ({
        url: `${environment.services.analyticsService}/revenue`,
        method: 'GET',
        params,
      }),
      providesTags: ['Analytics'],
    }),

    // ✅ UPDATED: Comprehensive Admin service endpoints matching postman admin-service.json

    // Authentication endpoints
    adminLogin: builder.mutation({
      query: (credentials) => ({
        url: `${environment.services.adminService}/admin/login`,
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Admin'],
    }),

    adminLogout: builder.mutation({
      query: () => ({
        url: `${environment.services.adminService}/admin/logout`,
        method: 'POST',
      }),
    }),

    // Dashboard & Analytics endpoints
    getAdminDashboard: builder.query({
      query: () => ({
        url: `${environment.services.adminService}/admin/dashboard`,
        method: 'GET',
      }),
      providesTags: ['Dashboard'],
    }),

    getSystemHealth: builder.query({
      query: () => ({
        url: `${environment.services.adminService}/admin/system/health`,
        method: 'GET',
      }),
      providesTags: ['System'],
    }),

    getSystemStats: builder.query({
      query: () => ({
        url: `${environment.services.adminService}/admin/system/stats`,
        method: 'GET',
      }),
      providesTags: ['System'],
    }),

    // Seller Management endpoints
    getAllSellers: builder.query({
      query: ({ page = 1, limit = 10, verified, state }) => ({
        url: `${environment.services.adminService}/admin/sellers`,
        method: 'GET',
        params: { page, limit, ...(verified !== undefined && { verified }), ...(state && { state }) },
      }),
      providesTags: ['Sellers'],
    }),

    getSellerById: builder.query({
      query: (sellerId) => ({
        url: `${environment.services.adminService}/admin/sellers/${sellerId}`,
        method: 'GET',
      }),
      providesTags: ['Sellers'],
    }),

    verifySeller: builder.mutation({
      query: ({ sellerId, verified, verificationNotes, verifiedBy }) => ({
        url: `${environment.services.adminService}/admin/sellers/${sellerId}/verify`,
        method: 'PUT',
        body: { verified, verificationNotes, verifiedBy },
      }),
      invalidatesTags: ['Sellers'],
    }),

    updateSeller: builder.mutation({
      query: ({ sellerId, status, notes }) => ({
        url: `${environment.services.adminService}/admin/sellers/${sellerId}`,
        method: 'PUT',
        body: { status, notes },
      }),
      invalidatesTags: ['Sellers'],
    }),

    suspendSeller: builder.mutation({
      query: ({ sellerId, status, reason, suspendedBy }) => ({
        url: `${environment.services.adminService}/admin/sellers/${sellerId}/suspend`,
        method: 'PUT',
        body: { status, reason, suspendedBy },
      }),
      invalidatesTags: ['Sellers'],
    }),

    // Farm Management endpoints
    getAllFarmsAdmin: builder.query({
      query: ({ page = 1, limit = 10, verified }) => ({
        url: `${environment.services.adminService}/admin/farms`,
        method: 'GET',
        params: { page, limit, ...(verified !== undefined && { verified }) },
      }),
      providesTags: ['Farms'],
    }),

    getFarmByIdAdmin: builder.query({
      query: (farmId) => ({
        url: `${environment.services.adminService}/admin/farms/${farmId}`,
        method: 'GET',
      }),
      providesTags: ['Farms'],
    }),

    verifyFarm: builder.mutation({
      query: ({ farmId, verified, verificationNotes, verifiedBy }) => ({
        url: `${environment.services.adminService}/admin/farms/${farmId}/verify`,
        method: 'PUT',
        body: { verified, verificationNotes, verifiedBy },
      }),
      invalidatesTags: ['Farms'],
    }),

    // Crop Management endpoints
    getAllCropsAdmin: builder.query({
      query: ({ page = 1, limit = 10, status }) => ({
        url: `${environment.services.adminService}/admin/crops`,
        method: 'GET',
        params: { page, limit, ...(status && { status }) },
      }),
      providesTags: ['Crops'],
    }),

    approveCrop: builder.mutation({
      query: ({ cropId, status, approvalNotes, approvedBy }) => ({
        url: `${environment.services.adminService}/admin/crops/${cropId}/approve`,
        method: 'PUT',
        body: { status, approvalNotes, approvedBy },
      }),
      invalidatesTags: ['Crops'],
    }),

    onboardSeller: builder.mutation({
      query: (sellerData) => ({
        url: `${environment.services.adminService}/admin/sellers`,
        method: 'POST',
        body: sellerData,
      }),
      invalidatesTags: ['User'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useLoginMutation,
  useLogoutMutation,
  // ✅ ADDED: Seller profile hooks
  useGetSellerProfileQuery,
  useUpdateSellerProfileMutation,
  useUpdateBankDetailsMutation,
  useChangePasswordMutation,
  useGetSellerDashboardQuery,
  // ✅ UPDATED: Farm service hooks
  useGetFarmsQuery,
  useGetFarmByIdQuery,
  useCreateFarmMutation,
  useUpdateFarmMutation,
  useDeleteFarmMutation,
  useSearchFarmsQuery,
  useGetFarmSuggestionsQuery,
  useGetNearbyFarmsQuery,
  useGetFarmAnalyticsQuery,
  useGetFarmPerformanceQuery,
  useGetFarmStatisticsQuery,
  useGetFarmCropsQuery,
  useAddCropToFarmMutation,
  useUpdateFarmCropMutation,
  useSubmitFarmVerificationMutation,
  useGetFarmVerificationStatusQuery,
  // ✅ ADDED: Crop service hooks
  useGetCropsQuery,
  useCreateCropMutation,
  useUpdateCropHealthMutation,
  useGetOrdersQuery,
  useCreateOrderMutation,
  useGetNotificationsQuery,
  useMarkNotificationReadMutation,
  useGetAnalyticsFarmDataQuery,
  useGetRevenueAnalyticsQuery,

  // ✅ UPDATED: Admin service hooks matching postman admin-service.json
  // Authentication hooks
  useAdminLoginMutation,
  useAdminLogoutMutation,

  // Dashboard & Analytics hooks
  useGetAdminDashboardQuery,
  useGetSystemHealthQuery,
  useGetSystemStatsQuery,

  // Seller Management hooks
  useGetAllSellersQuery,
  useGetSellerByIdQuery,
  useVerifySellerMutation,
  useUpdateSellerMutation,
  useSuspendSellerMutation,

  // Farm Management hooks
  useGetAllFarmsAdminQuery,
  useGetFarmByIdAdminQuery,
  useVerifyFarmMutation,

  // Crop Management hooks
  useGetAllCropsAdminQuery,
  useApproveCropMutation,
} = apiSlice;