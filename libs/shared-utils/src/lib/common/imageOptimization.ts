/**
 * Image Optimization Utilities for BeFarma Agricultural Platform
 * Specialized functions for optimizing farm photos, crop images, and verification documents
 */

export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'webp' | 'png';
  maintainAspectRatio?: boolean;
  addWatermark?: boolean;
  watermarkText?: string;
}

export interface OptimizationResult {
  success: boolean;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  file?: File;
  error?: string;
  metadata?: ImageMetadata;
}

export interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  quality?: number;
  hasExif?: boolean;
}

/**
 * Pre-defined optimization presets for different use cases
 */
export const OPTIMIZATION_PRESETS = {
  // Farm documentation photos
  FARM_PHOTO: {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 0.85,
    format: 'jpeg' as const,
    maintainAspectRatio: true,
  },
  
  // Crop monitoring images (higher quality for analysis)
  CROP_MONITORING: {
    maxWidth: 1600,
    maxHeight: 1200,
    quality: 0.9,
    format: 'jpeg' as const,
    maintainAspectRatio: true,
  },
  
  // Profile pictures
  PROFILE_PICTURE: {
    maxWidth: 400,
    maxHeight: 400,
    quality: 0.8,
    format: 'jpeg' as const,
    maintainAspectRatio: false, // Allow cropping to square
  },
  
  // Thumbnail generation
  THUMBNAIL: {
    maxWidth: 200,
    maxHeight: 200,
    quality: 0.7,
    format: 'webp' as const,
    maintainAspectRatio: true,
  },
  
  // Verification documents (high quality)
  VERIFICATION_DOC: {
    maxWidth: 2048,
    maxHeight: 2048,
    quality: 0.95,
    format: 'jpeg' as const,
    maintainAspectRatio: true,
  },
  
  // Web display optimized
  WEB_OPTIMIZED: {
    maxWidth: 1200,
    maxHeight: 800,
    quality: 0.8,
    format: 'webp' as const,
    maintainAspectRatio: true,
  },
} as const;

/**
 * Core image optimization utilities
 */
export const imageOptimizationUtils = {
  /**
   * Get image dimensions
   */
  getImageDimensions: (file: File): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({ width: img.width, height: img.height });
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };
      
      img.src = url;
    });
  },

  /**
   * Calculate optimal dimensions while maintaining aspect ratio
   */
  calculateOptimalDimensions: (
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number,
    maintainAspectRatio = true
  ): { width: number; height: number } => {
    if (!maintainAspectRatio) {
      return { width: maxWidth, height: maxHeight };
    }

    const aspectRatio = originalWidth / originalHeight;
    
    let newWidth = originalWidth;
    let newHeight = originalHeight;
    
    // Scale down if needed
    if (newWidth > maxWidth) {
      newWidth = maxWidth;
      newHeight = newWidth / aspectRatio;
    }
    
    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = newHeight * aspectRatio;
    }
    
    return {
      width: Math.round(newWidth),
      height: Math.round(newHeight),
    };
  },

  /**
   * Optimize image with specified options
   */
  optimizeImage: async (
    file: File,
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizationResult> => {
    try {
      const {
        maxWidth = 1920,
        maxHeight = 1080,
        quality = 0.8,
        format = 'jpeg',
        maintainAspectRatio = true,
        addWatermark = false,
        watermarkText = 'BeFarma',
      } = options;

      // Get original dimensions
      const originalDimensions = await imageOptimizationUtils.getImageDimensions(file);
      
      // Calculate optimal dimensions
      const optimalDimensions = imageOptimizationUtils.calculateOptimalDimensions(
        originalDimensions.width,
        originalDimensions.height,
        maxWidth,
        maxHeight,
        maintainAspectRatio
      );

      // Create canvas and context
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }

      canvas.width = optimalDimensions.width;
      canvas.height = optimalDimensions.height;

      // Load and draw image
      const img = new Image();
      const imageUrl = URL.createObjectURL(file);
      
      return new Promise((resolve, reject) => {
        img.onload = () => {
          URL.revokeObjectURL(imageUrl);
          
          // Draw image
          ctx.drawImage(img, 0, 0, optimalDimensions.width, optimalDimensions.height);
          
          // Add watermark if requested
          if (addWatermark) {
            imageOptimizationUtils.addWatermark(ctx, watermarkText, optimalDimensions);
          }
          
          // Convert to optimized format
          const outputFormat = format === 'jpeg' ? 'image/jpeg' : 
                              format === 'webp' ? 'image/webp' : 'image/png';
          
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const optimizedFile = new File([blob], 
                  imageOptimizationUtils.generateOptimizedFileName(file.name, format), 
                  {
                    type: outputFormat,
                    lastModified: Date.now(),
                  }
                );
                
                const compressionRatio = ((file.size - blob.size) / file.size) * 100;
                
                resolve({
                  success: true,
                  originalSize: file.size,
                  optimizedSize: blob.size,
                  compressionRatio: Math.max(0, compressionRatio),
                  file: optimizedFile,
                  metadata: {
                    width: optimalDimensions.width,
                    height: optimalDimensions.height,
                    format: outputFormat,
                    size: blob.size,
                    quality,
                  },
                });
              } else {
                reject(new Error('Failed to create optimized image blob'));
              }
            },
            outputFormat,
            quality
          );
        };
        
        img.onerror = () => {
          URL.revokeObjectURL(imageUrl);
          reject(new Error('Failed to load image for optimization'));
        };
        
        img.src = imageUrl;
      });
      
    } catch (error) {
      return {
        success: false,
        originalSize: file.size,
        optimizedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Optimization failed',
      };
    }
  },

  /**
   * Add watermark to canvas
   */
  addWatermark: (
    ctx: CanvasRenderingContext2D,
    text: string,
    dimensions: { width: number; height: number }
  ): void => {
    const fontSize = Math.max(12, Math.min(24, dimensions.width / 40));
    
    ctx.save();
    ctx.globalAlpha = 0.3;
    ctx.fillStyle = '#FFFFFF';
    ctx.strokeStyle = '#000000';
    ctx.font = `${fontSize}px Arial`;
    ctx.textAlign = 'right';
    ctx.textBaseline = 'bottom';
    
    const x = dimensions.width - 10;
    const y = dimensions.height - 10;
    
    ctx.strokeText(text, x, y);
    ctx.fillText(text, x, y);
    ctx.restore();
  },

  /**
   * Generate optimized file name
   */
  generateOptimizedFileName: (originalName: string, format: string): string => {
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
    const timestamp = Date.now();
    return `${nameWithoutExt}_optimized_${timestamp}.${format}`;
  },

  /**
   * Create multiple sizes for responsive images
   */
  createResponsiveSizes: async (
    file: File,
    sizes: Array<{ width: number; height: number; suffix: string }>
  ): Promise<{ [key: string]: OptimizationResult }> => {
    const results: { [key: string]: OptimizationResult } = {};
    
    for (const size of sizes) {
      const options: ImageOptimizationOptions = {
        maxWidth: size.width,
        maxHeight: size.height,
        quality: 0.8,
        format: 'webp',
        maintainAspectRatio: true,
      };
      
      const result = await imageOptimizationUtils.optimizeImage(file, options);
      results[size.suffix] = result;
    }
    
    return results;
  },

  /**
   * Batch optimize multiple images
   */
  batchOptimize: async (
    files: File[],
    preset: keyof typeof OPTIMIZATION_PRESETS
  ): Promise<OptimizationResult[]> => {
    const options = OPTIMIZATION_PRESETS[preset];
    const results: OptimizationResult[] = [];
    
    for (const file of files) {
      const result = await imageOptimizationUtils.optimizeImage(file, options);
      results.push(result);
    }
    
    return results;
  },
};

/**
 * Agricultural-specific image optimization functions
 */
export const agriculturalImageOptimizer = {
  /**
   * Optimize farm photos for documentation
   */
  optimizeFarmPhoto: async (file: File): Promise<OptimizationResult> => {
    return imageOptimizationUtils.optimizeImage(file, {
      ...OPTIMIZATION_PRESETS.FARM_PHOTO,
      addWatermark: true,
      watermarkText: 'BeFarma Farm',
    });
  },

  /**
   * Optimize crop monitoring images
   */
  optimizeCropPhoto: async (file: File): Promise<OptimizationResult> => {
    return imageOptimizationUtils.optimizeImage(file, {
      ...OPTIMIZATION_PRESETS.CROP_MONITORING,
      addWatermark: false, // No watermark for analysis
    });
  },

  /**
   * Optimize verification documents
   */
  optimizeVerificationDoc: async (file: File): Promise<OptimizationResult> => {
    return imageOptimizationUtils.optimizeImage(file, OPTIMIZATION_PRESETS.VERIFICATION_DOC);
  },

  /**
   * Create profile picture with crop to square
   */
  createProfilePicture: async (file: File): Promise<OptimizationResult> => {
    return imageOptimizationUtils.optimizeImage(file, OPTIMIZATION_PRESETS.PROFILE_PICTURE);
  },

  /**
   * Generate thumbnails for farm gallery
   */
  generateFarmThumbnails: async (files: File[]): Promise<OptimizationResult[]> => {
    return imageOptimizationUtils.batchOptimize(files, 'THUMBNAIL');
  },

  /**
   * Create responsive farm gallery images
   */
  createFarmGallery: async (file: File): Promise<{ [key: string]: OptimizationResult }> => {
    const sizes = [
      { width: 400, height: 300, suffix: 'small' },
      { width: 800, height: 600, suffix: 'medium' },
      { width: 1200, height: 900, suffix: 'large' },
    ];
    
    return imageOptimizationUtils.createResponsiveSizes(file, sizes);
  },

  /**
   * Optimize crop progress photos for timeline
   */
  optimizeCropProgressPhoto: async (file: File, stage: string): Promise<OptimizationResult> => {
    return imageOptimizationUtils.optimizeImage(file, {
      ...OPTIMIZATION_PRESETS.CROP_MONITORING,
      addWatermark: true,
      watermarkText: `${stage} - BeFarma`,
    });
  },
};

/**
 * Image quality assessment
 */
export const imageQualityUtils = {
  /**
   * Assess if image quality is suitable for crop monitoring
   */
  assessCropPhotoQuality: async (file: File): Promise<{
    suitable: boolean;
    issues: string[];
    recommendations: string[];
  }> => {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    try {
      const dimensions = await imageOptimizationUtils.getImageDimensions(file);
      
      // Check resolution
      if (dimensions.width < 800 || dimensions.height < 600) {
        issues.push('Low resolution may affect crop analysis accuracy');
        recommendations.push('Use a camera with at least 2MP resolution');
      }
      
      // Check file size vs dimensions (proxy for quality)
      const expectedMinSize = (dimensions.width * dimensions.height) / 100; // Very rough estimate
      if (file.size < expectedMinSize) {
        issues.push('Image appears to be heavily compressed');
        recommendations.push('Use less compression or higher quality settings');
      }
      
      // Check aspect ratio
      const aspectRatio = dimensions.width / dimensions.height;
      if (aspectRatio < 0.75 || aspectRatio > 2) {
        issues.push('Unusual aspect ratio detected');
        recommendations.push('Try to capture crops in landscape or portrait orientation');
      }
      
    } catch {
      issues.push('Unable to analyze image quality');
    }
    
    return {
      suitable: issues.length === 0,
      issues,
      recommendations,
    };
  },

  /**
   * Check if image is suitable for verification documents
   */
  assessDocumentQuality: async (file: File): Promise<{
    suitable: boolean;
    issues: string[];
    recommendations: string[];
  }> => {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    try {
      const dimensions = await imageOptimizationUtils.getImageDimensions(file);
      
      if (dimensions.width < 1000 || dimensions.height < 700) {
        issues.push('Document resolution too low for clear text reading');
        recommendations.push('Scan or photograph documents at higher resolution');
      }
      
      if (file.size > 10 * 1024 * 1024) {
        issues.push('File size very large');
        recommendations.push('Compress the image while maintaining text clarity');
      }
      
    } catch {
      issues.push('Unable to analyze document quality');
    }
    
    return {
      suitable: issues.length === 0,
      issues,
      recommendations,
    };
  },
};

export default {
  imageOptimizationUtils,
  agriculturalImageOptimizer,
  imageQualityUtils,
  OPTIMIZATION_PRESETS,
}; 