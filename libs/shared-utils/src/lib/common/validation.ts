/**
 * Form Validation Utilities for BeFarma Agricultural Platform
 * Comprehensive validation functions for agricultural data, farms, crops, and user information
 */

export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => ValidationResult;
}

/**
 * Core validation functions
 */
export const validators = {
  /**
   * Check if value is required and not empty
   */
  required: (value: any): ValidationResult => {
    if (value === null || value === undefined || value === '') {
      return { isValid: false, message: 'This field is required' };
    }
    return { isValid: true };
  },

  /**
   * Check minimum length
   */
  minLength: (value: string, min: number): ValidationResult => {
    if (typeof value !== 'string' || value.length < min) {
      return { isValid: false, message: `Minimum length is ${min} characters` };
    }
    return { isValid: true };
  },

  /**
   * Check maximum length
   */
  maxLength: (value: string, max: number): ValidationResult => {
    if (typeof value === 'string' && value.length > max) {
      return { isValid: false, message: `Maximum length is ${max} characters` };
    }
    return { isValid: true };
  },

  /**
   * Check pattern match
   */
  pattern: (value: string, pattern: RegExp, message?: string): ValidationResult => {
    if (typeof value === 'string' && !pattern.test(value)) {
      return { isValid: false, message: message || 'Invalid format' };
    }
    return { isValid: true };
  },

  /**
   * Email validation
   */
  email: (value: string): ValidationResult => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return validators.pattern(value, emailPattern, 'Please enter a valid email address');
  },

  /**
   * Phone number validation (Indian format)
   */
  phone: (value: string): ValidationResult => {
    const phonePattern = /^[6-9]\d{9}$/;
    return validators.pattern(value, phonePattern, 'Please enter a valid 10-digit mobile number');
  },

  /**
   * Password validation
   */
  password: (value: string): ValidationResult => {
    if (typeof value !== 'string') {
      return { isValid: false, message: 'Password is required' };
    }
    if (value.length < 8) {
      return { isValid: false, message: 'Password must be at least 8 characters long' };
    }
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
      return { isValid: false, message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number' };
    }
    return { isValid: true };
  },

  /**
   * Confirm password validation
   */
  confirmPassword: (value: string, originalPassword: string): ValidationResult => {
    if (value !== originalPassword) {
      return { isValid: false, message: 'Passwords do not match' };
    }
    return { isValid: true };
  },

  /**
   * Number validation
   */
  number: (value: any, min?: number, max?: number): ValidationResult => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    if (isNaN(num)) {
      return { isValid: false, message: 'Please enter a valid number' };
    }
    if (min !== undefined && num < min) {
      return { isValid: false, message: `Value must be at least ${min}` };
    }
    if (max !== undefined && num > max) {
      return { isValid: false, message: `Value cannot exceed ${max}` };
    }
    return { isValid: true };
  },

  /**
   * Positive number validation
   */
  positiveNumber: (value: any): ValidationResult => {
    return validators.number(value, 0.01);
  },

  /**
   * Integer validation
   */
  integer: (value: any): ValidationResult => {
    const num = typeof value === 'string' ? parseInt(value, 10) : value;
    if (!Number.isInteger(num)) {
      return { isValid: false, message: 'Please enter a whole number' };
    }
    return { isValid: true };
  },

  /**
   * Indian PIN code validation
   */
  pincode: (value: string): ValidationResult => {
    const pincodePattern = /^\d{6}$/;
    return validators.pattern(value, pincodePattern, 'Please enter a valid 6-digit PIN code');
  },
};

/**
 * Agricultural-specific validation functions
 */
export const agriculturalValidators = {
  /**
   * Farm area validation (in acres)
   */
  farmArea: (value: any): ValidationResult => {
    const result = validators.positiveNumber(value);
    if (!result.isValid) {
      return { isValid: false, message: 'Please enter a valid farm area in acres' };
    }
    
    const area = parseFloat(value);
    if (area > 10000) {
      return { isValid: false, message: 'Farm area cannot exceed 10,000 acres' };
    }
    
    return { isValid: true };
  },

  /**
   * GPS coordinates validation
   */
  latitude: (value: any): ValidationResult => {
    const result = validators.number(value, -90, 90);
    if (!result.isValid) {
      return { isValid: false, message: 'Please enter a valid latitude (-90 to 90)' };
    }
    return { isValid: true };
  },

  longitude: (value: any): ValidationResult => {
    const result = validators.number(value, -180, 180);
    if (!result.isValid) {
      return { isValid: false, message: 'Please enter a valid longitude (-180 to 180)' };
    }
    return { isValid: true };
  },

  /**
   * Crop yield validation (in kg/acre)
   */
  cropYield: (value: any): ValidationResult => {
    const result = validators.positiveNumber(value);
    if (!result.isValid) {
      return { isValid: false, message: 'Please enter a valid crop yield in kg/acre' };
    }
    
    const yield_ = parseFloat(value);
    if (yield_ > 50000) {
      return { isValid: false, message: 'Crop yield seems unusually high. Please verify.' };
    }
    
    return { isValid: true };
  },

  /**
   * Soil pH validation
   */
  soilPH: (value: any): ValidationResult => {
    const result = validators.number(value, 0, 14);
    if (!result.isValid) {
      return { isValid: false, message: 'Please enter a valid soil pH (0-14)' };
    }
    return { isValid: true };
  },

  /**
   * Indian Aadhaar number validation
   */
  aadhaar: (value: string): ValidationResult => {
    const aadhaarPattern = /^\d{12}$/;
    const result = validators.pattern(value, aadhaarPattern, 'Please enter a valid 12-digit Aadhaar number');
    if (!result.isValid) return result;
    
    // Basic checksum validation for Aadhaar
    const digits = value.split('').map(Number);
    const checksum = digits.reduce((sum, digit, index) => {
      const weight = [2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1][index];
      const product = digit * weight;
      return sum + Math.floor(product / 10) + (product % 10);
    }, 0);
    
    if (checksum % 10 !== 0) {
      return { isValid: false, message: 'Please enter a valid Aadhaar number' };
    }
    
    return { isValid: true };
  },

  /**
   * Indian PAN number validation
   */
  pan: (value: string): ValidationResult => {
    const panPattern = /^[A-Z]{5}\d{4}[A-Z]$/;
    return validators.pattern(value, panPattern, 'Please enter a valid PAN number (e.g., **********)');
  },

  /**
   * Bank account number validation
   */
  bankAccount: (value: string): ValidationResult => {
    const accountPattern = /^\d{9,18}$/;
    return validators.pattern(value, accountPattern, 'Please enter a valid bank account number (9-18 digits)');
  },

  /**
   * IFSC code validation
   */
  ifscCode: (value: string): ValidationResult => {
    const ifscPattern = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return validators.pattern(value, ifscPattern, 'Please enter a valid IFSC code (e.g., SBIN0001234)');
  },

  /**
   * Pesticide/Fertilizer quantity validation (in kg or liters)
   */
  chemicalQuantity: (value: any, maxRecommended?: number): ValidationResult => {
    const result = validators.positiveNumber(value);
    if (!result.isValid) {
      return { isValid: false, message: 'Please enter a valid quantity' };
    }
    
    if (maxRecommended && parseFloat(value) > maxRecommended) {
      return { 
        isValid: false, 
        message: `Quantity exceeds recommended limit of ${maxRecommended}. Please consult agricultural expert.` 
      };
    }
    
    return { isValid: true };
  },

  /**
   * Crop planting date validation
   */
  plantingDate: (value: string | Date): ValidationResult => {
    const date = new Date(value);
    const now = new Date();
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
    const sixMonthsFromNow = new Date(now.getFullYear(), now.getMonth() + 6, now.getDate());
    
    if (isNaN(date.getTime())) {
      return { isValid: false, message: 'Please enter a valid date' };
    }
    
    if (date < oneYearAgo) {
      return { isValid: false, message: 'Planting date cannot be more than 1 year ago' };
    }
    
    if (date > sixMonthsFromNow) {
      return { isValid: false, message: 'Planting date cannot be more than 6 months in the future' };
    }
    
    return { isValid: true };
  },

  /**
   * Water usage validation (in liters)
   */
  waterUsage: (value: any): ValidationResult => {
    const result = validators.positiveNumber(value);
    if (!result.isValid) {
      return { isValid: false, message: 'Please enter a valid water usage amount in liters' };
    }

    const usage = parseFloat(value);
    if (usage > 1000000) { // 1 million liters
      return { isValid: false, message: 'Water usage amount seems unusually high. Please verify.' };
    }

    return { isValid: true };
  },

  /**
   * Plot number validation
   */
  plotNumber: (value: any): ValidationResult => {
    if (!value || value.toString().trim() === '') {
      return { isValid: false, message: 'Please enter a plot number' };
    }

    const plotStr = value.toString().trim();
    if (plotStr.length > 20) {
      return { isValid: false, message: 'Plot number cannot exceed 20 characters' };
    }

    return { isValid: true };
  },
};

/**
 * Validate a single field with multiple rules
 */
export function validateField(value: any, rules: ValidationRule[]): ValidationResult {
  for (const rule of rules) {
    if (rule.required) {
      const result = validators.required(value);
      if (!result.isValid) return result;
    }
    
    if (rule.minLength && typeof value === 'string') {
      const result = validators.minLength(value, rule.minLength);
      if (!result.isValid) return result;
    }
    
    if (rule.maxLength && typeof value === 'string') {
      const result = validators.maxLength(value, rule.maxLength);
      if (!result.isValid) return result;
    }
    
    if (rule.pattern && typeof value === 'string') {
      const result = validators.pattern(value, rule.pattern);
      if (!result.isValid) return result;
    }
    
    if (rule.custom) {
      const result = rule.custom(value);
      if (!result.isValid) return result;
    }
  }
  
  return { isValid: true };
}

/**
 * Validate multiple fields
 */
export function validateForm(data: Record<string, any>, rules: Record<string, ValidationRule[]>): {
  isValid: boolean;
  errors: Record<string, string>;
} {
  const errors: Record<string, string> = {};
  
  for (const [field, fieldRules] of Object.entries(rules)) {
    const result = validateField(data[field], fieldRules);
    if (!result.isValid && result.message) {
      errors[field] = result.message;
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

/**
 * Common validation rule sets for agricultural forms
 */
export const commonRules = {
  email: [
    { required: true },
    { custom: validators.email }
  ],
  
  phone: [
    { required: true },
    { custom: validators.phone }
  ],
  
  password: [
    { required: true },
    { custom: validators.password }
  ],
  
  farmArea: [
    { required: true },
    { custom: agriculturalValidators.farmArea }
  ],
  
  plotNumber: [
    { required: true },
    { custom: agriculturalValidators.plotNumber }
  ],
  
  aadhaar: [
    { required: true },
    { custom: agriculturalValidators.aadhaar }
  ],
  
  pan: [
    { required: true },
    { custom: agriculturalValidators.pan }
  ],
  
  bankAccount: [
    { required: true },
    { custom: agriculturalValidators.bankAccount }
  ],
  
  ifscCode: [
    { required: true },
    { custom: agriculturalValidators.ifscCode }
  ],
};

export default {
  validators,
  agriculturalValidators,
  validateField,
  validateForm,
  commonRules,
}; 