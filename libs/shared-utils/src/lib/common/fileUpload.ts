/**
 * File Upload Utilities for BeFarma Agricultural Platform
 * Handles file uploads, validation, and processing for agricultural documents and images
 */

export interface FileUploadOptions {
  maxSizeBytes?: number;
  allowedTypes?: string[];
  maxFiles?: number;
  compressionQuality?: number;
  resizeMaxWidth?: number;
  resizeMaxHeight?: number;
}

export interface FileUploadResult {
  success: boolean;
  file?: File;
  error?: string;
  preview?: string;
  metadata?: FileMetadata;
}

export interface FileMetadata {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  dimensions?: { width: number; height: number };
  location?: { latitude?: number; longitude?: number };
}

export interface DocumentValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * File type constants for agricultural platform
 */
export const FILE_TYPES = {
  // Images
  IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  
  // Documents
  DOCUMENTS: [
    'application/pdf',
    'image/jpeg',
    'image/jpg', 
    'image/png'
  ],
  
  // Agricultural specific
  FARM_PHOTOS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  CROP_PHOTOS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  VERIFICATION_DOCS: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
  
  // Bulk data
  CSV_DATA: ['text/csv', 'application/vnd.ms-excel'],
} as const;

/**
 * File size limits for different file types
 */
export const FILE_SIZE_LIMITS = {
  // Images in bytes
  FARM_PHOTO: 5 * 1024 * 1024,      // 5MB
  CROP_PHOTO: 3 * 1024 * 1024,      // 3MB
  PROFILE_PHOTO: 2 * 1024 * 1024,   // 2MB
  
  // Documents in bytes
  VERIFICATION_DOC: 10 * 1024 * 1024, // 10MB
  GENERAL_DOC: 5 * 1024 * 1024,       // 5MB
  
  // Data files
  CSV_FILE: 1 * 1024 * 1024,         // 1MB
} as const;

/**
 * Core file upload utilities
 */
export const fileUploadUtils = {
  /**
   * Validate file type
   */
  validateFileType: (file: File, allowedTypes: readonly string[]): boolean => {
    return allowedTypes.includes(file.type);
  },

  /**
   * Validate file size
   */
  validateFileSize: (file: File, maxSizeBytes: number): boolean => {
    return file.size <= maxSizeBytes;
  },

  /**
   * Format file size to human readable format
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Get file extension
   */
  getFileExtension: (fileName: string): string => {
    return fileName.slice(((fileName.lastIndexOf('.') - 1) >>> 0) + 2);
  },

  /**
   * Generate unique file name
   */
  generateFileName: (originalName: string, prefix?: string): string => {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const extension = fileUploadUtils.getFileExtension(originalName);
    const baseName = originalName.replace(/\.[^/.]+$/, '');
    
    const cleanBaseName = baseName.replace(/[^a-zA-Z0-9]/g, '_');
    const prefixStr = prefix ? `${prefix}_` : '';
    
    return `${prefixStr}${cleanBaseName}_${timestamp}_${randomStr}.${extension}`;
  },

  /**
   * Create file preview URL
   */
  createPreviewUrl: (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(e);
      reader.readAsDataURL(file);
    });
  },

  /**
   * Get image dimensions
   */
  getImageDimensions: (file: File): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({ width: img.width, height: img.height });
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };
      
      img.src = url;
    });
  },

  /**
   * Compress image file
   */
  compressImage: (
    file: File, 
    maxWidth = 1920, 
    maxHeight = 1080, 
    quality = 0.8
  ): Promise<File> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Image compression failed'));
            }
          },
          file.type,
          quality
        );
      };
      
      img.onerror = () => reject(new Error('Failed to load image for compression'));
      img.src = URL.createObjectURL(file);
    });
  },

  /**
   * Extract EXIF data (basic implementation)
   */
  extractMetadata: async (file: File): Promise<FileMetadata> => {
    const metadata: FileMetadata = {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
    };

    // Get image dimensions for image files
    if (file.type.startsWith('image/')) {
      try {
        metadata.dimensions = await fileUploadUtils.getImageDimensions(file);
      } catch (error) {
        console.warn('Failed to extract image dimensions:', error);
      }
    }

    return metadata;
  },
};

/**
 * Agricultural-specific file validation
 */
export const agriculturalFileValidators = {
  /**
   * Validate farm photo
   */
  validateFarmPhoto: async (file: File): Promise<DocumentValidation> => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Type validation
    if (!fileUploadUtils.validateFileType(file, FILE_TYPES.FARM_PHOTOS)) {
      errors.push('Please upload a valid image file (JPEG, PNG, or WebP)');
    }

    // Size validation
    if (!fileUploadUtils.validateFileSize(file, FILE_SIZE_LIMITS.FARM_PHOTO)) {
      errors.push(`File size must be less than ${fileUploadUtils.formatFileSize(FILE_SIZE_LIMITS.FARM_PHOTO)}`);
    }

    // Image quality validation
    if (file.type.startsWith('image/')) {
      try {
        const dimensions = await fileUploadUtils.getImageDimensions(file);
        
        if (dimensions.width < 800 || dimensions.height < 600) {
          warnings.push('Image resolution is low. For better quality, use images with at least 800x600 pixels');
        }
        
        if (dimensions.width > 4000 || dimensions.height > 4000) {
          warnings.push('Image resolution is very high. The file will be compressed for optimal performance');
        }
      } catch {
        errors.push('Unable to read image file');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  },

  /**
   * Validate crop monitoring photo
   */
  validateCropPhoto: async (file: File): Promise<DocumentValidation> => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Type validation
    if (!fileUploadUtils.validateFileType(file, FILE_TYPES.CROP_PHOTOS)) {
      errors.push('Please upload a valid image file (JPEG, PNG, or WebP)');
    }

    // Size validation
    if (!fileUploadUtils.validateFileSize(file, FILE_SIZE_LIMITS.CROP_PHOTO)) {
      errors.push(`File size must be less than ${fileUploadUtils.formatFileSize(FILE_SIZE_LIMITS.CROP_PHOTO)}`);
    }

    // Image quality for crop monitoring
    if (file.type.startsWith('image/')) {
      try {
        const dimensions = await fileUploadUtils.getImageDimensions(file);
        
        if (dimensions.width < 640 || dimensions.height < 480) {
          errors.push('Crop photos must have minimum resolution of 640x480 pixels for proper analysis');
        }
      } catch {
        errors.push('Unable to read image file');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  },

  /**
   * Validate verification documents (Aadhaar, PAN, etc.)
   */
  validateVerificationDocument: async (file: File): Promise<DocumentValidation> => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Type validation
    if (!fileUploadUtils.validateFileType(file, FILE_TYPES.VERIFICATION_DOCS)) {
      errors.push('Please upload a PDF or image file (JPEG, PNG)');
    }

    // Size validation
    if (!fileUploadUtils.validateFileSize(file, FILE_SIZE_LIMITS.VERIFICATION_DOC)) {
      errors.push(`File size must be less than ${fileUploadUtils.formatFileSize(FILE_SIZE_LIMITS.VERIFICATION_DOC)}`);
    }

    // Document quality checks for images
    if (file.type.startsWith('image/')) {
      try {
        const dimensions = await fileUploadUtils.getImageDimensions(file);
        
        if (dimensions.width < 600 || dimensions.height < 400) {
          errors.push('Document image must be clear and readable. Minimum resolution: 600x400 pixels');
        }
        
        // Check if image might be too dark or unclear
        if (file.size < 50000 && dimensions.width * dimensions.height > 500000) {
          warnings.push('Image file size seems small for its resolution. Ensure the document is clearly visible');
        }
      } catch {
        errors.push('Unable to read image file');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  },

  /**
   * Validate bulk data files (CSV)
   */
  validateDataFile: (file: File): DocumentValidation => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Type validation
    if (!fileUploadUtils.validateFileType(file, FILE_TYPES.CSV_DATA)) {
      errors.push('Please upload a CSV file');
    }

    // Size validation
    if (!fileUploadUtils.validateFileSize(file, FILE_SIZE_LIMITS.CSV_FILE)) {
      errors.push(`File size must be less than ${fileUploadUtils.formatFileSize(FILE_SIZE_LIMITS.CSV_FILE)}`);
    }

    // File name validation
    if (!file.name.toLowerCase().includes('crop') && !file.name.toLowerCase().includes('farm')) {
      warnings.push('File name should indicate content type (e.g., crop_data.csv, farm_inventory.csv)');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  },
};

/**
 * File upload handling with agricultural context
 */
export const uploadHandlers = {
  /**
   * Handle multiple file upload with validation
   */
  handleMultipleFiles: async (
    files: FileList | File[],
    validator: (file: File) => Promise<DocumentValidation>,
    options: FileUploadOptions = {}
  ): Promise<FileUploadResult[]> => {
    const fileArray = Array.from(files);
    const maxFiles = options.maxFiles || 10;
    
    if (fileArray.length > maxFiles) {
      return [{
        success: false,
        error: `Maximum ${maxFiles} files allowed`,
      }];
    }

    const results: FileUploadResult[] = [];
    
    for (const file of fileArray) {
      try {
        const validation = await validator(file);
        
        if (!validation.isValid) {
          results.push({
            success: false,
            file,
            error: validation.errors.join(', '),
          });
          continue;
        }

        // Process file
        let processedFile = file;
        
        // Compress images if needed
        if (file.type.startsWith('image/') && options.compressionQuality) {
          processedFile = await fileUploadUtils.compressImage(
            file,
            options.resizeMaxWidth,
            options.resizeMaxHeight,
            options.compressionQuality
          );
        }

        // Generate preview
        const preview = await fileUploadUtils.createPreviewUrl(processedFile);
        const metadata = await fileUploadUtils.extractMetadata(processedFile);

        results.push({
          success: true,
          file: processedFile,
          preview,
          metadata,
        });

      } catch (error) {
        results.push({
          success: false,
          file,
          error: error instanceof Error ? error.message : 'Upload failed',
        });
      }
    }

    return results;
  },

  /**
   * Handle farm photo uploads
   */
  handleFarmPhotos: async (files: FileList | File[]): Promise<FileUploadResult[]> => {
    return uploadHandlers.handleMultipleFiles(
      files,
      agriculturalFileValidators.validateFarmPhoto,
      {
        maxFiles: 5,
        compressionQuality: 0.8,
        resizeMaxWidth: 1920,
        resizeMaxHeight: 1080,
      }
    );
  },

  /**
   * Handle crop monitoring photos
   */
  handleCropPhotos: async (files: FileList | File[]): Promise<FileUploadResult[]> => {
    return uploadHandlers.handleMultipleFiles(
      files,
      agriculturalFileValidators.validateCropPhoto,
      {
        maxFiles: 3,
        compressionQuality: 0.9, // Higher quality for analysis
        resizeMaxWidth: 1600,
        resizeMaxHeight: 1200,
      }
    );
  },

  /**
   * Handle verification documents
   */
  handleVerificationDocs: async (files: FileList | File[]): Promise<FileUploadResult[]> => {
    return uploadHandlers.handleMultipleFiles(
      files,
      agriculturalFileValidators.validateVerificationDocument,
      {
        maxFiles: 1,
        compressionQuality: 0.9, // High quality for document clarity
      }
    );
  },
};

/**
 * Utility for handling drag and drop
 */
export const dragDropUtils = {
  /**
   * Handle drag over event
   */
  handleDragOver: (event: DragEvent): void => {
    event.preventDefault();
    event.stopPropagation();
  },

  /**
   * Handle drop event
   */
  handleDrop: (event: DragEvent): FileList | null => {
    event.preventDefault();
    event.stopPropagation();
    
    const files = event.dataTransfer?.files;
    return files || null;
  },

  /**
   * Check if dragged items contain files
   */
  hasFiles: (event: DragEvent): boolean => {
    return Array.from(event.dataTransfer?.types || []).includes('Files');
  },
};

export default {
  fileUploadUtils,
  agriculturalFileValidators,
  uploadHandlers,
  dragDropUtils,
  FILE_TYPES,
  FILE_SIZE_LIMITS,
}; 