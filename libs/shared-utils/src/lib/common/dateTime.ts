/**
 * Date/Time Utilities for BeFarma Agricultural Platform
 * Specialized functions for agricultural scheduling, seasons, and crop lifecycle management
 */

export enum Season {
  KHARIF = 'kharif',     // Monsoon season (June-November)
  RABI = 'rabi',         // Winter season (November-April)
  ZAID = 'zaid',         // Summer season (March-June)
}

export enum CropStage {
  PLANTING = 'planting',
  GERMINATION = 'germination',
  VEGETATIVE = 'vegetative',
  FLOWERING = 'flowering',
  FRUITING = 'fruiting',
  MATURATION = 'maturation',
  HARVEST = 'harvest',
}

export interface CropCalendar {
  season: Season;
  plantingStart: Date;
  plantingEnd: Date;
  harvestStart: Date;
  harvestEnd: Date;
  duration: number; // in days
}

export interface WeatherWindow {
  start: Date;
  end: Date;
  description: string;
  suitable: boolean;
}

/**
 * Basic date utilities
 */
export const dateUtils = {
  /**
   * Format date to Indian format (DD/MM/YYYY)
   */
  formatIndian: (date: Date): string => {
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  },

  /**
   * Format date and time for agricultural logs
   */
  formatDateTime: (date: Date): string => {
    return date.toLocaleString('en-IN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  },

  /**
   * Format date for API usage (ISO string)
   */
  formatISO: (date: Date): string => {
    return date.toISOString();
  },

  /**
   * Parse date from various formats
   */
  parseDate: (dateString: string): Date | null => {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  },

  /**
   * Add days to a date
   */
  addDays: (date: Date, days: number): Date => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  },

  /**
   * Add months to a date
   */
  addMonths: (date: Date, months: number): Date => {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
  },

  /**
   * Calculate difference between dates in days
   */
  daysDifference: (startDate: Date, endDate: Date): number => {
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  },

  /**
   * Check if date is today
   */
  isToday: (date: Date): boolean => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  },

  /**
   * Check if date is in the past
   */
  isPast: (date: Date): boolean => {
    return date < new Date();
  },

  /**
   * Check if date is in the future
   */
  isFuture: (date: Date): boolean => {
    return date > new Date();
  },

  /**
   * Get start of day
   */
  startOfDay: (date: Date): Date => {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
  },

  /**
   * Get end of day
   */
  endOfDay: (date: Date): Date => {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
  },
};

/**
 * Agricultural season utilities
 */
export const seasonUtils = {
  /**
   * Get current agricultural season based on date
   */
  getCurrentSeason: (date: Date = new Date()): Season => {
    const month = date.getMonth() + 1; // 1-based month
    
    if (month >= 6 && month <= 11) {
      return Season.KHARIF; // Monsoon season
    } else if (month >= 11 || month <= 4) {
      return Season.RABI; // Winter season
    } else {
      return Season.ZAID; // Summer season
    }
  },

  /**
   * Get season name in local language
   */
  getSeasonName: (season: Season): string => {
    const names = {
      [Season.KHARIF]: 'खरीफ (Kharif)',
      [Season.RABI]: 'रबी (Rabi)',
      [Season.ZAID]: 'जायद (Zaid)',
    };
    return names[season];
  },

  /**
   * Get season date ranges for a given year
   */
  getSeasonDates: (year: number, season: Season): { start: Date; end: Date } => {
    switch (season) {
      case Season.KHARIF:
        return {
          start: new Date(year, 5, 1), // June 1
          end: new Date(year, 10, 30), // November 30
        };
      case Season.RABI:
        return {
          start: new Date(year, 10, 1), // November 1
          end: new Date(year + 1, 3, 30), // April 30 next year
        };
      case Season.ZAID:
        return {
          start: new Date(year, 2, 1), // March 1
          end: new Date(year, 5, 30), // June 30
        };
      default:
        throw new Error('Invalid season');
    }
  },

  /**
   * Check if date falls within a season
   */
  isDateInSeason: (date: Date, season: Season): boolean => {
    const year = date.getFullYear();
    const seasonDates = seasonUtils.getSeasonDates(year, season);
    return date >= seasonDates.start && date <= seasonDates.end;
  },

  /**
   * Get recommended planting window for season
   */
  getPlantingWindow: (year: number, season: Season): { start: Date; end: Date } => {
    switch (season) {
      case Season.KHARIF:
        return {
          start: new Date(year, 5, 15), // June 15
          end: new Date(year, 6, 31), // July 31
        };
      case Season.RABI:
        return {
          start: new Date(year, 10, 15), // November 15
          end: new Date(year, 11, 31), // December 31
        };
      case Season.ZAID:
        return {
          start: new Date(year, 1, 15), // February 15
          end: new Date(year, 2, 31), // March 31
        };
      default:
        throw new Error('Invalid season');
    }
  },
};

/**
 * Crop lifecycle utilities
 */
export const cropUtils = {
  /**
   * Calculate harvest date based on planting date and crop duration
   */
  calculateHarvestDate: (plantingDate: Date, cropDurationDays: number): Date => {
    return dateUtils.addDays(plantingDate, cropDurationDays);
  },

  /**
   * Calculate crop stage based on planting date and current date
   */
  getCurrentCropStage: (plantingDate: Date, cropDurationDays: number, currentDate: Date = new Date()): CropStage => {
    const daysSincePlanting = dateUtils.daysDifference(plantingDate, currentDate);
    const progressPercentage = (daysSincePlanting / cropDurationDays) * 100;

    if (progressPercentage < 0) return CropStage.PLANTING;
    if (progressPercentage <= 10) return CropStage.GERMINATION;
    if (progressPercentage <= 40) return CropStage.VEGETATIVE;
    if (progressPercentage <= 60) return CropStage.FLOWERING;
    if (progressPercentage <= 80) return CropStage.FRUITING;
    if (progressPercentage <= 95) return CropStage.MATURATION;
    return CropStage.HARVEST;
  },

  /**
   * Get crop stage duration in days
   */
  getCropStageDuration: (stage: CropStage, totalDuration: number): number => {
    const stagePercentages = {
      [CropStage.PLANTING]: 0,
      [CropStage.GERMINATION]: 10,
      [CropStage.VEGETATIVE]: 30,
      [CropStage.FLOWERING]: 20,
      [CropStage.FRUITING]: 20,
      [CropStage.MATURATION]: 15,
      [CropStage.HARVEST]: 5,
    };
    
    return Math.round((stagePercentages[stage] / 100) * totalDuration);
  },

  /**
   * Get days remaining until harvest
   */
  getDaysUntilHarvest: (plantingDate: Date, cropDurationDays: number, currentDate: Date = new Date()): number => {
    const harvestDate = cropUtils.calculateHarvestDate(plantingDate, cropDurationDays);
    const daysRemaining = dateUtils.daysDifference(currentDate, harvestDate);
    return Math.max(0, daysRemaining);
  },

  /**
   * Check if crop is ready for harvest
   */
  isReadyForHarvest: (plantingDate: Date, cropDurationDays: number, currentDate: Date = new Date()): boolean => {
    const daysSincePlanting = dateUtils.daysDifference(plantingDate, currentDate);
    return daysSincePlanting >= cropDurationDays;
  },

  /**
   * Get crop progress percentage
   */
  getCropProgress: (plantingDate: Date, cropDurationDays: number, currentDate: Date = new Date()): number => {
    const daysSincePlanting = dateUtils.daysDifference(plantingDate, currentDate);
    return Math.min(100, Math.max(0, (daysSincePlanting / cropDurationDays) * 100));
  },

  /**
   * Generate crop calendar for a season
   */
  generateCropCalendar: (
    season: Season,
    year: number,
    cropDurationDays: number
  ): CropCalendar => {
    const plantingWindow = seasonUtils.getPlantingWindow(year, season);
    const harvestStart = dateUtils.addDays(plantingWindow.start, cropDurationDays);
    const harvestEnd = dateUtils.addDays(plantingWindow.end, cropDurationDays);

    return {
      season,
      plantingStart: plantingWindow.start,
      plantingEnd: plantingWindow.end,
      harvestStart,
      harvestEnd,
      duration: cropDurationDays,
    };
  },
};

/**
 * Weather and irrigation utilities
 */
export const weatherUtils = {
  /**
   * Check if it's monsoon season
   */
  isMonsoonSeason: (date: Date = new Date()): boolean => {
    const month = date.getMonth() + 1;
    return month >= 6 && month <= 9; // June to September
  },

  /**
   * Get irrigation schedule based on season and crop stage
   */
  getIrrigationSchedule: (season: Season, cropStage: CropStage): number => {
    // Returns days between irrigation
    const schedules = {
      [Season.KHARIF]: {
        [CropStage.PLANTING]: 1,
        [CropStage.GERMINATION]: 2,
        [CropStage.VEGETATIVE]: 3,
        [CropStage.FLOWERING]: 2,
        [CropStage.FRUITING]: 2,
        [CropStage.MATURATION]: 4,
        [CropStage.HARVEST]: 7,
      },
      [Season.RABI]: {
        [CropStage.PLANTING]: 2,
        [CropStage.GERMINATION]: 3,
        [CropStage.VEGETATIVE]: 5,
        [CropStage.FLOWERING]: 3,
        [CropStage.FRUITING]: 3,
        [CropStage.MATURATION]: 7,
        [CropStage.HARVEST]: 10,
      },
      [Season.ZAID]: {
        [CropStage.PLANTING]: 1,
        [CropStage.GERMINATION]: 1,
        [CropStage.VEGETATIVE]: 2,
        [CropStage.FLOWERING]: 1,
        [CropStage.FRUITING]: 2,
        [CropStage.MATURATION]: 3,
        [CropStage.HARVEST]: 5,
      },
    };

    return schedules[season]?.[cropStage] || 3; // Default to 3 days
  },

  /**
   * Calculate next irrigation date
   */
  getNextIrrigationDate: (
    lastIrrigationDate: Date,
    season: Season,
    cropStage: CropStage
  ): Date => {
    const intervalDays = weatherUtils.getIrrigationSchedule(season, cropStage);
    return dateUtils.addDays(lastIrrigationDate, intervalDays);
  },

  /**
   * Check if irrigation is due
   */
  isIrrigationDue: (
    lastIrrigationDate: Date,
    season: Season,
    cropStage: CropStage,
    currentDate: Date = new Date()
  ): boolean => {
    const nextIrrigationDate = weatherUtils.getNextIrrigationDate(
      lastIrrigationDate,
      season,
      cropStage
    );
    return currentDate >= nextIrrigationDate;
  },
};

/**
 * Market and business utilities
 */
export const marketUtils = {
  /**
   * Get market days (typically specific days of the week)
   */
  getMarketDays: (): number[] => {
    return [2, 5]; // Tuesday (2) and Friday (5), 0 = Sunday
  },

  /**
   * Get next market day
   */
  getNextMarketDay: (fromDate: Date = new Date()): Date => {
    const marketDays = marketUtils.getMarketDays();
    const nextDate = new Date(fromDate);
    nextDate.setDate(nextDate.getDate() + 1);

    while (!marketDays.includes(nextDate.getDay())) {
      nextDate.setDate(nextDate.getDate() + 1);
    }

    return nextDate;
  },

  /**
   * Check if date is a market day
   */
  isMarketDay: (date: Date): boolean => {
    const marketDays = marketUtils.getMarketDays();
    return marketDays.includes(date.getDay());
  },

  /**
   * Calculate optimal harvest timing for market
   */
  getOptimalHarvestDate: (
    naturalHarvestDate: Date,
    marketPreference: 'immediate' | 'next-market' | 'best-price'
  ): Date => {
    switch (marketPreference) {
      case 'immediate':
        return naturalHarvestDate;
      case 'next-market':
        return marketUtils.getNextMarketDay(naturalHarvestDate);
      case 'best-price':
        // Typically 2-3 days before peak season for better prices
        return dateUtils.addDays(naturalHarvestDate, -3);
      default:
        return naturalHarvestDate;
    }
  },
};

/**
 * Utility for relative time formatting (like "2 days ago", "in 3 weeks")
 */
export const relativeTimeUtils = {
  /**
   * Get relative time string
   */
  getRelativeTime: (date: Date, fromDate: Date = new Date()): string => {
    const diffMs = date.getTime() - fromDate.getTime();
    const diffDays = Math.round(diffMs / (1000 * 60 * 60 * 24));
    const absDays = Math.abs(diffDays);

    if (absDays === 0) return 'Today';
    if (absDays === 1) return diffDays > 0 ? 'Tomorrow' : 'Yesterday';
    if (absDays < 7) return `${absDays} days ${diffDays > 0 ? 'from now' : 'ago'}`;
    if (absDays < 30) {
      const weeks = Math.round(absDays / 7);
      return `${weeks} week${weeks > 1 ? 's' : ''} ${diffDays > 0 ? 'from now' : 'ago'}`;
    }
    if (absDays < 365) {
      const months = Math.round(absDays / 30);
      return `${months} month${months > 1 ? 's' : ''} ${diffDays > 0 ? 'from now' : 'ago'}`;
    }
    
    const years = Math.round(absDays / 365);
    return `${years} year${years > 1 ? 's' : ''} ${diffDays > 0 ? 'from now' : 'ago'}`;
  },

  /**
   * Get time until date
   */
  getTimeUntil: (targetDate: Date, fromDate: Date = new Date()): string => {
    const diffMs = targetDate.getTime() - fromDate.getTime();
    
    if (diffMs <= 0) return 'Overdue';
    
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  },
};

export default {
  dateUtils,
  seasonUtils,
  cropUtils,
  weatherUtils,
  marketUtils,
  relativeTimeUtils,
  Season,
  CropStage,
}; 