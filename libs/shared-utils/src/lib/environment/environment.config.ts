/**
 * Environment Configuration
 * Centralized environment management for BeFarma platform
 */

export interface EnvironmentConfig {
  production: boolean;
  apiUrl: string;
  services: {
    sellerService: string;
    adminService: string;
    farmService: string;
    cropService: string;
    plotService: string;
    orderService: string;
    financialService: string;
    inventoryService: string;
    analyticsService: string;
    notificationService: string;
  };
  features: {
    enableDevTools: boolean;
    enableLogging: boolean;
    enableAnalytics: boolean;
  };
  auth: {
    tokenKey: string;
    refreshTokenKey: string;
    tokenExpiry: number;
  };
}

// Development Environment Configuration
export const developmentConfig: EnvironmentConfig = {
  production: false,
  apiUrl: process.env['REACT_APP_API_URL'] || 'http://localhost:3000',
  services: {
    // ✅ UPDATED: Direct microservice URLs matching postman collections exactly
    // Updated to match the exact base URLs from postman collections for direct microservice access
    sellerService: process.env['REACT_APP_SELLER_SERVICE_URL'] || 'http://localhost:3001/api/v1/sellers',
    adminService: process.env['REACT_APP_ADMIN_SERVICE_URL'] || 'http://localhost:3002/api/v1',
    analyticsService: process.env['REACT_APP_ANALYTICS_SERVICE_URL'] || 'http://localhost:3003/api/analytics',
    cropService: process.env['REACT_APP_CROP_SERVICE_URL'] || 'http://localhost:3004/api/v1/crops',
    farmService: process.env['REACT_APP_FARM_SERVICE_URL'] || 'http://localhost:3005/api/farms',
    financialService: process.env['REACT_APP_FINANCIAL_SERVICE_URL'] || 'http://localhost:3006/api/v1',
    inventoryService: process.env['REACT_APP_INVENTORY_SERVICE_URL'] || 'http://localhost:3007/api/v1',
    notificationService: process.env['REACT_APP_NOTIFICATION_SERVICE_URL'] || 'http://localhost:3008/api/notifications',
    orderService: process.env['REACT_APP_ORDER_SERVICE_URL'] || 'http://localhost:3009/api/orders',
    plotService: process.env['REACT_APP_PLOT_SERVICE_URL'] || 'http://localhost:3010/api/v1',
  },
  features: {
    enableDevTools: true,
    enableLogging: true,
    enableAnalytics: false,
  },
  auth: {
    tokenKey: 'befarma_auth_token',
    refreshTokenKey: 'befarma_refresh_token',
    tokenExpiry: 3600000, // 1 hour in milliseconds
  },
};

// Production Environment Configuration
export const productionConfig: EnvironmentConfig = {
  production: true,
  apiUrl: process.env['REACT_APP_API_URL'] || 'https://api.befarma.com',
  services: {
    // ✅ UPDATED: Production URLs matching postman collections exactly
    sellerService: process.env['REACT_APP_SELLER_SERVICE_URL'] || 'https://seller.befarma.com/api/v1/sellers',
    adminService: process.env['REACT_APP_ADMIN_SERVICE_URL'] || 'https://admin.befarma.com/api/v1',
    analyticsService: process.env['REACT_APP_ANALYTICS_SERVICE_URL'] || 'https://analytics.befarma.com/api/analytics',
    cropService: process.env['REACT_APP_CROP_SERVICE_URL'] || 'https://crop.befarma.com/api/v1/crops',
    farmService: process.env['REACT_APP_FARM_SERVICE_URL'] || 'https://farm.befarma.com/api/farms',
    financialService: process.env['REACT_APP_FINANCIAL_SERVICE_URL'] || 'https://financial.befarma.com/api/v1',
    inventoryService: process.env['REACT_APP_INVENTORY_SERVICE_URL'] || 'https://inventory.befarma.com/api/v1',
    notificationService: process.env['REACT_APP_NOTIFICATION_SERVICE_URL'] || 'https://notification.befarma.com/api/notifications',
    orderService: process.env['REACT_APP_ORDER_SERVICE_URL'] || 'https://order.befarma.com/api/orders',
    plotService: process.env['REACT_APP_PLOT_SERVICE_URL'] || 'https://plot.befarma.com/api/v1',
  },
  features: {
    enableDevTools: false,
    enableLogging: false,
    enableAnalytics: true,
  },
  auth: {
    tokenKey: 'befarma_auth_token',
    refreshTokenKey: 'befarma_refresh_token',
    tokenExpiry: 3600000, // 1 hour in milliseconds
  },
};

// Get current environment configuration
export const getEnvironmentConfig = (): EnvironmentConfig => {
  const isProduction = process.env['NODE_ENV'] === 'production';
  return isProduction ? productionConfig : developmentConfig;
};

// Export current environment
export const environment = getEnvironmentConfig();
