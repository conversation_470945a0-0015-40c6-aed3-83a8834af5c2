/**
 * Environment Types
 * TypeScript definitions for environment variables and configurations
 */

// Environment variable types
export interface ProcessEnv {
  NODE_ENV: 'development' | 'production' | 'test';

  // API Configuration
  REACT_APP_API_URL?: string;

  // Microservice URLs (aligned with development planning phases)
  REACT_APP_SELLER_SERVICE_URL?: string; // Phase 3.1, 3.2
  REACT_APP_ADMIN_SERVICE_URL?: string; // Phase 8.1-8.4
  REACT_APP_FARM_SERVICE_URL?: string; // Phase 3.3
  REACT_APP_CROP_SERVICE_URL?: string; // Phase 4.1-4.4
  REACT_APP_PLOT_SERVICE_URL?: string; // Phase 3.4
  REACT_APP_ORDER_SERVICE_URL?: string; // Phase 5.1
  REACT_APP_FINANCIAL_SERVICE_URL?: string; // Phase 5.2
  REACT_APP_INVENTORY_SERVICE_URL?: string; // Phase 5.3
  REACT_APP_ANALYTICS_SERVICE_URL?: string; // Phase 6.1, 6.2
  REACT_APP_NOTIFICATION_SERVICE_URL?: string; // Phase 7.1, 7.2

  // Feature Flags
  REACT_APP_ENABLE_DEV_TOOLS?: string;
  REACT_APP_ENABLE_LOGGING?: string;
  REACT_APP_ENABLE_ANALYTICS?: string;

  // Authentication Configuration
  REACT_APP_AUTH_TOKEN_KEY?: string;
  REACT_APP_AUTH_REFRESH_TOKEN_KEY?: string;
  REACT_APP_AUTH_TOKEN_EXPIRY?: string;

  // External Services
  REACT_APP_MAPBOX_ACCESS_TOKEN?: string; // For GPS/mapping features (Phase 3.3)
  REACT_APP_WEATHER_API_KEY?: string; // For weather integration (Phase 4.4)

  // Payment Configuration (Phase 5.2)
  REACT_APP_PAYMENT_GATEWAY_KEY?: string;
  REACT_APP_PAYMENT_GATEWAY_SECRET?: string;

  // Analytics & Monitoring
  REACT_APP_ANALYTICS_ID?: string;
  REACT_APP_SENTRY_DSN?: string;
}

// Application environment modes
export type AppEnvironment = 'development' | 'production' | 'staging' | 'test';

// Service ports for development (matching backend architecture)
export const SERVICE_PORTS = {
  SELLER_SERVICE: 3001,
  ADMIN_SERVICE: 3002,
  ANALYTICS_SERVICE: 3003,
  CROP_SERVICE: 3004,
  FARM_SERVICE: 3005,
  FINANCIAL_SERVICE: 3006,
  INVENTORY_SERVICE: 3007,
  NOTIFICATION_SERVICE: 3008,
  ORDER_SERVICE: 3009,
  PLOT_SERVICE: 3010,
} as const;

// Feature flag types
export interface FeatureFlags {
  enableDevTools: boolean;
  enableLogging: boolean;
  enableAnalytics: boolean;
  enableExperimentalFeatures: boolean;
  enableOfflineMode: boolean; // Phase 9.1
}

// Build configuration types
export interface BuildConfig {
  version: string;
  buildNumber: string;
  buildDate: string;
  gitHash: string;
}

// API endpoint types
export interface ApiEndpoints {
  baseUrl: string;
  seller: string;
  admin: string;
  farm: string;
  crop: string;
  plot: string;
  order: string;
  financial: string;
  inventory: string;
  analytics: string;
  notification: string;
}

// Export utility type for environment validation
export type RequiredEnvVars = keyof ProcessEnv;
