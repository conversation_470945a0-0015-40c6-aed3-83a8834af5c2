/**
 * Admin Service Types
 * TypeScript interfaces for admin service API responses matching postman admin-service.json
 */

// Base response interface
export interface AdminApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp?: string;
}

// Pagination interface
export interface AdminPaginationParams {
  page?: number;
  limit?: number;
}

// ✅ UPDATED: Pagination response matching actual API structure
export interface AdminSellersListData {
  sellers: AdminSeller[];
  total: number;
  page: number;
  totalPages: number;
}

export interface AdminPaginatedResponse<T> extends AdminApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication Types
export interface AdminLoginRequest {
  email: string;
  password: string;
}

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'superadmin';
  permissions: string[];
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminLoginResponse extends AdminApiResponse<{
  admin: AdminUser;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}> {}

// Dashboard & Analytics Types
export interface SystemHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    [serviceName: string]: {
      status: 'up' | 'down' | 'degraded';
      responseTime?: number;
      lastCheck: string;
    };
  };
  uptime: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
}

export interface SystemStats {
  totalUsers: number;
  totalSellers: number;
  totalFarms: number;
  totalCrops: number;
  totalOrders: number;
  revenue: {
    today: number;
    thisMonth: number;
    thisYear: number;
  };
  growth: {
    users: number;
    sellers: number;
    revenue: number;
  };
}

export interface AdminDashboard {
  stats: SystemStats;
  recentActivity: {
    id: string;
    type: 'seller_registration' | 'farm_verification' | 'crop_approval' | 'order_placed';
    description: string;
    timestamp: string;
    userId?: string;
    metadata?: Record<string, any>;
  }[];
  alerts: {
    id: string;
    type: 'warning' | 'error' | 'info';
    title: string;
    message: string;
    timestamp: string;
    resolved: boolean;
  }[];
}

// ✅ UPDATED: Seller Management Types matching actual API response
export interface AdminSellerPersonalInfo {
  address: {
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2?: string;
  };
  name: string;
  contact: string;
  email: string;
}

export interface AdminSellerDocuments {
  identityProof: string;
  landOwnership: string;
  certifications: string[];
}

export interface AdminSellerBankDetails {
  accountNumber: string;
  bankName: string;
  ifscCode: string;
}

export interface AdminSeller {
  _id: string;
  sellerId: string;
  personalInfo: AdminSellerPersonalInfo;
  documents: AdminSellerDocuments;
  bankDetails: AdminSellerBankDetails;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  farms: string[]; // Farm IDs
  createdAt: string;
  updatedAt: string;
  statusHistory: any[];
  __v: number;
}

export interface AdminSellersListParams extends AdminPaginationParams {
  verified?: boolean;
  state?: string;
  status?: string;
  search?: string;
}

export interface AdminVerifySellerRequest {
  verified: boolean;
  verificationNotes: string;
  verifiedBy: string;
}

export interface AdminUpdateSellerRequest {
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  notes?: string;
}

export interface AdminSuspendSellerRequest {
  status: 'SUSPENDED';
  reason: string;
  suspendedBy: string;
}

// Farm Management Types
export interface AdminFarm {
  farmId: string;
  name: string;
  sellerId: string;
  sellerName: string;
  location: {
    address: {
      village: string;
      district: string;
      state: string;
      pincode: string;
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  totalArea: number;
  soilType: string;
  irrigationType: string;
  verified: boolean;
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  documents?: {
    id: string;
    type: string;
    url: string;
    status: 'pending' | 'verified' | 'rejected';
  }[];
  crops?: string[]; // Crop IDs
  createdAt: string;
  updatedAt: string;
  verifiedBy?: string;
  verificationNotes?: string;
}

export interface AdminFarmsListParams extends AdminPaginationParams {
  verified?: boolean;
  state?: string;
  district?: string;
}

export interface AdminVerifyFarmRequest {
  verified: boolean;
  verificationNotes: string;
  verifiedBy: string;
}

// Crop Management Types
export interface AdminCrop {
  cropId: string;
  name: string;
  variety: string;
  farmId: string;
  farmName: string;
  sellerId: string;
  sellerName: string;
  status: 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED' | 'ACTIVE' | 'HARVESTED';
  plantingDate: string;
  expectedHarvestDate: string;
  quantity: number;
  unit: string;
  pricePerUnit: number;
  totalValue: number;
  qualityGrade?: string;
  certifications?: string[];
  images?: string[];
  createdAt: string;
  updatedAt: string;
  approvedBy?: string;
  approvalNotes?: string;
}

export interface AdminCropsListParams extends AdminPaginationParams {
  status?: string;
  farmId?: string;
  sellerId?: string;
}

export interface AdminApproveCropRequest {
  status: 'APPROVED' | 'REJECTED';
  approvalNotes: string;
  approvedBy: string;
}

// ✅ UPDATED: API Response Types matching actual API structure
export type AdminDashboardResponse = AdminApiResponse<AdminDashboard>;
export type AdminSystemHealthResponse = AdminApiResponse<SystemHealthStatus>;
export type AdminSystemStatsResponse = AdminApiResponse<SystemStats>;
export type AdminSellersListResponse = AdminApiResponse<AdminSellersListData>;
export type AdminSellerResponse = AdminApiResponse<AdminSeller>;
export type AdminFarmsListResponse = AdminPaginatedResponse<AdminFarm>;
export type AdminFarmResponse = AdminApiResponse<AdminFarm>;
export type AdminCropsListResponse = AdminPaginatedResponse<AdminCrop>;
export type AdminCropResponse = AdminApiResponse<AdminCrop>;