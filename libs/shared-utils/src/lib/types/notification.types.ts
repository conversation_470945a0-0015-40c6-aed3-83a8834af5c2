/**
 * Notification Service Types
 * Based on API documentation from apidocs/services/notification-service/README.md
 */

export interface NotificationRecipient {
  userId: string;
  userType: 'seller' | 'admin' | 'buyer';
  email?: string;
  phone?: string;
  deviceTokens?: string[];
}

export interface NotificationContent {
  title: string;
  message: string;
  body?: string;
  imageUrl?: string;
  actionUrl?: string;
  actionText?: string;
  data?: Record<string, any>;
}

export interface NotificationTemplate {
  templateId: string;
  name: string;
  type: 'email' | 'sms' | 'push' | 'in_app';
  subject?: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Notification {
  notificationId: string;
  type: 'order' | 'payment' | 'crop' | 'farm' | 'system' | 'marketing' | 'alert';
  category: 'info' | 'success' | 'warning' | 'error' | 'promotional';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  recipient: NotificationRecipient;
  content: NotificationContent;
  channels: Array<'email' | 'sms' | 'push' | 'in_app'>;
  status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed';
  scheduledAt?: string;
  sentAt?: string;
  deliveredAt?: string;
  readAt?: string;
  failureReason?: string;
  retryCount: number;
  maxRetries: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPreferences {
  userId: string;
  userType: 'seller' | 'admin' | 'buyer';
  preferences: {
    email: {
      enabled: boolean;
      types: string[];
      frequency: 'immediate' | 'daily' | 'weekly';
    };
    sms: {
      enabled: boolean;
      types: string[];
      urgentOnly: boolean;
    };
    push: {
      enabled: boolean;
      types: string[];
      quietHours?: {
        start: string;
        end: string;
      };
    };
    inApp: {
      enabled: boolean;
      types: string[];
      showBadge: boolean;
    };
  };
  timezone: string;
  language: string;
  createdAt: string;
  updatedAt: string;
}

export interface BulkNotification {
  bulkId: string;
  title: string;
  content: NotificationContent;
  recipients: NotificationRecipient[];
  channels: Array<'email' | 'sms' | 'push' | 'in_app'>;
  filters?: {
    userType?: string[];
    location?: {
      states?: string[];
      cities?: string[];
    };
    tags?: string[];
  };
  scheduledAt?: string;
  status: 'draft' | 'scheduled' | 'sending' | 'completed' | 'failed';
  stats: {
    totalRecipients: number;
    sent: number;
    delivered: number;
    failed: number;
    opened?: number;
    clicked?: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface NotificationCampaign {
  campaignId: string;
  name: string;
  description?: string;
  type: 'promotional' | 'transactional' | 'informational';
  targetAudience: {
    userTypes: string[];
    segments: string[];
    filters: Record<string, any>;
  };
  content: NotificationContent;
  channels: Array<'email' | 'sms' | 'push' | 'in_app'>;
  schedule: {
    type: 'immediate' | 'scheduled' | 'recurring';
    scheduledAt?: string;
    recurringPattern?: {
      frequency: 'daily' | 'weekly' | 'monthly';
      interval: number;
      endDate?: string;
    };
  };
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  analytics: {
    totalSent: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    conversionRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Request Types
export interface SendNotificationRequest {
  type: Notification['type'];
  category: Notification['category'];
  priority: Notification['priority'];
  recipient: NotificationRecipient;
  content: NotificationContent;
  channels: Array<'email' | 'sms' | 'push' | 'in_app'>;
  scheduledAt?: string;
  templateId?: string;
  templateVariables?: Record<string, any>;
}

export interface BulkNotificationRequest {
  title: string;
  content: NotificationContent;
  recipients?: NotificationRecipient[];
  filters?: BulkNotification['filters'];
  channels: Array<'email' | 'sms' | 'push' | 'in_app'>;
  scheduledAt?: string;
  templateId?: string;
  templateVariables?: Record<string, any>;
}

export interface NotificationPreferencesUpdateRequest {
  preferences: Partial<NotificationPreferences['preferences']>;
  timezone?: string;
  language?: string;
}

export interface CreateTemplateRequest {
  name: string;
  type: 'email' | 'sms' | 'push' | 'in_app';
  subject?: string;
  content: string;
  variables: string[];
}

export interface UpdateTemplateRequest {
  name?: string;
  subject?: string;
  content?: string;
  variables?: string[];
  isActive?: boolean;
}

// Search and Filter Types
export interface NotificationFilters {
  type?: Notification['type'];
  category?: Notification['category'];
  priority?: Notification['priority'];
  status?: Notification['status'];
  userId?: string;
  userType?: string;
  channels?: string[];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

export interface NotificationSearchParams {
  filters?: NotificationFilters;
  sort?: {
    field: 'createdAt' | 'sentAt' | 'priority' | 'status';
    order: 'asc' | 'desc';
  };
  page?: number;
  limit?: number;
}

// Analytics Types
export interface NotificationAnalytics {
  overview: {
    totalSent: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
  };
  byChannel: Array<{
    channel: string;
    sent: number;
    delivered: number;
    failed: number;
    deliveryRate: number;
  }>;
  byType: Array<{
    type: string;
    count: number;
    deliveryRate: number;
    engagementRate: number;
  }>;
  trends: Array<{
    date: string;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
  topPerforming: Array<{
    templateId: string;
    templateName: string;
    sent: number;
    openRate: number;
    clickRate: number;
  }>;
}

// API Response Types
export interface NotificationResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  error?: {
    code: string;
    message: string;
  };
}

export interface NotificationsListResponse {
  notifications: Notification[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  unreadCount: number;
}

export interface NotificationStatsResponse {
  total: number;
  unread: number;
  byType: Record<string, number>;
  byStatus: Record<string, number>;
  recent: Notification[];
}
