export interface FarmDetails {
  id: string;
  name: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  photos: string[]; 
  createdAt: Date;
  updatedAt: Date;
}


export interface FarmPhoto {
  id: string;
  farmId: string;
  url: string;
  caption?: string;
  createdAt: Date;
}

// Additional types for farm service
export interface FarmCreateRequest {
  name: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure?: string[];
  certifications?: string[];
}

export interface FarmUpdateRequest {
  name?: string;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  totalArea?: number;
  soilType?: string;
  waterSource?: string;
  infrastructure?: string[];
  certifications?: string[];
}

export interface FarmSearchParams {
  query?: string;
  location?: {
    latitude?: number;
    longitude?: number;
    radius?: number;
  };
  soilType?: string;
  waterSource?: string;
  minArea?: number;
  maxArea?: number;
  page?: number;
  limit?: number;
}