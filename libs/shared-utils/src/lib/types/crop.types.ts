/**
 * Crop Service Types
 * Updated to match crop-service.json postman collection structure
 */

// Soil Conditions Interface (from postman collection)
export interface SoilConditions {
  type: 'LOAMY' | 'CLAY' | 'SANDY' | 'SILT' | 'PEAT' | 'CHALK';
  ph: number;
  nutrients: ('NITROGEN' | 'PHOSPHORUS' | 'POTASSIUM' | 'CALCIUM' | 'MAGNESIUM' | 'SULFUR')[];
}

// Crop Metadata (from postman collection)
export interface CropMetadata {
  cropCategory: 'VEGETABLES' | 'FRUITS' | 'GRAINS' | 'PULSES' | 'SPICES' | 'OILSEEDS' | 'FIBER' | 'FODDER';
  farmingMethod: 'ORGANIC' | 'CONVENTIONAL' | 'HYDROPONIC' | 'GREENHOUSE';
  irrigationMethod: 'DRIP' | 'SPRINKLER' | 'FLOOD' | 'RAIN_FED';
  harvestSeason: 'SUMMER' | 'WINTER' | 'MONSOON' | 'SPRING';
  waterSource: 'BOREWELL' | 'CANAL' | 'RIVER' | 'RAINWATER' | 'TANK';
  seedType: 'HYBRID' | 'TRADITIONAL' | 'GMO';
}

// Cultivation Details (from postman collection)
export interface CropCultivation {
  irrigationNeeds: string;
  fertilizerRequirements: string;
  pestControl: string;
  climateConditions: string;
}

// Yield Information (from postman collection)
export interface CropYield {
  expected: number;
  actual: number;
  unit: 'KG' | 'TONS' | 'QUINTALS';
}

// Resource Usage (from postman collection)
export interface CropResources {
  water: number;
  fertilizer: number;
  pesticides: number;
}

// Health Status (from postman collection)
export interface CropHealth {
  status: 'HEALTHY' | 'DISEASE' | 'PEST_ATTACK' | 'NUTRIENT_DEFICIENCY' | 'WATER_STRESS';
  issues: string[];
  lastCheck: string;
}

// Marketplace Pricing (from postman collection)
export interface MarketplacePricing {
  pricePerKg: number;
  minimumOrder: number;
  bulkDiscounts: Array<{
    quantity: number;
    discountPercent: number;
  }>;
}

// Marketplace Availability (from postman collection)
export interface MarketplaceAvailability {
  quantityAvailable: number;
  availableUntil: string;
}

// Marketplace Delivery (from postman collection)
export interface MarketplaceDelivery {
  methods: ('PICKUP' | 'LOCAL_DELIVERY' | 'SHIPPING')[];
  deliveryRadius: number;
  deliveryCharge: number;
}

// Quality Test Results (from postman collection)
export interface QualityTest {
  testType: 'PESTICIDE_RESIDUE' | 'NUTRITIONAL_ANALYSIS' | 'MOISTURE_CONTENT' | 'ORGANIC_CERTIFICATION';
  result: 'PASS' | 'FAIL';
  details: string;
  testedBy: string;
  testDate: string;
}

// Maintenance Activity (from postman collection)
export interface MaintenanceActivity {
  type: 'IRRIGATION' | 'FERTILIZATION' | 'PEST_CONTROL' | 'PRUNING' | 'HARVESTING';
  date: string;
  description: string;
  performedBy: string;
  cost: number;
  resources: string[];
}

// Additional interfaces to match actual API response
export interface NutritionalInfo {
  vitamins: string[];
}

export interface PostHarvest {
  processingMethods: string[];
}

export interface MaintenanceSchedule {
  irrigation: any[];
  fertilization: any[];
  pestControl: any[];
  inspection: any[];
}

export interface MaintenanceHistory {
  activities: any[];
}

export interface CropMaintenance {
  schedule: MaintenanceSchedule;
  history: MaintenanceHistory;
}

export interface WeatherInfo {
  forecasts: any[];
  alerts: any[];
}

// Updated Crop Metadata to match API response
export interface CropMetadata {
  cropCategory: 'VEGETABLES' | 'FRUITS' | 'GRAINS' | 'PULSES' | 'SPICES' | 'OILSEEDS' | 'FIBER' | 'FODDER';
  farmingMethod: 'ORGANIC' | 'CONVENTIONAL' | 'HYDROPONIC' | 'GREENHOUSE';
  irrigationMethod: 'DRIP' | 'SPRINKLER' | 'FLOOD' | 'RAIN_FED';
  harvestSeason: 'SUMMER' | 'WINTER' | 'MONSOON' | 'SPRING';
  waterSource: 'BOREWELL' | 'CANAL' | 'RIVER' | 'RAINWATER' | 'TANK';
  seedType: 'HYBRID' | 'TRADITIONAL' | 'GMO';
  pestDiseaseStatus?: string;
  pesticideUsage?: string;
}

// Main Crop Interface (updated to match actual API response)
export interface Crop {
  _id: string;
  cropId: string;
  numberOfPlots: number;
  farmId: string;
  sellerId: string;
  name: string;
  type: 'VEGETABLE' | 'FRUIT' | 'GRAIN' | 'PULSE' | 'SPICE' | 'OILSEED' | 'FIBER' | 'FODDER';
  variety: string;
  plantingDate: string;
  expectedHarvestDate: string;
  actualHarvestDate?: string;
  growthStage: 'PLANTING' | 'SEEDLING' | 'VEGETATIVE' | 'FLOWERING' | 'FRUITING' | 'MATURITY' | 'HARVESTED';
  soilConditions: SoilConditions;
  waterAvailability: 'ADEQUATE' | 'LIMITED' | 'ABUNDANT' | 'SCARCE';
  metadata: CropMetadata;
  cultivation: CropCultivation;
  yield: CropYield;
  resources: CropResources;
  health: CropHealth;
  nutritionalInfo: NutritionalInfo;
  postHarvest: PostHarvest;
  maintenance: CropMaintenance;
  weather: WeatherInfo;
  images: string[];
  tags: string[];
  certifications: ('ORGANIC' | 'PESTICIDE_FREE' | 'PREMIUM_QUALITY' | 'FAIR_TRADE')[];
  createdAt: string;
  updatedAt: string;
}

// Crop Create Request (matching exact API structure)
export interface CropCreateRequest {
  numberOfPlots: number;
  farmId: string;
  sellerId: string;
  name: string;
  type: 'VEGETABLE' | 'FRUIT' | 'GRAIN' | 'PULSE' | 'SPICE' | 'OILSEED' | 'FIBER' | 'FODDER';
  variety: string;
  plantingDate: string;
  expectedHarvestDate: string;
  soilConditions: {
    type: 'LOAMY' | 'CLAY' | 'SANDY' | 'SILT' | 'PEAT' | 'CHALK';
    ph: number;
    nutrients: ('NITROGEN' | 'PHOSPHORUS' | 'POTASSIUM' | 'CALCIUM' | 'MAGNESIUM' | 'SULFUR')[];
  };
  waterAvailability: 'ADEQUATE' | 'LIMITED' | 'ABUNDANT' | 'SCARCE';
  metadata: {
    cropCategory: 'VEGETABLES' | 'FRUITS' | 'GRAINS' | 'PULSES' | 'SPICES' | 'OILSEEDS' | 'FIBER' | 'FODDER';
    farmingMethod: 'ORGANIC' | 'CONVENTIONAL' | 'HYDROPONIC' | 'GREENHOUSE';
    irrigationMethod: 'DRIP' | 'SPRINKLER' | 'FLOOD' | 'RAIN_FED';
    harvestSeason: 'SUMMER' | 'WINTER' | 'MONSOON' | 'SPRING';
    waterSource: 'BOREWELL' | 'CANAL' | 'RIVER' | 'RAINWATER' | 'TANK';
    seedType: 'HYBRID' | 'TRADITIONAL' | 'GMO';
  };
  cultivation: {
    irrigationNeeds: string;
    fertilizerRequirements: string;
    pestControl: string;
    climateConditions: string;
  };
  yield: {
    expected: number;
    actual: number;
    unit: 'KG' | 'TONS' | 'QUINTALS';
  };
  resources: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };
  health: {
    status: 'HEALTHY' | 'DISEASE' | 'PEST_ATTACK' | 'NUTRIENT_DEFICIENCY' | 'WATER_STRESS';
    issues: string[];
    lastCheck: string;
  };
  images: string[];
  tags: string[];
  certifications: ('ORGANIC' | 'PESTICIDE_FREE' | 'PREMIUM_QUALITY' | 'FAIR_TRADE')[];
}

// Crop Update Request (matching postman collection structure)
export interface CropUpdateRequest {
  name?: string;
  variety?: string;
  expectedHarvestDate?: string;
  actualHarvestDate?: string;
  growthStage?: Crop['growthStage'];
  yield?: Partial<CropYield>;
  health?: Partial<CropHealth>;
  resources?: Partial<CropResources>;
  tags?: string[];
  certifications?: Crop['certifications'];
}

// Search Parameters (matching postman collection endpoints)
export interface CropSearchParams {
  query?: string;
  sellerId?: string;
  farmId?: string;
  numberOfPlots?: number;
  growthStage?: Crop['growthStage'];
  healthStatus?: CropHealth['status'];
  page?: number;
  limit?: number;
  harvestDateFrom?: string;
  harvestDateTo?: string;
}

// Marketplace Listing (from postman collection)
export interface MarketplaceListing {
  cropId: string;
  title: string;
  description: string;
  pricing: MarketplacePricing;
  availability: MarketplaceAvailability;
  delivery: MarketplaceDelivery;
  images: string[];
}

// Analytics Parameters (from postman collection)
export interface AnalyticsParams {
  sellerId?: string;
  farmId?: string;
  fromDate?: string;
  toDate?: string;
}

// Market Insights Parameters (from postman collection)
export interface MarketInsightsParams {
  category?: 'VEGETABLES' | 'FRUITS' | 'GRAINS' | 'PULSES' | 'SPICES';
  region?: string;
  period?: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

// Price Trends Parameters (from postman collection)
export interface PriceTrendsParams {
  crop?: string;
  period?: '1month' | '3months' | '6months' | '1year';
  region?: string;
}

// Nearby Crops Parameters (from postman collection)
export interface NearbyCropsParams {
  latitude: number;
  longitude: number;
  radius: number;
  available?: boolean;
}

// API Response Types (matching actual API response)
export interface CropResponse<T = any> {
  success: boolean;
  message?: string;
  crop?: T;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
}

export interface CropsListResponse {
  success: boolean;
  data: Crop[];
  pagination: PaginationInfo;
  message?: string;
}

export interface CropSearchResponse {
  success: boolean;
  data: Crop[];
  pagination: PaginationInfo;
  message?: string;
}

// Growth Stage Update Request (from postman collection)
export interface GrowthStageUpdateRequest {
  growthStage: Crop['growthStage'];
  notes?: string;
}

// Health Status Update Request (from postman collection)
export interface HealthStatusUpdateRequest {
  healthStatus: CropHealth['status'];
  issues?: string[];
  notes?: string;
}

// Harvest Record Request (from postman collection)
export interface HarvestRecordRequest {
  actualYield: number;
  harvestDate: string;
  quality: 'PREMIUM' | 'STANDARD' | 'BELOW_STANDARD';
}

// Quality Report Request (from postman collection)
export interface QualityReportRequest {
  cropId: string;
  qualityTests: QualityTest[];
  overallGrade: 'A+' | 'A' | 'B+' | 'B' | 'C';
  certifications: Crop['certifications'];
}
