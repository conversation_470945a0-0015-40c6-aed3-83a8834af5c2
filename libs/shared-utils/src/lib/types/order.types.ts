/**
 * Order Service Types
 * Based on API documentation from apidocs/services/order-service/README.md
 */

export interface OrderAddress {
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  landmark?: string;
  contactPerson?: string;
  contactPhone?: string;
}

export interface OrderItem {
  itemId: string;
  cropId: string;
  cropName: string;
  variety: string;
  quantity: number;
  unit: 'kg' | 'quintal' | 'ton';
  pricePerUnit: number;
  totalPrice: number;
  quality: {
    grade: string;
    qualityScore: number;
    certifications: string[];
  };
  specifications?: Record<string, any>;
}

export interface OrderPayment {
  paymentId: string;
  method: 'card' | 'upi' | 'netbanking' | 'wallet' | 'cod' | 'bank_transfer';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  amount: number;
  currency: 'INR' | 'USD';
  transactionId?: string;
  gatewayResponse?: Record<string, any>;
  paidAt?: string;
  refundedAt?: string;
  refundAmount?: number;
  refundReason?: string;
}

export interface OrderShipping {
  shippingId: string;
  method: 'standard' | 'express' | 'overnight' | 'pickup';
  carrier: string;
  trackingNumber?: string;
  estimatedDeliveryDate: string;
  actualDeliveryDate?: string;
  shippingCost: number;
  status: 'pending' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'failed' | 'returned';
  pickupAddress: OrderAddress;
  deliveryAddress: OrderAddress;
  trackingHistory: Array<{
    status: string;
    location: string;
    timestamp: string;
    description: string;
  }>;
}

export interface OrderTimeline {
  status: string;
  timestamp: string;
  description: string;
  updatedBy: string;
  notes?: string;
}

export interface Order {
  orderId: string;
  orderNumber: string;
  sellerId: string;
  sellerName: string;
  buyerId: string;
  buyerName: string;
  buyerEmail: string;
  buyerPhone: string;
  items: OrderItem[];
  subtotal: number;
  taxes: number;
  shippingCost: number;
  discount: number;
  totalAmount: number;
  currency: 'INR' | 'USD';
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded' | 'disputed';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded' | 'partial_refund';
  shippingStatus: 'pending' | 'picked_up' | 'in_transit' | 'delivered' | 'failed';
  payment: OrderPayment;
  shipping: OrderShipping;
  timeline: OrderTimeline[];
  notes?: string;
  specialInstructions?: string;
  estimatedDeliveryDate: string;
  actualDeliveryDate?: string;
  cancellationReason?: string;
  refundReason?: string;
  disputeReason?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface OrderSummary {
  orderId: string;
  orderNumber: string;
  sellerName: string;
  buyerName: string;
  totalAmount: number;
  status: Order['status'];
  paymentStatus: Order['paymentStatus'];
  itemCount: number;
  createdAt: string;
  estimatedDeliveryDate: string;
}

// Request Types
export interface CreateOrderRequest {
  sellerId: string;
  buyerId: string;
  buyerEmail: string;
  buyerPhone: string;
  items: Array<{
    cropId: string;
    quantity: number;
    unit: string;
    pricePerUnit: number;
    specifications?: Record<string, any>;
  }>;
  shippingAddress: OrderAddress;
  billingAddress?: OrderAddress;
  paymentMethod: OrderPayment['method'];
  shippingMethod: OrderShipping['method'];
  specialInstructions?: string;
  couponCode?: string;
}

export interface UpdateOrderRequest {
  status?: Order['status'];
  paymentStatus?: Order['paymentStatus'];
  shippingStatus?: Order['shippingStatus'];
  notes?: string;
  specialInstructions?: string;
  estimatedDeliveryDate?: string;
  actualDeliveryDate?: string;
  cancellationReason?: string;
  refundReason?: string;
  disputeReason?: string;
}

export interface OrderStatusUpdateRequest {
  status: Order['status'];
  notes?: string;
  notifyCustomer?: boolean;
  estimatedDeliveryDate?: string;
}

export interface ProcessPaymentRequest {
  orderId: string;
  paymentMethod: OrderPayment['method'];
  amount: number;
  paymentDetails?: Record<string, any>;
}

export interface RefundRequest {
  orderId: string;
  amount: number;
  reason: string;
  refundMethod?: 'original' | 'bank_transfer' | 'wallet';
}

export interface ShippingUpdateRequest {
  orderId: string;
  status: OrderShipping['status'];
  trackingNumber?: string;
  carrier?: string;
  location?: string;
  estimatedDeliveryDate?: string;
  actualDeliveryDate?: string;
  notes?: string;
}

// Search and Filter Types
export interface OrderFilters {
  sellerId?: string;
  buyerId?: string;
  status?: Order['status'];
  paymentStatus?: Order['paymentStatus'];
  shippingStatus?: Order['shippingStatus'];
  paymentMethod?: OrderPayment['method'];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  amountRange?: {
    min: number;
    max: number;
  };
  location?: {
    state?: string;
    city?: string;
  };
}

export interface OrderSearchParams {
  query?: string;
  filters?: OrderFilters;
  sort?: {
    field: 'createdAt' | 'totalAmount' | 'status' | 'estimatedDeliveryDate';
    order: 'asc' | 'desc';
  };
  page?: number;
  limit?: number;
}

// Analytics Types
export interface OrderAnalytics {
  overview: {
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    completionRate: number;
  };
  trends: Array<{
    date: string;
    orders: number;
    revenue: number;
    averageOrderValue: number;
  }>;
  statusDistribution: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  paymentAnalytics: {
    byMethod: Array<{
      method: string;
      count: number;
      amount: number;
      successRate: number;
    }>;
    failureReasons: Array<{
      reason: string;
      count: number;
    }>;
  };
  shippingAnalytics: {
    averageDeliveryTime: number;
    onTimeDeliveryRate: number;
    byCarrier: Array<{
      carrier: string;
      orders: number;
      averageDeliveryTime: number;
      successRate: number;
    }>;
  };
  topProducts: Array<{
    cropName: string;
    quantity: number;
    revenue: number;
    orderCount: number;
  }>;
  customerAnalytics: {
    newCustomers: number;
    returningCustomers: number;
    topCustomers: Array<{
      buyerId: string;
      buyerName: string;
      orderCount: number;
      totalSpent: number;
    }>;
  };
}

// API Response Types
export interface OrderResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, string[]>;
  };
}

export interface OrdersListResponse {
  orders: Order[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  summary?: {
    totalRevenue: number;
    averageOrderValue: number;
    statusCounts: Record<string, number>;
  };
}

export interface OrderStatsResponse {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  recentOrders: OrderSummary[];
  statusDistribution: Record<string, number>;
  monthlyTrends: Array<{
    month: string;
    orders: number;
    revenue: number;
  }>;
}
