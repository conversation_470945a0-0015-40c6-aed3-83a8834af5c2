/**
 * Seller Service Types
 * Based on API documentation from apidocs/services/seller/README.md
 */

export interface SellerLocation {
  latitude: number;
  longitude: number;
  address: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    pincode: string;
    country?: string;
  };
}

export interface SellerPersonalInfo {
  fullName: string;
  dateOfBirth: string;
  fatherName: string;
  aadharNumber: string;
}

export interface SellerDocuments {
  identityProof: string;
  addressProof?: string;
  landOwnership: string;
  bankStatement?: string;
  certifications?: string[];
}

export interface SellerKYC {
  kycId: string;
  sellerId: string;
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'VERIFIED' | 'REJECTED';
  submittedAt: string;
  verifiedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  estimatedVerificationTime?: string;
  documents: SellerDocuments;
  personalInfo: SellerPersonalInfo;
}

export interface Seller {
  sellerId: string;
  name: string;
  email: string;
  contact: string;
  location: SellerLocation;
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  kycStatus: 'PENDING' | 'SUBMITTED' | 'VERIFIED' | 'REJECTED';
  registrationDate: string;
  lastLoginDate?: string;
  profileCompleteness: number;
  documents?: SellerDocuments;
  personalInfo?: SellerPersonalInfo;
  createdAt: string;
  updatedAt: string;
}

export interface SellerStats {
  totalFarms: number;
  totalCrops: number;
  totalOrders: number;
  totalRevenue: number;
}

export interface SellerDashboard {
  seller: Seller;
  stats: SellerStats;
  recentOrders: Array<{
    orderId: string;
    buyerName: string;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  upcomingHarvests: Array<{
    cropId: string;
    cropName: string;
    expectedHarvestDate: string;
    expectedYield: number;
  }>;
}

export interface SellerLoginRequest {
  email: string;
  password: string;
}

export interface SellerLoginResponse {
  status: 'success' | 'error';
  data?: {
    token: string;
    seller: Seller;
  };
  error?: {
    code: string;
    message: string;
  };
}

export interface SellerRegistrationRequest {
  name: string;
  email: string;
  password: string;
  contact: string;
  location: SellerLocation;
}

export interface SellerUpdateRequest {
  name?: string;
  phone?: string;
  location?: {
    state: string;
    district: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  preferences?: {
    language: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
}

export interface BankDetailsRequest {
  accountNumber: string;
  bankName: string;
  ifscCode: string;
  accountHolderName: string;
  branchName: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Profile Response Structure (matches actual API response)
export interface SellerProfileResponse {
  _id: string;
  sellerId: string;
  status: string;
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  farms: string[];
  createdAt: string;
  updatedAt: string;
  statusHistory: any[];
  __v: number;
  personalInfo: {
    name: string;
    email: string;
    contact: string;
    address: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2?: string;
    };
  };
  documents: {
    identityProof: string;
    landOwnership: string;
    certifications: string[];
  };
  bankDetails: {
    accountNumber: string;
    bankName: string;
    ifscCode: string;
  };
}

export interface SellerVerificationRequest {
  sellerId: string;
  verificationStatus: 'VERIFIED' | 'REJECTED';
  rejectionReason?: string;
}

export interface SellerKYCSubmissionRequest {
  documents: SellerDocuments;
  personalInfo: SellerPersonalInfo;
}

// API Response Types
export interface SellerResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, string[]>;
  };
}

export interface SellersListResponse {
  sellers: Seller[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and Filter Types
export interface SellerSearchFilters {
  status?: 'PENDING' | 'VERIFIED' | 'REJECTED';
  verificationStatus?: 'PENDING' | 'VERIFIED' | 'REJECTED';
  kycStatus?: 'PENDING' | 'SUBMITTED' | 'VERIFIED' | 'REJECTED';
  location?: {
    state?: string;
    city?: string;
  };
  registrationDateFrom?: string;
  registrationDateTo?: string;
}

export interface SellerSearchParams {
  query?: string;
  filters?: SellerSearchFilters;
  sort?: {
    field: 'name' | 'registrationDate' | 'lastLoginDate' | 'verificationStatus';
    order: 'asc' | 'desc';
  };
  page?: number;
  limit?: number;
}
