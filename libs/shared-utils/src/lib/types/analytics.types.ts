/**
 * Analytics Service Types
 * Based on API documentation from apidocs/services/analytics-service/README.md
 */

export interface TimeSeriesData {
  date: string;
  value: number;
  label?: string;
}

export interface MetricData {
  current: number;
  previous: number;
  change: number;
  changePercentage: number;
  trend: 'up' | 'down' | 'stable';
}

export interface DashboardMetrics {
  totalRevenue: MetricData;
  totalOrders: MetricData;
  totalSellers: MetricData;
  totalCrops: MetricData;
  averageOrderValue: MetricData;
  conversionRate: MetricData;
}

export interface RevenueAnalytics {
  totalRevenue: number;
  monthlyRevenue: TimeSeriesData[];
  revenueByCategory: Array<{
    category: string;
    revenue: number;
    percentage: number;
  }>;
  revenueByState: Array<{
    state: string;
    revenue: number;
    orderCount: number;
  }>;
  topPerformingCrops: Array<{
    cropName: string;
    revenue: number;
    quantity: number;
    averagePrice: number;
  }>;
}

export interface SalesAnalytics {
  totalSales: number;
  salesTrends: TimeSeriesData[];
  salesByChannel: Array<{
    channel: string;
    sales: number;
    percentage: number;
  }>;
  seasonalTrends: Array<{
    season: string;
    sales: number;
    growth: number;
  }>;
  topSellingProducts: Array<{
    productName: string;
    quantitySold: number;
    revenue: number;
  }>;
}

export interface CropAnalytics {
  totalCrops: number;
  cropsByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  cropsByType: Array<{
    type: string;
    count: number;
    averageYield: number;
  }>;
  yieldTrends: TimeSeriesData[];
  qualityMetrics: {
    averageQualityScore: number;
    qualityDistribution: Array<{
      grade: string;
      count: number;
      percentage: number;
    }>;
  };
  harvestCalendar: Array<{
    month: string;
    cropsCount: number;
    expectedYield: number;
  }>;
}

export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newRegistrations: TimeSeriesData[];
  usersByLocation: Array<{
    state: string;
    userCount: number;
    percentage: number;
  }>;
  userEngagement: {
    averageSessionDuration: number;
    averageOrdersPerUser: number;
    retentionRate: number;
  };
  verificationStats: {
    verified: number;
    pending: number;
    rejected: number;
  };
}

export interface MarketAnalytics {
  marketTrends: {
    priceIndex: number;
    priceChange: number;
    demandIndex: number;
    supplyIndex: number;
  };
  priceAnalytics: Array<{
    cropType: string;
    currentPrice: number;
    averagePrice: number;
    priceChange: number;
    volatility: number;
  }>;
  demandSupplyAnalysis: Array<{
    cropType: string;
    demand: number;
    supply: number;
    gap: number;
    opportunity: 'high' | 'medium' | 'low';
  }>;
  seasonalPricing: Array<{
    month: string;
    averagePrice: number;
    priceRange: {
      min: number;
      max: number;
    };
  }>;
}

export interface GeographicAnalytics {
  performanceByState: Array<{
    state: string;
    revenue: number;
    orderCount: number;
    sellerCount: number;
    averageOrderValue: number;
  }>;
  cropDistribution: Array<{
    state: string;
    cropTypes: Array<{
      type: string;
      count: number;
      area: number;
    }>;
  }>;
  logisticsMetrics: Array<{
    route: string;
    averageDeliveryTime: number;
    cost: number;
    volume: number;
  }>;
}

export interface PerformanceAnalytics {
  systemMetrics: {
    apiResponseTime: number;
    uptime: number;
    errorRate: number;
    throughput: number;
  };
  businessMetrics: {
    customerSatisfaction: number;
    orderFulfillmentRate: number;
    averageDeliveryTime: number;
    returnRate: number;
  };
  financialMetrics: {
    grossMargin: number;
    operatingMargin: number;
    customerAcquisitionCost: number;
    customerLifetimeValue: number;
  };
}

export interface AnalyticsReport {
  reportId: string;
  title: string;
  type: 'revenue' | 'sales' | 'crops' | 'users' | 'market' | 'performance' | 'custom';
  dateRange: {
    startDate: string;
    endDate: string;
  };
  filters?: Record<string, any>;
  data: any;
  generatedAt: string;
  generatedBy: string;
  format: 'json' | 'pdf' | 'excel' | 'csv';
  status: 'generating' | 'completed' | 'failed';
}

export interface AnalyticsFilters {
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  location?: {
    states?: string[];
    districts?: string[];
  };
  cropTypes?: string[];
  sellerIds?: string[];
  orderStatus?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
}

export interface AnalyticsRequest {
  type: 'dashboard' | 'revenue' | 'sales' | 'crops' | 'users' | 'market' | 'performance';
  filters?: AnalyticsFilters;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  metrics?: string[];
}

// API Response Types
export interface AnalyticsResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  metadata?: {
    generatedAt: string;
    processingTime: number;
    dataPoints: number;
  };
}

export interface DashboardAnalyticsResponse {
  metrics: DashboardMetrics;
  charts: {
    revenue: TimeSeriesData[];
    orders: TimeSeriesData[];
    users: TimeSeriesData[];
    crops: TimeSeriesData[];
  };
  insights: Array<{
    type: 'positive' | 'negative' | 'neutral';
    title: string;
    description: string;
    value?: number;
    change?: number;
  }>;
  alerts: Array<{
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    message: string;
    timestamp: string;
  }>;
}
