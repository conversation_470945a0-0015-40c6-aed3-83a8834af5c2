import React, { ReactNode } from 'react';
import { useAuth, useRole, usePermission, useVerification } from './AuthContext';
import { Permission } from './jwt';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectIsAuthenticated, selectCurrentUser, selectUserRole } from '../redux/slices/authSlice';

export interface RouteProtectionProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
}

export interface RoleProtectionProps extends RouteProtectionProps {
  role: 'farmer' | 'admin' | 'seller';
  requireVerification?: boolean;
}

export interface PermissionProtectionProps extends RouteProtectionProps {
  permission: Permission;
  requireVerification?: boolean;
}

export interface VerificationProtectionProps extends RouteProtectionProps {
  requireVerification?: boolean;
}

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredPermissions?: string[];
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermissions = []
}) => {
  // Use Redux selectors for authentication state
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const currentUser = useSelector(selectCurrentUser);
  const userRole = useSelector(selectUserRole);
  const location = useLocation();

  if (!isAuthenticated) {
    // Redirect to login page with return URL
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // Check role if required
  if (requiredRole && userRole !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  // Check permissions if required (if user object has permissions)
  if (requiredPermissions.length > 0 && currentUser) {
    const userPermissions = (currentUser as any).permissions || [];
    const hasAllPermissions = requiredPermissions.every(permission =>
      userPermissions.includes(permission)
    );
    if (!hasAllPermissions) {
      return <Navigate to="/unauthorized" replace />;
    }
  }

  return <>{children}</>;
};

/**
 * Component for protecting routes that require specific roles
 */
export const RoleProtectedRoute: React.FC<RoleProtectionProps> = ({
  children,
  role,
  requireVerification = false,
  fallback = <div>Access denied. Insufficient permissions.</div>,
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const hasRole = useRole(role);
  const isVerified = useVerification();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div>Access denied. Please log in.</div>;
  }

  if (!hasRole) {
    return <>{fallback}</>;
  }

  if (requireVerification && !isVerified) {
    return <div>Access denied. Account verification required.</div>;
  }

  return <>{children}</>;
};

/**
 * Component for protecting routes that require specific permissions
 */
export const PermissionProtectedRoute: React.FC<PermissionProtectionProps> = ({
  children,
  permission,
  requireVerification = false,
  fallback = <div>Access denied. Insufficient permissions.</div>,
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const hasPermission = usePermission(permission);
  const isVerified = useVerification();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div>Access denied. Please log in.</div>;
  }

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  if (requireVerification && !isVerified) {
    return <div>Access denied. Account verification required.</div>;
  }

  return <>{children}</>;
};

/**
 * Component for protecting routes that require verified accounts
 */
export const VerifiedRoute: React.FC<VerificationProtectionProps> = ({
  children,
  fallback = <div>Account verification required to access this feature.</div>,
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const isVerified = useVerification();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div>Access denied. Please log in.</div>;
  }

  if (!isVerified) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Agricultural platform specific route protections
 */

// Farmer/Seller only routes
export const FarmerRoute: React.FC<RouteProtectionProps> = ({ children, ...props }) => (
  <RoleProtectedRoute role="farmer" {...props}>
    {children}
  </RoleProtectedRoute>
);

export const SellerRoute: React.FC<RouteProtectionProps> = ({ children, ...props }) => (
  <RoleProtectedRoute role="seller" {...props}>
    {children}
  </RoleProtectedRoute>
);

// Admin only routes
export const AdminRoute: React.FC<RouteProtectionProps> = ({ children, ...props }) => (
  <RoleProtectedRoute role="admin" {...props}>
    {children}
  </RoleProtectedRoute>
);

// Farm management routes (farmers/sellers only, verified required)
export const FarmManagementRoute: React.FC<RouteProtectionProps> = ({ children, ...props }) => {
  const { user } = useAuth();
  const isFarmManager = user?.role === 'farmer' || user?.role === 'seller';
  
  if (!isFarmManager) {
    return <div>Access denied. Farm management is only available to farmers and sellers.</div>;
  }

  return (
    <VerifiedRoute {...props}>
      {children}
    </VerifiedRoute>
  );
};

// Crop monitoring routes (verified farmers/sellers only)
export const CropMonitoringRoute: React.FC<RouteProtectionProps> = ({ children, ...props }) => (
  <PermissionProtectedRoute 
    permission="crop:monitor" 
    requireVerification={true}
    {...props}
  >
    {children}
  </PermissionProtectedRoute>
);

// Financial routes (verified users only)
export const FinancialRoute: React.FC<RouteProtectionProps> = ({ children, ...props }) => (
  <PermissionProtectedRoute 
    permission="financial:read" 
    requireVerification={true}
    {...props}
  >
    {children}
  </PermissionProtectedRoute>
);

// Admin verification routes
export const AdminVerificationRoute: React.FC<RouteProtectionProps> = ({ children, ...props }) => (
  <PermissionProtectedRoute 
    permission="admin:verification" 
    {...props}
  >
    {children}
  </PermissionProtectedRoute>
);

/**
 * Higher-order component for route protection
 */
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

export function withRole<P extends object>(
  Component: React.ComponentType<P>,
  role: 'farmer' | 'admin' | 'seller',
  requireVerification = false
) {
  return function RoleProtectedComponent(props: P) {
    return (
      <RoleProtectedRoute role={role} requireVerification={requireVerification}>
        <Component {...props} />
      </RoleProtectedRoute>
    );
  };
}

export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  permission: Permission,
  requireVerification = false
) {
  return function PermissionProtectedComponent(props: P) {
    return (
      <PermissionProtectedRoute permission={permission} requireVerification={requireVerification}>
        <Component {...props} />
      </PermissionProtectedRoute>
    );
  };
}

/**
 * Utility functions for route protection logic
 */
export const routeGuards = {
  canAccessFarmManagement: (user: any): boolean => {
    return user?.role === 'farmer' || user?.role === 'seller';
  },
  
  canAccessCropMonitoring: (user: any): boolean => {
    return (user?.role === 'farmer' || user?.role === 'seller') && user?.isVerified;
  },
  
  canAccessFinancialData: (user: any): boolean => {
    return user?.isVerified && user?.permissions?.includes('financial:read');
  },
  
  canAccessAdminPanel: (user: any): boolean => {
    return user?.role === 'admin';
  },
  
  canVerifyUsers: (user: any): boolean => {
    return user?.role === 'admin' && user?.permissions?.includes('admin:verification');
  },
  
  requiresVerification: (path: string): boolean => {
    const verificationRequiredPaths = [
      '/farm-management',
      '/crop-monitoring',
      '/financial',
      '/orders'
    ];
    
    return verificationRequiredPaths.some(requiredPath => path.startsWith(requiredPath));
  }
};

// GuestRoute is exported from GuestRoute.tsx to avoid duplication

export default {
  ProtectedRoute,
  RoleProtectedRoute,
  PermissionProtectedRoute,
  VerifiedRoute,
  FarmerRoute,
  SellerRoute,
  AdminRoute,
  FarmManagementRoute,
  CropMonitoringRoute,
  FinancialRoute,
  AdminVerificationRoute,
  withAuth,
  withRole,
  withPermission,
  routeGuards,
};