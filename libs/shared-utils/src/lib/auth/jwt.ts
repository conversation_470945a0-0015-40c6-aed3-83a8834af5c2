/**
 * JWT Token Management Utilities
 * Handles authentication tokens for the BeFarma agricultural platform
 */

// JWT decode functionality implemented manually to avoid external dependency

export interface TokenPayload {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'farmer' | 'admin' | 'seller';
  isVerified: boolean;
  farmId?: string;
  permissions: string[];
  iat: number;
  exp: number;
}

export interface AuthTokens {
  token: string;
  refreshToken: string;
}

const TOKEN_STORAGE_KEY = 'befarma_auth_tokens';
const USER_STORAGE_KEY = 'befarma_user_data';

/**
 * JWT Token Management Class
 */
export class JWTManager {
  private static instance: JWTManager;

  private constructor() {
    // Load tokens from storage on initialization
    this.loadTokens();
  }

  static getInstance(): JWTManager {
    if (!JWTManager.instance) {
      JWTManager.instance = new JWTManager();
    }
    return JWTManager.instance;
  }

  private loadTokens(): void {
    const storedTokens = localStorage.getItem(TOKEN_STORAGE_KEY);
    if (storedTokens) {
      try {
        JSON.parse(storedTokens) as AuthTokens;
      } catch (error) {
        console.error('Failed to parse stored tokens:', error);
        JWTManager.clearTokens();
      }
    }
  }

  /**
   * Store authentication tokens in localStorage
   */
  static setTokens(tokens: AuthTokens): void {
    try {
      localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokens));
    } catch (error) {
      console.error('Failed to store authentication tokens:', error);
    }
  }

  /**
   * Retrieve authentication tokens from localStorage
   */
  static getTokens(): AuthTokens | null {
    try {
      const tokensString = localStorage.getItem(TOKEN_STORAGE_KEY);
      if (!tokensString) return null;
      
      const tokens = JSON.parse(tokensString) as AuthTokens;
      
      // Check if tokens are expired
      if (this.isTokenExpired(tokens.token)) {
        this.clearTokens();
        return null;
      }
      
      return tokens;
    } catch (error) {
      console.error('Failed to retrieve authentication tokens:', error);
      this.clearTokens();
      return null;
    }
  }

  /**
   * Clear authentication tokens from localStorage
   */
  static clearTokens(): void {
    try {
      localStorage.removeItem(TOKEN_STORAGE_KEY);
      localStorage.removeItem(USER_STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear authentication tokens:', error);
    }
  }

  /**
   * Get the current access token
   */
  static getAccessToken(): string | null {
    const tokens = this.getTokens();
    return tokens?.token || null;
  }

  /**
   * Get the current refresh token
   */
  static getRefreshToken(): string | null {
    const tokens = this.getTokens();
    return tokens?.refreshToken || null;
  }

  /**
   * Check if a token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      const payload = this.decodeToken(token);
      if (!payload?.exp) return true;
      
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }

  /**
   * Decode JWT token without verification (for client-side use only)
   */
  static decodeToken(token: string): TokenPayload | null {
    try {
      const base64Url = token.split('.')[1];
      if (!base64Url) return null;
      
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      
      return JSON.parse(jsonPayload) as TokenPayload;
    } catch (error) {
      console.error('Failed to decode JWT token:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const tokens = this.getTokens();
    return tokens !== null && !this.isTokenExpired(tokens.token);
  }

  /**
   * Get current user data from token
   */
  static getCurrentUser(): TokenPayload | null {
    const accessToken = this.getAccessToken();
    if (!accessToken) return null;
    
    return this.decodeToken(accessToken);
  }

  /**
   * Check if user has specific role
   */
  static hasRole(role: 'farmer' | 'admin' | 'seller'): boolean {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  /**
   * Check if user has specific permission
   */
  static hasPermission(permission: string): boolean {
    const user = this.getCurrentUser();
    return user?.permissions?.includes(permission) || false;
  }

  /**
   * Check if user account is verified
   */
  static isUserVerified(): boolean {
    const user = this.getCurrentUser();
    return user?.isVerified || false;
  }

  /**
   * Get user's farm ID (for farmers/sellers)
   */
  static getUserFarmId(): string | null {
    const user = this.getCurrentUser();
    return user?.farmId || null;
  }

  /**
   * Calculate time until token expiration
   */
  static getTimeUntilExpiration(): number {
    const accessToken = this.getAccessToken();
    if (!accessToken) return 0;
    
    const payload = this.decodeToken(accessToken);
    if (!payload?.exp) return 0;
    
    const currentTime = Math.floor(Date.now() / 1000);
    return Math.max(0, payload.exp - currentTime);
  }

  /**
   * Check if token needs refresh (expires within 5 minutes)
   */
  static shouldRefreshToken(): boolean {
    const timeUntilExpiration = this.getTimeUntilExpiration();
    return timeUntilExpiration > 0 && timeUntilExpiration < 300; // 5 minutes
  }
}

// Agricultural platform specific permissions
export const PERMISSIONS = {
  // Farm Management
  FARM_CREATE: 'farm:create',
  FARM_READ: 'farm:read',
  FARM_UPDATE: 'farm:update',
  FARM_DELETE: 'farm:delete',
  
  // Crop Management
  CROP_CREATE: 'crop:create',
  CROP_READ: 'crop:read',
  CROP_UPDATE: 'crop:update',
  CROP_DELETE: 'crop:delete',
  CROP_MONITOR: 'crop:monitor',

  
  // Order Management
  ORDER_CREATE: 'order:create',
  ORDER_READ: 'order:read',
  ORDER_UPDATE: 'order:update',
  ORDER_DELETE: 'order:delete',
  ORDER_PROCESS: 'order:process',
  
  // Financial Management
  FINANCIAL_READ: 'financial:read',
  FINANCIAL_REPORTS: 'financial:reports',
  
  // Admin Permissions
  ADMIN_USER_MANAGE: 'admin:user:manage',
  ADMIN_PLATFORM_CONFIG: 'admin:platform:config',
  ADMIN_VERIFICATION: 'admin:verification',
  ADMIN_ANALYTICS: 'admin:analytics',
} as const;

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];

export const tokenManager = JWTManager.getInstance(); 