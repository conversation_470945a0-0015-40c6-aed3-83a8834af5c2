import React, { createContext, useContext, useCallback } from 'react';
import { JWTManager } from './jwt';
import { useAdminLoginMutation } from '../redux/api/apiSlice';
import { useDispatch, useSelector } from 'react-redux';
import { 
  setCredentials, 
  clearCredentials,
  selectIsAuthenticated,
  selectCurrentUser,
  selectUserRole,
  selectAuthLoading,
  selectAuthError
} from '../redux/slices/authSlice';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any | null;
  role: 'seller' | 'admin' | null;
  permissions: string[];
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (token: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use Redux selectors instead of local state
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthLoading);
  const user = useSelector(selectCurrentUser);
  const role = useSelector(selectUserRole);
  const error = useSelector(selectAuthError);

  const dispatch = useDispatch();
  const [adminLogin] = useAdminLoginMutation();

  // Memoize the logout function to prevent unnecessary re-renders
  const handleLogout = useCallback(() => {
    JWTManager.clearTokens();
    dispatch(clearCredentials());
  }, [dispatch]);

  const handleLogin = async (email: string, password: string) => {
    try {
      const result = await adminLogin({ email, password }).unwrap();
      
      if (!result.success || !result.data) {
        throw new Error(result.message || 'Login failed');
      }

      // Store tokens
      JWTManager.setTokens({
        token: result.token,
        refreshToken: result.refreshToken || ''
      });

      // Update Redux store only
      dispatch(setCredentials({
        user: {
          ...result.data.admin,
          role: 'admin' as const
        },
        token: result.token,
        refreshToken: result.refreshToken || ''
      }));
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const handleResetPassword = async (_email: string) => {
    // Implement using RTK Query mutation
    throw new Error('Not implemented');
  };

  const handleUpdatePassword = async (_token: string, _newPassword: string) => {
    // Implement using RTK Query mutation
    throw new Error('Not implemented');
  };

  const value = {
    isAuthenticated,
    isLoading,
    user,
    role: role ?? null,
    permissions: user?.permissions || [],
    error,
    login: handleLogin,
    logout: handleLogout,
    resetPassword: handleResetPassword,
    updatePassword: handleUpdatePassword
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Custom hook for checking permissions
export const usePermission = (requiredPermission: string) => {
  const { permissions } = useAuth();
  return permissions.includes(requiredPermission);
};

// Custom hook for role-based access control
export const useRole = (requiredRole: string) => {
  const { role } = useAuth();
  return role === requiredRole;
};

// Custom hook for verification status
export const useVerification = () => {
  const { user } = useAuth();
  return user?.isVerified || false;
}; 