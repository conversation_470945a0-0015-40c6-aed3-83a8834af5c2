import React, { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectIsAuthenticated, selectAuthLoading } from '../redux/slices/authSlice';

interface GuestRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

/**
 * Route wrapper that redirects authenticated users to a specified route
 * Used for pages that should only be accessible to non-authenticated users
 * (e.g., login, register, forgot password)
 */
export const GuestRoute: React.FC<GuestRouteProps> = ({
  children,
  redirectTo = '/dashboard',
}) => {
  // Use Redux selectors for authentication state
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthLoading);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isAuthenticated) {
    return <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};