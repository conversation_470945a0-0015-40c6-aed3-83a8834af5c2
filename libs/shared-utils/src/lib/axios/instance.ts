/**
 * Enhanced Axios Configuration for BeFarma Agricultural Platform
 * Multiple service instances aligned with microservices architecture
 */

import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosError, AxiosResponse } from 'axios';
import { environment } from '../environment/environment.config';

// Store reference for accessing Redux state
let store: any = null;

export const setAxiosStore = (reduxStore: any) => {
  store = reduxStore;
};

// Helper function to get auth token from store
const getAuthToken = (): string | null => {
  if (store) {
    const state = store.getState();
    return state.auth?.token || null;
  }
  return null;
};

// Helper function to create service-specific instances
const createServiceInstance = (serviceUrl: string, serviceName: string): AxiosInstance => {
  const instance = axios.create({
    baseURL: serviceUrl,
    timeout: 30000, // 30 seconds for agricultural data processing
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
      // Add auth token if available
      const token = getAuthToken();
      if (token && config.headers && !config.headers['Authorization']) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Add service identifier
      if (config.headers) {
        config.headers['X-Service'] = serviceName;
        config.headers['X-Timestamp'] = new Date().toISOString();
      }

      // Log request in development
      if (!environment.production && environment.features.enableLogging) {
        console.log(`🚀 [${serviceName}] Request:`, {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data,
        });
      }

      return config;
    },
    (error: AxiosError): Promise<AxiosError> => {
      console.error(`❌ [${serviceName}] Request Error:`, error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse): AxiosResponse => {
      // Log response in development
      if (!environment.production && environment.features.enableLogging) {
        console.log(`✅ [${serviceName}] Response:`, {
          status: response.status,
          data: response.data,
        });
      }

      return response;
    },
    (error: AxiosError): Promise<AxiosError> => {
      if (error.response) {
        // Server responded with error status
        const { status, data } = error.response;
        
        switch (status) {
          case 401:
            console.error(`🔒 [${serviceName}] Unauthorized:`, data);
            // Dispatch logout action if store is available
            if (store) {
              store.dispatch({ type: 'auth/clearCredentials' });
            }
            break;
          case 403:
            console.error(`⛔ [${serviceName}] Forbidden:`, data);
            break;
          case 404:
            console.error(`🔍 [${serviceName}] Not Found:`, data);
            break;
          case 422:
            console.error(`📝 [${serviceName}] Validation Error:`, data);
            break;
          case 500:
            console.error(`💥 [${serviceName}] Server Error:`, data);
            break;
          default:
            console.error(`❌ [${serviceName}] HTTP ${status}:`, data);
        }
      } else if (error.request) {
        // Network error
        console.error(`🌐 [${serviceName}] Network Error:`, error.request);
      } else {
        // Request setup error
        console.error(`⚙️ [${serviceName}] Request Setup Error:`, error.message);
      }

      // Add service context to error (extend error object)
      (error as any).service = serviceName;
      (error as any).timestamp = new Date().toISOString();

      return Promise.reject(error);
    }
  );

  return instance;
};

// ✅ FIXED: Direct microservice access with proper base URLs
// Each service client connects directly to its respective microservice
// Base URLs no longer include service-specific paths to prevent double paths

// Service-specific clients with direct microservice URLs
export const sellerServiceClient = createServiceInstance(
  environment.services.sellerService,
  'seller-service'
); // Direct: http://localhost:3001/api/v1 -> /sellers/*

export const adminServiceClient = createServiceInstance(
  environment.services.adminService,
  'admin-service'
); // Direct: http://localhost:3002/api/v1 -> /admin/*

export const farmServiceClient = createServiceInstance(
  environment.services.farmService,
  'farm-service'
); // Direct: http://localhost:3005/api/v1 -> /farms/*

export const cropServiceClient = createServiceInstance(
  environment.services.cropService,
  'crop-service'
); // Direct: http://localhost:3004/api/v1/crops/*

export const orderServiceClient = createServiceInstance(
  environment.services.orderService,
  'order-service'
); // Direct: http://localhost:3009/api/v1/orders/*

export const financialServiceClient = createServiceInstance(
  environment.services.financialService,
  'financial-service'
); // Direct: http://localhost:3006/api/v1/financial/*

export const inventoryServiceClient = createServiceInstance(
  environment.services.inventoryService,
  'inventory-service'
); // Direct: http://localhost:3007/api/v1/inventory/*

export const analyticsServiceClient = createServiceInstance(
  environment.services.analyticsService,
  'analytics-service'
); // Direct: http://localhost:3003/api/v1/analytics/*

export const notificationServiceClient = createServiceInstance(
  environment.services.notificationService,
  'notification-service'
); // Direct: http://localhost:3008/api/v1/notifications/*

// Utility function to get service client by name
export const getServiceClient = (serviceName: string): AxiosInstance => {
  const clients: Record<string, AxiosInstance> = {
    'seller': sellerServiceClient,
    'admin': adminServiceClient,
    'farm': farmServiceClient,
    'crop': cropServiceClient,
    'order': orderServiceClient,
    'financial': financialServiceClient,
    'inventory': inventoryServiceClient,
    'analytics': analyticsServiceClient,
    'notification': notificationServiceClient,
  };

  return clients[serviceName] || sellerServiceClient; // Default to seller service if not found
};