{"name": "shared-utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-utils/src", "projectType": "library", "tags": [], "// targets": "to see all targets run: nx show project shared-utils --web", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared-utils", "main": "libs/shared-utils/src/index.ts", "tsConfig": "libs/shared-utils/tsconfig.lib.json", "assets": ["libs/shared-utils/*.md"]}}}}