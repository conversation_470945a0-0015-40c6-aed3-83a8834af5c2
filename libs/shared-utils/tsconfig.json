{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "forceConsistentCasingInFileNames": true, "strict": true, "importHelpers": true, "noImplicitOverride": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}]}