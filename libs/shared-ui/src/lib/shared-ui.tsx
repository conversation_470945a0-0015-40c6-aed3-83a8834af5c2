/**
 * BeFarma Shared UI Components
 * Base component for the agricultural platform
 */

import React from 'react';

import './shared-ui.module.scss';

export interface SharedUiProps {
  children?: React.ReactNode;
  className?: string;
}

export function SharedUi({ children, className = '' }: SharedUiProps): React.ReactElement {
  return (
    <div className={`befarma-shared-ui ${className}`}>
      <h2>BeFarma Shared UI Library</h2>
      <p>Agricultural Technology Platform - Shared Components</p>
      {children && <div className='shared-ui-content'>{children}</div>}
    </div>
  );
}

export default SharedUi;
