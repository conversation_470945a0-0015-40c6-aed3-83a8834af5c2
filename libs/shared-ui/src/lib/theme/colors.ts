/**
 * BeFarma Color Palette
 * Agricultural Theme Colors for the Platform
 */

export const colors = {
  // Primary Colors (Agricultural Theme)
  text: '#000000', // Black - All primary text content
  accentGreen: '#22C55E', // Light green - Small buttons, icons, symbols (leaf color)
  primaryOrange: '#F97316', // Light orange - Large buttons, primary actions (fruit color)
  background: '#FFFFFF', // White - Primary background
  secondaryBackground: '#F8F9FA', // Light gray - Cards and sections

  // Supporting Colors
  success: '#10B981', // Green - Success states
  warning: '#F59E0B', // Amber - Warning states
  error: '#EF4444', // Red - Error states
  info: '#3B82F6', // Blue - Info states

  // Neutral Colors
  neutralGray: '#6B7280', // Medium gray
  lightGray: '#E5E7EB', // Light gray borders
  darkGray: '#374151', // Dark gray text

  // Agricultural Status Colors
  cropHealth: {
    healthy: '#10B981', // Green - Healthy crops
    warning: '#F59E0B', // Amber - Attention needed
    critical: '#EF4444', // Red - Critical issues
  },

  // Growth Stage Colors
  growthStage: {
    planting: '#8B5CF6', // Purple - Planting phase
    growing: '#22C55E', // Green - Growing phase
    maturing: '#F59E0B', // Orange - Maturing phase
    ready: '#10B981', // Dark green - Ready for harvest
  },
} as const;

// Color utility functions
export const getStatusColor = (status: 'healthy' | 'warning' | 'critical'): string => {
  return colors.cropHealth[status];
};

export const getPlotStatusColor = (status: 'available' | 'leased' | 'cultivating'): string => {
  return colors.plotStatus[status];
};

export const getGrowthStageColor = (stage: 'planting' | 'growing' | 'maturing' | 'ready'): string => {
  return colors.growthStage[stage];
};

// Export for Tailwind CSS configuration
export const tailwindColors = {
  'befarma-text': colors.text,
  'befarma-accent-green': colors.accentGreen,
  'befarma-primary-orange': colors.primaryOrange,
  'befarma-background': colors.background,
  'befarma-secondary-bg': colors.secondaryBackground,
  'befarma-success': colors.success,
  'befarma-warning': colors.warning,
  'befarma-error': colors.error,
  'befarma-info': colors.info,
  'befarma-neutral': colors.neutralGray,
  'befarma-light-gray': colors.lightGray,
  'befarma-dark-gray': colors.darkGray,
};

export type BeFarmaColor = keyof typeof colors;
export type CropHealthStatus = keyof typeof colors.cropHealth;
export type PlotStatus = keyof typeof colors.plotStatus;
export type GrowthStage = keyof typeof colors.growthStage;
