/**
 * BeFarma Theme System Barrel Export
 * Centralized export for all design system tokens and configuration
 */

// Consolidated theme configuration for Tailwind CSS
import { tailwindColors } from './colors';
import { tailwindTypography } from './typography';
import { tailwindSpacing } from './spacing';

// Export all color system
export * from './colors';

// Export typography system
export * from './typography';

// Export spacing system
export * from './spacing';

export const beFarmaTheme = {
  colors: tailwindColors,
  ...tailwindTypography,
  ...tailwindSpacing,
};

// Theme utilities
export const createThemeClasses = () => {
  return {
    // Agricultural specific utility classes
    '.befarma-text': {
      color: tailwindColors['befarma-text'],
    },
    '.befarma-bg': {
      backgroundColor: tailwindColors['befarma-background'],
    },
    '.befarma-card': {
      backgroundColor: tailwindColors['befarma-background'],
      boxShadow: tailwindSpacing.boxShadow.base,
      borderRadius: tailwindSpacing.borderRadius.md,
    },
    '.befarma-button-primary': {
      backgroundColor: tailwindColors['befarma-primary-orange'],
      color: tailwindColors['befarma-background'],
      padding: '12px 20px',
      borderRadius: tailwindSpacing.borderRadius.md,
      fontWeight: tailwindTypography.fontWeight.medium,
    },
    '.befarma-button-secondary': {
      backgroundColor: tailwindColors['befarma-accent-green'],
      color: tailwindColors['befarma-background'],
      padding: '8px 12px',
      borderRadius: tailwindSpacing.borderRadius.base,
      fontWeight: tailwindTypography.fontWeight.medium,
    },
  };
};

// Export theme constants
export const THEME_CONSTANTS = {
  SPACING_UNIT: 4,
  MIN_TOUCH_TARGET: 44,
  BREAKPOINTS: {
    mobile: 320,
    tablet: 768,
    desktop: 1024,
    wide: 1280,
  },
} as const;
