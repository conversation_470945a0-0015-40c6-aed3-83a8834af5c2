/**
 * BeFarma Typography System
 * Font and text styling configuration for the agricultural platform
 */

// Font family configuration
export const fonts = {
  primary: ['Inter', 'system-ui', 'sans-serif'],
  fallback: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
} as const;

// Font weights (aligned with design guidelines)
export const fontWeights = {
  regular: 400, // Body text
  medium: 500, // Labels
  semibold: 600, // Headings
  bold: 700, // Headings
} as const;

// Font sizes with agricultural context
export const fontSizes = {
  // Text sizes
  xs: '0.75rem', // 12px - Captions, small labels
  sm: '0.875rem', // 14px - Small body text
  base: '1rem', // 16px - Default body text
  lg: '1.125rem', // 18px - Large body text
  xl: '1.25rem', // 20px - Large text

  // Heading sizes
  'heading-sm': '1.5rem', // 24px - Small headings
  'heading-md': '1.875rem', // 30px - Medium headings
  'heading-lg': '2.25rem', // 36px - Large headings
  'heading-xl': '3rem', // 48px - Display headings

  // Agricultural specific sizes
  'farm-title': '1.75rem', // 28px - Farm names, plot titles
  'crop-label': '1rem', // 16px - Crop type labels
  'status-text': '0.875rem', // 14px - Status indicators
  'metric-value': '2rem', // 32px - Large metric displays
} as const;

// Line heights for readability
export const lineHeights = {
  tight: 1.25, // Headlines
  normal: 1.5, // Body text
  relaxed: 1.75, // Long form content
} as const;

// Letter spacing
export const letterSpacing = {
  tight: '-0.025em',
  normal: '0',
  wide: '0.025em',
} as const;

// Typography utility classes
export const typography = {
  // Heading styles
  'heading-xl': {
    fontSize: fontSizes['heading-xl'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.tight,
    letterSpacing: letterSpacing.tight,
  },
  'heading-lg': {
    fontSize: fontSizes['heading-lg'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.tight,
  },
  'heading-md': {
    fontSize: fontSizes['heading-md'],
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.tight,
  },
  'heading-sm': {
    fontSize: fontSizes['heading-sm'],
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.normal,
  },

  // Body text styles
  'body-lg': {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.normal,
  },
  'body-base': {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.normal,
  },
  'body-sm': {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.normal,
  },

  // Label styles
  'label-lg': {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal,
  },
  'label-base': {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal,
  },
  'label-sm': {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal,
  },

  // Agricultural specific styles
  'farm-title': {
    fontSize: fontSizes['farm-title'],
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.tight,
  },
  'crop-label': {
    fontSize: fontSizes['crop-label'],
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal,
  },
  'status-text': {
    fontSize: fontSizes['status-text'],
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal,
    letterSpacing: letterSpacing.wide,
  },
  'metric-value': {
    fontSize: fontSizes['metric-value'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.tight,
  },

  // Caption and small text
  caption: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.regular,
    lineHeight: lineHeights.normal,
  },
} as const;

// Export for Tailwind CSS configuration
export const tailwindTypography = {
  fontFamily: {
    befarma: fonts.primary,
  },
  fontSize: fontSizes,
  fontWeight: fontWeights,
  lineHeight: lineHeights,
  letterSpacing,
};

export type TypographyVariant = keyof typeof typography;
export type FontSize = keyof typeof fontSizes;
export type FontWeight = keyof typeof fontWeights;
