/**
 * BeFarma Spacing System
 * 4px grid-based spacing configuration for consistent layouts
 */

// Base spacing unit (4px)
const SPACING_UNIT = 4;

// Generate spacing scale based on 4px grid
export const spacing = {
  0: '0',
  px: '1px',
  0.5: `${SPACING_UNIT * 0.5}px`, // 2px
  1: `${SPACING_UNIT * 1}px`, // 4px
  1.5: `${SPACING_UNIT * 1.5}px`, // 6px
  2: `${SPACING_UNIT * 2}px`, // 8px
  2.5: `${SPACING_UNIT * 2.5}px`, // 10px
  3: `${SPACING_UNIT * 3}px`, // 12px
  3.5: `${SPACING_UNIT * 3.5}px`, // 14px
  4: `${SPACING_UNIT * 4}px`, // 16px
  5: `${SPACING_UNIT * 5}px`, // 20px
  6: `${SPACING_UNIT * 6}px`, // 24px
  7: `${SPACING_UNIT * 7}px`, // 28px
  8: `${SPACING_UNIT * 8}px`, // 32px
  9: `${SPACING_UNIT * 9}px`, // 36px
  10: `${SPACING_UNIT * 10}px`, // 40px
  11: `${SPACING_UNIT * 11}px`, // 44px
  12: `${SPACING_UNIT * 12}px`, // 48px
  14: `${SPACING_UNIT * 14}px`, // 56px
  16: `${SPACING_UNIT * 16}px`, // 64px
  20: `${SPACING_UNIT * 20}px`, // 80px
  24: `${SPACING_UNIT * 24}px`, // 96px
  28: `${SPACING_UNIT * 28}px`, // 112px
  32: `${SPACING_UNIT * 32}px`, // 128px
  36: `${SPACING_UNIT * 36}px`, // 144px
  40: `${SPACING_UNIT * 40}px`, // 160px
  44: `${SPACING_UNIT * 44}px`, // 176px
  48: `${SPACING_UNIT * 48}px`, // 192px
  52: `${SPACING_UNIT * 52}px`, // 208px
  56: `${SPACING_UNIT * 56}px`, // 224px
  60: `${SPACING_UNIT * 60}px`, // 240px
  64: `${SPACING_UNIT * 64}px`, // 256px
  72: `${SPACING_UNIT * 72}px`, // 288px
  80: `${SPACING_UNIT * 80}px`, // 320px
  96: `${SPACING_UNIT * 96}px`, // 384px
} as const;

// Agricultural component specific spacing
export const componentSpacing = {
  // Card spacing
  'card-padding': spacing[6], // 24px - Card internal padding
  'card-margin': spacing[4], // 16px - Card external margin
  'card-gap': spacing[4], // 16px - Gap between cards

  // Form spacing
  'form-field-gap': spacing[4], // 16px - Gap between form fields
  'form-section-gap': spacing[8], // 32px - Gap between form sections
  'form-padding': spacing[6], // 24px - Form container padding

  // Button spacing
  'button-padding-sm': `${spacing[2]} ${spacing[3]}`, // 8px 12px - Small buttons
  'button-padding-md': `${spacing[3]} ${spacing[5]}`, // 12px 20px - Medium buttons
  'button-padding-lg': `${spacing[4]} ${spacing[8]}`, // 16px 32px - Large buttons
  'button-gap': spacing[3], // 12px - Gap between buttons

  // Navigation spacing
  'nav-padding': spacing[4], // 16px - Navigation padding
  'nav-item-gap': spacing[2], // 8px - Gap between nav items

  // Dashboard spacing
  'dashboard-section-gap': spacing[8], // 32px - Gap between dashboard sections
  'dashboard-widget-gap': spacing[6], // 24px - Gap between widgets
  'dashboard-padding': spacing[6], // 24px - Dashboard container padding

  // Agricultural specific spacing
  'farm-card-spacing': spacing[6], // 24px - Farm card internal spacing
  'plot-grid-gap': spacing[4], // 16px - Gap between plot items
  'crop-info-spacing': spacing[3], // 12px - Crop information spacing
  'status-indicator-margin': spacing[2], // 8px - Status indicator margin

  // Mobile touch targets (minimum 44px for accessibility)
  'touch-target-min': spacing[11], // 44px - Minimum touch target size
} as const;

// Layout spacing for different screen sizes
export const layoutSpacing = {
  mobile: {
    container: spacing[4], // 16px - Mobile container padding
    section: spacing[6], // 24px - Mobile section spacing
    element: spacing[3], // 12px - Mobile element spacing
  },
  tablet: {
    container: spacing[6], // 24px - Tablet container padding
    section: spacing[8], // 32px - Tablet section spacing
    element: spacing[4], // 16px - Tablet element spacing
  },
  desktop: {
    container: spacing[8], // 32px - Desktop container padding
    section: spacing[12], // 48px - Desktop section spacing
    element: spacing[6], // 24px - Desktop element spacing
  },
} as const;

// Border radius scale
export const borderRadius = {
  none: '0',
  sm: '2px',
  base: '4px',
  md: '6px',
  lg: '8px',
  xl: '12px',
  '2xl': '16px',
  '3xl': '24px',
  full: '9999px',
} as const;

// Shadow scale for cards and elevation
export const shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
} as const;

// Export for Tailwind CSS configuration
export const tailwindSpacing = {
  spacing: spacing,
  borderRadius: borderRadius,
  boxShadow: shadows,
};

export type SpacingKey = keyof typeof spacing;
export type ComponentSpacingKey = keyof typeof componentSpacing;
export type BorderRadiusKey = keyof typeof borderRadius;
