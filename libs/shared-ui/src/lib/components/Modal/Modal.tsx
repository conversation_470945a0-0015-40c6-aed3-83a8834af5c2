import React, { useEffect, useRef } from 'react';
import { colors } from '../../theme/colors';

export interface ModalProps {
  /**
   * Whether the modal is open
   */
  open: boolean;
  
  /**
   * Callback when modal should close
   */
  onClose: () => void;
  
  /**
   * Modal title
   */
  title?: string;
  
  /**
   * Modal size
   */
  size?: 'small' | 'medium' | 'large' | 'xl';
  
  /**
   * Whether clicking outside closes the modal
   */
  closeOnOverlayClick?: boolean;
  
  /**
   * Whether pressing escape closes the modal
   */
  closeOnEscape?: boolean;
  
  /**
   * Custom header content
   */
  header?: React.ReactNode;
  
  /**
   * Footer content (usually buttons)
   */
  footer?: React.ReactNode;
  
  /**
   * Modal content
   */
  children: React.ReactNode;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

export const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  title,
  size = 'medium',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  header,
  footer,
  children,
  className = '',
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // Handle escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (closeOnEscape && event.key === 'Escape' && open) {
        onClose();
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => document.removeEventListener('keydown', handleEscapeKey);
    }
  }, [open, closeOnEscape, onClose]);

  // Focus management
  useEffect(() => {
    if (open) {
      // Store the currently focused element
      previousActiveElement.current = document.activeElement as HTMLElement;
      
      // Focus the modal
      if (modalRef.current) {
        modalRef.current.focus();
      }
      
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else {
      // Restore focus and body scroll
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [open]);

  const getSizeClasses = () => {
    const sizeClasses = {
      small: 'max-w-md',
      medium: 'max-w-lg', 
      large: 'max-w-2xl',
      xl: 'max-w-4xl'
    };
    return sizeClasses[size];
  };

  const handleOverlayClick = (event: React.MouseEvent) => {
    if (closeOnOverlayClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleOverlayClick}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div
        ref={modalRef}
        className={`relative bg-white rounded-lg shadow-xl w-full mx-4 my-8 ${getSizeClasses()} ${className}`}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'modal-title' : undefined}
        tabIndex={-1}
        style={{ maxHeight: 'calc(100vh - 4rem)' }}
      >
        <div className="flex flex-col max-h-full">
          {/* Header */}
          {(title || header) && (
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex-1">
                {header || (
                  <h2 
                    id="modal-title" 
                    className="text-lg font-semibold text-gray-900"
                  >
                    {title}
                  </h2>
                )}
              </div>
              
              {/* Close Button */}
              <button
                type="button"
                className="ml-4 inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                onClick={onClose}
                aria-label="Close modal"
              >
                <svg 
                  className="w-4 h-4" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M6 18L18 6M6 6l12 12" 
                  />
                </svg>
              </button>
            </div>
          )}
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {children}
          </div>
          
          {/* Footer */}
          {footer && (
            <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Modal sub-components for better composition
export const ModalHeader: React.FC<{ 
  children: React.ReactNode; 
  className?: string; 
}> = ({ children, className = '' }) => (
  <div className={`mb-4 ${className}`}>
    {children}
  </div>
);

export const ModalBody: React.FC<{ 
  children: React.ReactNode; 
  className?: string; 
}> = ({ children, className = '' }) => (
  <div className={`mb-4 ${className}`}>
    {children}
  </div>
);

export const ModalFooter: React.FC<{ 
  children: React.ReactNode; 
  className?: string; 
}> = ({ children, className = '' }) => (
  <div className={`flex justify-end space-x-2 ${className}`}>
    {children}
  </div>
);

export default Modal; 