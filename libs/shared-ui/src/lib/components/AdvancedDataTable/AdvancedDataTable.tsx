import React, { useState, useMemo, useCallback } from 'react';
import { 
  ChevronUpIcon, 
  ChevronDownIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { Button } from '../Button';
import { Input } from '../Input';
import { StatusBadge } from '../StatusBadge';

type DataPath = string | string[];

// Utility function to get nested value
const getNestedValue = (obj: any, path: DataPath): any => {
  const keys = Array.isArray(path) ? path : path.split('.');
  return keys.reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj);
};

// Convert DataPath to string for use as object key
const pathToString = (path: DataPath): string => {
  return Array.isArray(path) ? path.join('.') : path;
};

// Convert string path back to original format
const stringToPath = (path: string): DataPath => {
  return path.includes('.') ? path.split('.') : path;
};

export interface Column<T = any> {
  key: string;
  title: string;
  dataIndex: DataPath;
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  filterType?: 'text' | 'select' | 'date' | 'number';
  filterOptions?: Array<{ label: string; value: any }>;
}

export interface AdvancedDataTableProps<T = any> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    pageSizeOptions?: string[];
    onChange: (page: number, pageSize: number) => void;
  };
  rowSelection?: {
    selectedRowKeys: string[];
    onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;
  };
  actions?: {
    view?: (record: T) => void;
    edit?: (record: T) => void;
    delete?: (record: T) => void;
    custom?: Array<{
      key: string;
      label: string;
      icon?: React.ReactNode;
      onClick: (record: T) => void;
      disabled?: (record: T) => boolean;
    }>;
  };
  searchable?: boolean;
  exportable?: boolean;
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  className?: string;
  emptyText?: string;
  rowKey?: string | ((record: T) => string);
}

export function AdvancedDataTable<T = any>({
  data,
  columns,
  loading = false,
  pagination,
  rowSelection,
  actions,
  searchable = true,
  exportable = false,
  onExport,
  className = '',
  emptyText = 'No data available',
  rowKey = 'id'
}: AdvancedDataTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{
    key: DataPath;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [showFilters, setShowFilters] = useState(false);

  // Get row key
  const getRowKey = useCallback((record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return (record as any)[rowKey] || index.toString();
  }, [rowKey]);

  // Filter and search data
  const filteredData = useMemo(() => {
    let result = [...data];

    // Apply search
    if (searchTerm && searchable) {
      result = result.filter(item =>
        columns.some(column => {
          const value = getNestedValue(item, column.dataIndex);
          return value?.toString().toLowerCase().includes(searchTerm.toLowerCase());
        })
      );
    }

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        result = result.filter(item => {
          const itemValue = getNestedValue(item, stringToPath(key));
          if (Array.isArray(value)) {
            return value.includes(itemValue);
          }
          return itemValue === value;
        });
      }
    });

    // Apply sorting
    if (sortConfig) {
      result.sort((a, b) => {
        const aValue = getNestedValue(a, sortConfig.key);
        const bValue = getNestedValue(b, sortConfig.key);
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return result;
  }, [data, searchTerm, filters, sortConfig, columns, searchable]);

  // Handle sorting
  const handleSort = (key: DataPath) => {
    setSortConfig(current => {
      const keysMatch = current?.key === key || 
        (Array.isArray(current?.key) && Array.isArray(key) && 
        current.key.length === key.length && 
        current.key.every((k, i) => k === key[i]));

      if (keysMatch) {
        return current.direction === 'asc' 
          ? { key, direction: 'desc' }
          : null;
      }
      return { key, direction: 'asc' };
    });
  };

  // Handle filter change
  const handleFilterChange = (key: DataPath, value: any) => {
    setFilters(prev => ({
      ...prev,
      [pathToString(key)]: value
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  // Render cell content
  const renderCell = (column: Column<T>, record: T, index: number) => {
    const value = getNestedValue(record, column.dataIndex);
    
    if (column.render) {
      return column.render(value, record, index);
    }

    // Default rendering based on value type
    if (typeof value === 'boolean') {
      return <StatusBadge status={value ? 'active' : 'inactive'} />;
    }

    if (value instanceof Date) {
      return value.toLocaleDateString();
    }

    return value?.toString() || '-';
  };

  // Render filter input
  const renderFilter = (column: Column<T>) => {
    if (!column.filterable) return null;

    const filterValue = filters[column.dataIndex];

    switch (column.filterType) {
      case 'select':
        return (
          <select
            value={filterValue || ''}
            onChange={(e) => handleFilterChange(column.dataIndex, e.target.value)}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
          >
            <option value="">All</option>
            {column.filterOptions?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      
      case 'date':
        return (
          <input
            type="date"
            value={filterValue || ''}
            onChange={(e) => handleFilterChange(column.dataIndex, e.target.value)}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
          />
        );
      
      case 'number':
        return (
          <input
            type="number"
            value={filterValue || ''}
            onChange={(e) => handleFilterChange(column.dataIndex, e.target.value)}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
            placeholder="Filter..."
          />
        );
      
      default:
        return (
          <input
            type="text"
            value={filterValue || ''}
            onChange={(e) => handleFilterChange(column.dataIndex, e.target.value)}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded"
            placeholder="Filter..."
          />
        );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm ${className}`}>
      {/* Header with search and actions */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {searchable && (
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            )}
            
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </Button>

            {(Object.keys(filters).length > 0 || searchTerm) && (
              <Button
                variant="secondary"
                size="small"
                onClick={clearFilters}
                className="text-gray-500"
              >
                Clear All
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {exportable && onExport && (
              <div className="relative group">
                <Button
                  variant="secondary"
                  size="small"
                  className="flex items-center space-x-2"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                  <span>Export</span>
                </Button>
                <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                  <button
                    onClick={() => onExport('csv')}
                    className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50"
                  >
                    Export as CSV
                  </button>
                  <button
                    onClick={() => onExport('excel')}
                    className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50"
                  >
                    Export as Excel
                  </button>
                  <button
                    onClick={() => onExport('pdf')}
                    className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50"
                  >
                    Export as PDF
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Filters row */}
      {showFilters && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {columns.filter(col => col.filterable).map(column => (
              <div key={column.key}>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  {column.title}
                </label>
                {renderFilter(column)}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              {rowSelection && (
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={rowSelection.selectedRowKeys.length === filteredData.length && filteredData.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        const allKeys = filteredData.map((record, index) => getRowKey(record, index));
                        rowSelection.onChange(allKeys, filteredData);
                      } else {
                        rowSelection.onChange([], []);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </th>
              )}
              
              {columns.map(column => (
                <th
                  key={column.key}
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ width: column.width }}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <button
                        onClick={() => handleSort(column.dataIndex)}
                        className="flex flex-col"
                      >
                        <ChevronUpIcon 
                          className={`h-3 w-3 ${
                            sortConfig?.key === column.dataIndex && sortConfig.direction === 'asc'
                              ? 'text-green-600'
                              : 'text-gray-400'
                          }`}
                        />
                        <ChevronDownIcon 
                          className={`h-3 w-3 -mt-1 ${
                            sortConfig?.key === column.dataIndex && sortConfig.direction === 'desc'
                              ? 'text-green-600'
                              : 'text-gray-400'
                          }`}
                        />
                      </button>
                    )}
                  </div>
                </th>
              ))}
              
              {actions && (
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredData.length === 0 ? (
              <tr>
                <td 
                  colSpan={columns.length + (rowSelection ? 1 : 0) + (actions ? 1 : 0)}
                  className="px-4 py-8 text-center text-gray-500"
                >
                  {emptyText}
                </td>
              </tr>
            ) : (
              filteredData.map((record, index) => {
                const key = getRowKey(record, index);
                const isSelected = rowSelection?.selectedRowKeys.includes(key);
                
                return (
                  <tr 
                    key={key}
                    className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}
                  >
                    {rowSelection && (
                      <td className="px-4 py-4">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) => {
                            if (e.target.checked) {
                              rowSelection.onChange(
                                [...rowSelection.selectedRowKeys, key],
                                [...(rowSelection.selectedRowKeys.map(k => 
                                  filteredData.find((r, i) => getRowKey(r, i) === k)
                                ).filter(Boolean) as T[]), record]
                              );
                            } else {
                              rowSelection.onChange(
                                rowSelection.selectedRowKeys.filter(k => k !== key),
                                rowSelection.selectedRowKeys
                                  .filter(k => k !== key)
                                  .map(k => filteredData.find((r, i) => getRowKey(r, i) === k))
                                  .filter(Boolean) as T[]
                              );
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                      </td>
                    )}
                    
                    {columns.map(column => (
                      <td key={column.key} className="px-4 py-4 text-sm text-gray-900">
                        {renderCell(column, record, index)}
                      </td>
                    ))}
                    
                    {actions && (
                      <td className="px-4 py-4 text-sm">
                        <div className="flex items-center space-x-2">
                          {actions.view && (
                            <button
                              onClick={() => actions.view!(record)}
                              className="text-blue-600 hover:text-blue-800"
                              title="View"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {actions.edit && (
                            <button
                              onClick={() => actions.edit!(record)}
                              className="text-green-600 hover:text-green-800"
                              title="Edit"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {actions.delete && (
                            <button
                              onClick={() => actions.delete!(record)}
                              className="text-red-600 hover:text-red-800"
                              title="Delete"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {actions.custom?.map(action => (
                            <button
                              key={action.key}
                              onClick={() => action.onClick(record)}
                              disabled={action.disabled?.(record)}
                              className="text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                              title={action.label}
                            >
                              {action.icon}
                            </button>
                          ))}
                        </div>
                      </td>
                    )}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {Math.min((pagination.current - 1) * pagination.pageSize + 1, pagination.total)} to{' '}
            {Math.min(pagination.current * pagination.pageSize, pagination.total)} of {pagination.total} results
          </div>
          
          <div className="flex items-center space-x-2">
            {pagination.showSizeChanger && (
              <select
                value={pagination.pageSize}
                onChange={(e) => pagination.onChange(1, parseInt(e.target.value))}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                {pagination.pageSizeOptions?.map(size => (
                  <option key={size} value={size}>
                    {size} / page
                  </option>
                ))}
              </select>
            )}
            
            <Button
              variant="secondary"
              size="small"
              disabled={pagination.current <= 1}
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
            >
              Previous
            </Button>
            
            <span className="text-sm text-gray-700">
              Page {pagination.current} of {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            
            <Button
              variant="secondary"
              size="small"
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
