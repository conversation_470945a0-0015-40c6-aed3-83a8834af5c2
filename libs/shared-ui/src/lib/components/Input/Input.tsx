import React, { useState } from 'react';
import { colors } from '../../theme/colors';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Input label
   */
  label?: string;
  
  /**
   * Helper text displayed below the input
   */
  helperText?: string;
  
  /**
   * Error message
   */
  error?: string;
  
  /**
   * Success state
   */
  success?: boolean;
  
  /**
   * Loading state
   */
  loading?: boolean;
  
  /**
   * Icon to display (start position)
   */
  startIcon?: React.ReactNode;
  
  /**
   * Icon to display (end position)
   */
  endIcon?: React.ReactNode;
  
  /**
   * Full width input
   */
  fullWidth?: boolean;
  
  /**
   * Input size
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Whether to show character count
   */
  showCharCount?: boolean;
  
  /**
   * Maximum character count
   */
  maxLength?: number;
  
  /**
   * Whether the end icon is clickable
   */
  clickableEndIcon?: boolean;
  
  /**
   * Callback for end icon click
   */
  onEndIconClick?: () => void;
}

export const Input: React.FC<InputProps> = ({
  label,
  helperText,
  error,
  success,
  loading,
  startIcon,
  endIcon,
  fullWidth = false,
  size = 'medium',
  disabled,
  className = '',
  id,
  showCharCount = false,
  maxLength,
  clickableEndIcon = false,
  onEndIconClick,
  value = '',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  
  const getInputClasses = () => {
    const baseClasses = `
      block w-full rounded-xl border-0 py-2 px-3 text-gray-900 shadow-sm 
      ring-1 ring-inset placeholder:text-gray-400 
      transition-all duration-200 ease-in-out
      disabled:opacity-50 disabled:cursor-not-allowed
      focus:outline-none focus:ring-2 focus:ring-inset
    `;
    
    // Size classes
    const sizeClasses = {
      small: 'text-sm py-1.5 px-2.5',
      medium: 'text-base py-2 px-3',
      large: 'text-lg py-2.5 px-4'
    };
    
    // State classes
    let stateClasses = `
      ring-gray-200 hover:ring-gray-300
      focus:ring-orange-500 
      ${isFocused ? 'ring-orange-500' : ''}
    `;
    
    if (error) {
      stateClasses = `
        ring-red-200 hover:ring-red-300 
        focus:ring-red-500 bg-red-50/30
        ${isFocused ? 'ring-red-500' : ''}
      `;
    } else if (success) {
      stateClasses = `
        ring-green-200 hover:ring-green-300 
        focus:ring-green-500 bg-green-50/30
        ${isFocused ? 'ring-green-500' : ''}
      `;
    }
    
    const widthClass = fullWidth ? 'w-full' : '';
    const iconPadding = startIcon ? 'pl-10' : endIcon ? 'pr-10' : '';
    
    return `${baseClasses} ${sizeClasses[size]} ${stateClasses} ${widthClass} ${iconPadding} ${className}`.trim();
  };

  const getContainerClasses = () => {
    return `${fullWidth ? 'w-full' : ''} group`;
  };

  const getLabelClasses = () => {
    return `
      block text-sm font-medium leading-6 
      transition-colors duration-200
      ${error ? 'text-red-600' : success ? 'text-green-600' : 'text-gray-900'}
      ${isFocused ? 'text-orange-600' : ''}
    `;
  };

  const getIconContainerClasses = (isStart: boolean) => {
    const position = isStart ? 'left-0 pl-3' : 'right-0 pr-3';
    return `
      absolute inset-y-0 ${position} flex items-center
      transition-colors duration-200
      ${clickableEndIcon && !isStart ? 'cursor-pointer' : 'pointer-events-none'}
      ${isFocused ? 'text-orange-500' : 'text-gray-400'}
      ${error ? 'text-red-500' : success ? 'text-green-500' : ''}
    `;
  };

  const handleEndIconClick = (e: React.MouseEvent) => {
    if (clickableEndIcon && onEndIconClick) {
      e.stopPropagation();
      onEndIconClick();
    }
  };

  return (
    <div className={getContainerClasses()}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={inputId} 
          className={getLabelClasses()}
        >
          {label}
          {props.required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      {/* Input Container */}
      <div className="relative mt-1">
        {/* Start Icon */}
        {startIcon && (
          <div className={getIconContainerClasses(true)}>
            {startIcon}
          </div>
        )}
        
        {/* Input Field */}
        <input
          id={inputId}
          className={getInputClasses()}
          disabled={disabled || loading}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          value={value}
          {...props}
        />
        
        {/* End Icon or Loading Spinner */}
        {(endIcon || loading) && (
          <div 
            className={getIconContainerClasses(false)}
            onClick={handleEndIconClick}
          >
            {loading ? (
              <svg
                className="animate-spin h-5 w-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            ) : (
              endIcon
            )}
          </div>
        )}

        {/* Character Count */}
        {showCharCount && maxLength && (
          <div className="absolute right-3 -bottom-6 text-xs text-gray-400">
            {value.toString().length}/{maxLength}
          </div>
        )}
      </div>
      
      {/* Helper Text or Error */}
      {(helperText || error) && (
        <p 
          className={`
            mt-2 text-sm transition-colors duration-200
            ${error ? 'text-red-600' : success ? 'text-green-600' : 'text-gray-500'}
          `}
          id={`${inputId}-description`}
        >
          {error || helperText}
        </p>
      )}
    </div>
  );
};

export default Input; 