import React, { useState, useEffect, useCallback } from 'react';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  XMarkIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { Input } from '../Input';
import { Button } from '../Button';

export interface SearchFilter {
  key: string;
  label: string;
  type: 'text' | 'select' | 'multiselect' | 'date' | 'daterange' | 'number' | 'numberrange' | 'location';
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
  icon?: React.ReactNode;
}

export interface LocationFilter {
  latitude?: number;
  longitude?: number;
  radius?: number;
  address?: string;
}

export interface AdvancedSearchProps {
  placeholder?: string;
  filters?: SearchFilter[];
  onSearch: (query: string, filters: Record<string, any>) => void;
  onClear?: () => void;
  loading?: boolean;
  showSuggestions?: boolean;
  suggestions?: string[];
  onSuggestionClick?: (suggestion: string) => void;
  className?: string;
  defaultQuery?: string;
  defaultFilters?: Record<string, any>;
}

export function AdvancedSearch({
  placeholder = "Search...",
  filters = [],
  onSearch,
  onClear,
  loading = false,
  showSuggestions = false,
  suggestions = [],
  onSuggestionClick,
  className = '',
  defaultQuery = '',
  defaultFilters = {}
}: AdvancedSearchProps) {
  const [query, setQuery] = useState(defaultQuery);
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>(defaultFilters);
  const [showFilters, setShowFilters] = useState(false);
  const [showSuggestionsList, setShowSuggestionsList] = useState(false);

  // Debounced search
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleSearch = useCallback(() => {
    onSearch(query, activeFilters);
  }, [query, activeFilters, onSearch]);

  const debouncedSearch = useCallback(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    const timeout = setTimeout(() => {
      handleSearch();
    }, 300);
    
    setSearchTimeout(timeout);
  }, [handleSearch, searchTimeout]);

  useEffect(() => {
    if (query || Object.keys(activeFilters).length > 0) {
      debouncedSearch();
    }
    
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [query, activeFilters, debouncedSearch, searchTimeout]);

  const handleFilterChange = (key: string, value: any) => {
    setActiveFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const removeFilter = (key: string) => {
    setActiveFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  };

  const clearAll = () => {
    setQuery('');
    setActiveFilters({});
    onClear?.();
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestionsList(false);
    onSuggestionClick?.(suggestion);
  };

  const renderFilterInput = (filter: SearchFilter) => {
    const value = activeFilters[filter.key];

    switch (filter.type) {
      case 'select':
        return (
          <select
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="">All</option>
            {filter.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'multiselect':
        return (
          <div className="space-y-2">
            {filter.options?.map(option => (
              <label key={option.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={Array.isArray(value) ? value.includes(option.value) : false}
                  onChange={(e) => {
                    const currentValues = Array.isArray(value) ? value : [];
                    if (e.target.checked) {
                      handleFilterChange(filter.key, [...currentValues, option.value]);
                    } else {
                      handleFilterChange(filter.key, currentValues.filter(v => v !== option.value));
                    }
                  }}
                  className="rounded border-gray-300"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'date':
        return (
          <input
            type="date"
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.key, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        );

      case 'daterange':
        return (
          <div className="grid grid-cols-2 gap-2">
            <input
              type="date"
              value={value?.start || ''}
              onChange={(e) => handleFilterChange(filter.key, { ...value, start: e.target.value })}
              placeholder="Start date"
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <input
              type="date"
              value={value?.end || ''}
              onChange={(e) => handleFilterChange(filter.key, { ...value, end: e.target.value })}
              placeholder="End date"
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        );

      case 'number':
        return (
          <input
            type="number"
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.key, e.target.value)}
            placeholder={filter.placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        );

      case 'numberrange':
        return (
          <div className="grid grid-cols-2 gap-2">
            <input
              type="number"
              value={value?.min || ''}
              onChange={(e) => handleFilterChange(filter.key, { ...value, min: e.target.value })}
              placeholder="Min"
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <input
              type="number"
              value={value?.max || ''}
              onChange={(e) => handleFilterChange(filter.key, { ...value, max: e.target.value })}
              placeholder="Max"
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        );

      case 'location':
        return (
          <div className="space-y-2">
            <input
              type="text"
              value={value?.address || ''}
              onChange={(e) => handleFilterChange(filter.key, { ...value, address: e.target.value })}
              placeholder="Enter location"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <input
              type="number"
              value={value?.radius || ''}
              onChange={(e) => handleFilterChange(filter.key, { ...value, radius: e.target.value })}
              placeholder="Radius (km)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        );

      default:
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.key, e.target.value)}
            placeholder={filter.placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        );
    }
  };

  const getFilterIcon = (type: string) => {
    switch (type) {
      case 'location':
        return <MapPinIcon className="h-4 w-4" />;
      case 'date':
      case 'daterange':
        return <CalendarIcon className="h-4 w-4" />;
      case 'number':
      case 'numberrange':
        return <CurrencyDollarIcon className="h-4 w-4" />;
      default:
        return <FunnelIcon className="h-4 w-4" />;
    }
  };

  const activeFilterCount = Object.keys(activeFilters).filter(key => {
    const value = activeFilters[key];
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null && v !== undefined);
    }
    return value !== '' && value !== null && value !== undefined;
  }).length;

  return (
    <div className={`relative ${className}`}>
      {/* Main search bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          type="text"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            if (showSuggestions && e.target.value) {
              setShowSuggestionsList(true);
            } else {
              setShowSuggestionsList(false);
            }
          }}
          onFocus={() => {
            if (showSuggestions && query) {
              setShowSuggestionsList(true);
            }
          }}
          onBlur={() => {
            // Delay hiding to allow suggestion clicks
            setTimeout(() => setShowSuggestionsList(false), 200);
          }}
          placeholder={placeholder}
          className="w-full pl-10 pr-20 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center space-x-2 pr-3">
          {loading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center space-x-1 ${activeFilterCount > 0 ? 'text-green-600' : 'text-gray-500'}`}
          >
            <FunnelIcon className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="bg-green-100 text-green-800 text-xs rounded-full px-2 py-1">
                {activeFilterCount}
              </span>
            )}
          </Button>
          
          {(query || activeFilterCount > 0) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="text-gray-500 hover:text-gray-700"
            >
              <XMarkIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Suggestions dropdown */}
      {showSuggestionsList && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full text-left px-4 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
            >
              <div className="flex items-center space-x-2">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                <span>{suggestion}</span>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Active filters display */}
      {activeFilterCount > 0 && (
        <div className="mt-3 flex flex-wrap gap-2">
          {Object.entries(activeFilters).map(([key, value]) => {
            if (!value || (Array.isArray(value) && value.length === 0)) return null;
            
            const filter = filters.find(f => f.key === key);
            if (!filter) return null;

            let displayValue = value;
            if (Array.isArray(value)) {
              displayValue = value.join(', ');
            } else if (typeof value === 'object') {
              displayValue = Object.entries(value)
                .filter(([, v]) => v)
                .map(([k, v]) => `${k}: ${v}`)
                .join(', ');
            }

            return (
              <div
                key={key}
                className="inline-flex items-center space-x-1 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm"
              >
                {filter.icon || getFilterIcon(filter.type)}
                <span>{filter.label}: {displayValue}</span>
                <button
                  onClick={() => removeFilter(key)}
                  className="ml-1 hover:text-green-600"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* Filters panel */}
      {showFilters && filters.length > 0 && (
        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filters.map(filter => (
              <div key={filter.key}>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <div className="flex items-center space-x-2">
                    {filter.icon || getFilterIcon(filter.type)}
                    <span>{filter.label}</span>
                  </div>
                </label>
                {renderFilterInput(filter)}
              </div>
            ))}
          </div>
          
          <div className="mt-4 flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(false)}
            >
              Close
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActiveFilters({})}
            >
              Clear Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
