/**
 * Shared UI Components Barrel Export
 * Centralized exports for BeFarma platform UI components
 */

// Core UI Components (Phase 1.2 - Completed)
export * from './Button';
export * from './Input';
export * from './Card';
export * from './Alert';
export * from './Modal';
export * from './Spinner';
export * from './Checkbox';
export * from './DataTable';
export * from './DashboardGrid';
export * from './Footer';
export * from './Header';
export * from './Navigation';
export * from './Sidebar';
export * from './StatusBadge';
export * from './StatsCard';

// Application Shell Components (Phase 2.2 - In Progress)
export * from './Header';
export * from './Sidebar';
export * from './Footer';
export * from './Navigation';
export * from './Layout';

// Professional Dashboard Components (Enhanced)
export * from './DashboardGrid';
export * from './StatsCard';
export * from './StatusBadge';
export * from './DataTable';

// Advanced Components (New)
export * from './AdvancedDataTable';
export * from './AdvancedSearch';
export * from './Charts';

// Re-export individual components for convenience (only for components with default exports)
export { default as Button } from './Button';
export { default as Input } from './Input';
export { default as Card } from './Card';
export { default as Modal } from './Modal';
export { default as Spinner } from './Spinner';
export { default as Alert } from './Alert';
export { default as Layout } from './Layout';
export { default as DashboardGrid } from './DashboardGrid';
export { default as StatsCard } from './StatsCard';
export { default as StatusBadge } from './StatusBadge';
export { default as DataTable } from './DataTable';
