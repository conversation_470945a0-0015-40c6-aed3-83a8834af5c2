import React, { ReactNode, useMemo } from 'react';

export interface DashboardGridProps {
  children: ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'small' | 'medium' | 'large';
  className?: string;
}

// ✅ FIXED: Move static objects outside component to prevent re-creation
const gapClasses = {
  small: 'gap-4',
  medium: 'gap-6',
  large: 'gap-8'
};

const columnClasses = {
  1: 'grid-cols-1',
  2: 'grid-cols-1 sm:grid-cols-2',
  3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
  4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
};

const DashboardGrid: React.FC<DashboardGridProps> = ({
  children,
  columns = 4,
  gap = 'medium',
  className = ''
}) => {

  // ✅ FIXED: Memoize children mapping to prevent style object re-creation
  const childrenWithAnimation = useMemo(() =>
    React.Children.map(children, (child, index) => (
      <div
        key={index}
        className="animate-slideIn"
        style={{
          animationDelay: `${index * 100}ms`,
          animationFillMode: 'both'
        }}
      >
        {child}
      </div>
    )), [children]);

  return (
    <div
      className={`
        grid ${columnClasses[columns]} ${gapClasses[gap]} ${className}
        animate-fadeIn
      `}
    >
      {childrenWithAnimation}
    </div>
  );
};

export default DashboardGrid; 