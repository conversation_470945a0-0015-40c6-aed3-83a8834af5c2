import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronLeft, ChevronRight } from 'lucide-react';

export interface NavigationItem {
  name: string;
  path: string;
  icon?: React.ReactNode;
}

interface SidebarProps {
  navigation: NavigationItem[];
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  navigation,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const location = useLocation();
  
  return (
    <aside 
      className={`fixed left-0 top-0 h-screen bg-white border-r border-gray-200 shadow-sm transition-all duration-300 ease-in-out ${
        isCollapsed ? 'w-16' : 'w-64'
      }`}
    >
      {/* Toggle Button */}
      <button
        onClick={onToggleCollapse}
        className="absolute -right-3 top-16 bg-white border border-gray-200 rounded-full p-1 shadow-sm hover:shadow-md transition-shadow duration-200"
        aria-label={isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}
      >
        {isCollapsed ? (
          <ChevronRight className="w-4 h-4 text-gray-600" />
        ) : (
          <ChevronLeft className="w-4 h-4 text-gray-600" />
        )}
      </button>

      {/* Logo Area */}
      <div className={`h-16 flex items-center ${isCollapsed ? 'justify-center' : 'px-4'} border-b border-gray-200`}>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-lg">🌱</span>
          </div>
          {!isCollapsed && (
            <span className="font-semibold text-gray-800">BeFarma</span>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-2 pt-4">
        <ul className="space-y-2">
          {navigation.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive 
                      ? 'bg-green-50 text-green-600' 
                      : 'text-gray-700 hover:bg-gray-50'
                  } ${
                    isCollapsed ? 'justify-center' : ''
                  }`}
                  title={isCollapsed ? item.name : undefined}
                >
                  <div className="w-5 h-5">
                    {item.icon}
                  </div>
                  {!isCollapsed && (
                    <span className="truncate">{item.name}</span>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Bottom Area - can be used for user profile, settings, etc. */}
      <div className={`absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 ${
        isCollapsed ? 'flex justify-center' : ''
      }`}>
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
          <span className="text-sm">👤</span>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar; 