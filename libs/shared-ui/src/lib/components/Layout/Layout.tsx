import React, { ReactNode, useState } from 'react';
import { Header } from '../Header';
import { Sidebar, NavigationItem } from '../Sidebar';
import { Footer } from '../Footer';

interface LayoutProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  headerActions?: ReactNode;
  showSidebar?: boolean;
  contentPadding?: 'none' | 'small' | 'medium' | 'large';
  navigation?: NavigationItem[];
  userEmail?: string;
  userRole?: string;
  onLogout?: () => void;
  notifications?: {
    id: string;
    title: string;
    message: string;
    time: string;
    read: boolean;
  }[];
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  title,
  subtitle,
  headerActions,
  showSidebar = true,
  contentPadding = 'medium',
  navigation = [],
  userEmail,
  userRole,
  onLogout,
  notifications = []
}) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const paddingClasses = {
    none: 'p-0',
    small: 'p-4',
    medium: 'p-6',
    large: 'p-8'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {showSidebar && (
        <Sidebar 
          navigation={navigation} 
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
        />
      )}
      
      <div className={`transition-all duration-300 ${
        showSidebar ? (isSidebarCollapsed ? 'ml-16' : 'ml-64') : ''
      }`}>
        <Header 
          userEmail={userEmail}
          userRole={userRole}
          onLogout={onLogout}
          notifications={notifications}
        />
        
        <main className={`min-h-[calc(100vh-4rem)] ${paddingClasses[contentPadding]}`}>
          {(title || subtitle || headerActions) && (
            <div className="mb-6">
              {title && <h1 className="text-2xl font-semibold text-gray-800">{title}</h1>}
              {subtitle && <p className="mt-1 text-gray-600">{subtitle}</p>}
              {headerActions && <div className="mt-4">{headerActions}</div>}
            </div>
          )}
          {children}
        </main>
        
        <Footer />
      </div>
    </div>
  );
};

export default Layout; 