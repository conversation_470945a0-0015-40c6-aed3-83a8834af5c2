import React from 'react';
import { colors } from '../../theme/colors';

export interface SpinnerProps {
  /**
   * Size of the spinner
   */
  size?: 'small' | 'medium' | 'large' | 'xl';
  
  /**
   * Color variant
   */
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  
  /**
   * Loading text to display
   */
  text?: string;
  
  /**
   * Custom color (overrides variant)
   */
  color?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Whether to center the spinner
   */
  center?: boolean;
}

export const Spinner: React.FC<SpinnerProps> = ({
  size = 'medium',
  variant = 'primary',
  text,
  color,
  className = '',
  center = false,
}) => {
  const getSizeClasses = () => {
    const sizeClasses = {
      small: 'w-4 h-4',
      medium: 'w-6 h-6',
      large: 'w-8 h-8',
      xl: 'w-12 h-12'
    };
    return sizeClasses[size];
  };

  const getColorClasses = () => {
    const colorClasses = {
      primary: 'border-orange-200 border-t-orange-600',
      secondary: 'border-green-200 border-t-green-600',
      success: 'border-green-200 border-t-green-600',
      warning: 'border-yellow-200 border-t-yellow-600',
      error: 'border-red-200 border-t-red-600',
    };
    
    return colorClasses[variant];
  };

  const getCustomStyle = () => {
    if (color) {
      return {
        borderColor: `${color}20`, // Light version for background
        borderTopColor: color,
      };
    }
    return undefined;
  };

  const getTextSizeClasses = () => {
    const textSizeClasses = {
      small: 'text-sm',
      medium: 'text-base',
      large: 'text-lg',
      xl: 'text-xl'
    };
    return textSizeClasses[size];
  };

  const spinnerElement = (
    <div
      className={`
        inline-block animate-spin rounded-full border-2 border-solid
        ${getSizeClasses()}
        ${!color ? getColorClasses() : ''}
        ${className}
      `.trim()}
      style={getCustomStyle()}
      role="status"
      aria-label={text || 'Loading'}
    >
      <span className="sr-only">{text || 'Loading...'}</span>
    </div>
  );

  const content = (
    <div className={`inline-flex items-center space-x-2 ${center ? 'justify-center' : ''}`}>
      {spinnerElement}
      {text && (
        <span className={`text-gray-600 ${getTextSizeClasses()}`}>
          {text}
        </span>
      )}
    </div>
  );

  if (center) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        {content}
      </div>
    );
  }

  return content;
};

// Agricultural-themed spinner variants
export const FarmSpinner: React.FC<Omit<SpinnerProps, 'variant'>> = (props) => (
  <Spinner {...props} variant="success" />
);

export const CropSpinner: React.FC<Omit<SpinnerProps, 'variant'>> = (props) => (
  <Spinner {...props} variant="primary" />
);

export const PlotSpinner: React.FC<Omit<SpinnerProps, 'variant'>> = (props) => (
  <Spinner {...props} variant="secondary" />
);

// Full-page loading spinner
export const PageSpinner: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50">
    <Spinner size="xl" variant="primary" text={text} center />
  </div>
);

export default Spinner; 