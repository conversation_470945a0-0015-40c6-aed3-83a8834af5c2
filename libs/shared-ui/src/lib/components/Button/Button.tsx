import React from 'react';
import { colors } from '../../theme/colors';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Button variant - primary (orange/large) or secondary (green/small)
   */
  variant?: 'primary' | 'secondary';
  
  /**
   * Size of the button
   */
  size?: 'small' | 'large';
  
  /**
   * Loading state
   */
  loading?: boolean;
  
  /**
   * Icon to display (optional)
   */
  icon?: React.ReactNode;
  
  /**
   * Full width button
   */
  fullWidth?: boolean;
  
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'large',
  loading = false,
  icon,
  fullWidth = false,
  disabled,
  className = '',
  children,
  ...props
}) => {
  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    
    // Size classes
    const sizeClasses = {
      small: 'px-3 py-2 text-sm min-h-[44px]', // Minimum touch target
      large: 'px-6 py-3 text-base min-h-[48px]'
    };
    
    // Variant classes following agricultural theme
    const variantClasses = {
      primary: `bg-[${colors.primaryOrange}] text-white hover:bg-orange-600 focus:ring-orange-500 active:bg-orange-700`,
      secondary: `bg-[${colors.accentGreen}] text-white hover:bg-green-600 focus:ring-green-500 active:bg-green-700`
    };
    
    const widthClass = fullWidth ? 'w-full' : '';
    
    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${widthClass} ${className}`.trim();
  };

  return (
    <button
      className={getButtonClasses()}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      
      {!loading && icon && (
        <span className="mr-2 flex-shrink-0">
          {icon}
        </span>
      )}
      
      <span>{children}</span>
    </button>
  );
};

export default Button; 