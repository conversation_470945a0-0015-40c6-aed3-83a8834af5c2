import React from 'react';
import { ArrowUp, ArrowDown, ArrowR<PERSON> } from 'lucide-react';

export interface StatsCardProps {
  title: string;
  value: string | number;
  trend?: string;
  trendDirection?: 'up' | 'down' | 'neutral';
  icon?: React.ReactNode;
  color?: 'orange' | 'green' | 'blue' | 'purple' | 'indigo' | 'pink' | 'yellow' | 'red';
  subtitle?: string;
  loading?: boolean;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  trend,
  trendDirection = 'neutral',
  icon,
  color = 'orange',
  subtitle,
  loading = false
}) => {
  const colorClasses = {
    orange: {
      iconBg: 'bg-gradient-to-br from-orange-500/10 to-orange-600/10',
      iconColor: 'text-orange-600',
      border: 'border-orange-100',
      shadow: 'group-hover:shadow-orange-500/10',
      ring: 'group-hover:ring-orange-500/20'
    },
    green: {
      iconBg: 'bg-gradient-to-br from-green-500/10 to-green-600/10',
      iconColor: 'text-green-600',
      border: 'border-green-100',
      shadow: 'group-hover:shadow-green-500/10',
      ring: 'group-hover:ring-green-500/20'
    },
    blue: {
      iconBg: 'bg-gradient-to-br from-blue-500/10 to-blue-600/10',
      iconColor: 'text-blue-600',
      border: 'border-blue-100',
      shadow: 'group-hover:shadow-blue-500/10',
      ring: 'group-hover:ring-blue-500/20'
    },
    purple: {
      iconBg: 'bg-gradient-to-br from-purple-500/10 to-purple-600/10',
      iconColor: 'text-purple-600',
      border: 'border-purple-100',
      shadow: 'group-hover:shadow-purple-500/10',
      ring: 'group-hover:ring-purple-500/20'
    },
    indigo: {
      iconBg: 'bg-gradient-to-br from-indigo-500/10 to-indigo-600/10',
      iconColor: 'text-indigo-600',
      border: 'border-indigo-100',
      shadow: 'group-hover:shadow-indigo-500/10',
      ring: 'group-hover:ring-indigo-500/20'
    },
    pink: {
      iconBg: 'bg-gradient-to-br from-pink-500/10 to-pink-600/10',
      iconColor: 'text-pink-600',
      border: 'border-pink-100',
      shadow: 'group-hover:shadow-pink-500/10',
      ring: 'group-hover:ring-pink-500/20'
    },
    yellow: {
      iconBg: 'bg-gradient-to-br from-yellow-500/10 to-yellow-600/10',
      iconColor: 'text-yellow-600',
      border: 'border-yellow-100',
      shadow: 'group-hover:shadow-yellow-500/10',
      ring: 'group-hover:ring-yellow-500/20'
    },
    red: {
      iconBg: 'bg-gradient-to-br from-red-500/10 to-red-600/10',
      iconColor: 'text-red-600',
      border: 'border-red-100',
      shadow: 'group-hover:shadow-red-500/10',
      ring: 'group-hover:ring-red-500/20'
    }
  };

  const trendClasses = {
    up: 'text-green-600 bg-green-50',
    down: 'text-red-600 bg-red-50',
    neutral: 'text-gray-600 bg-gray-50'
  };

  const TrendIcon = ({ direction }: { direction: 'up' | 'down' | 'neutral' }) => {
    switch (direction) {
      case 'up':
        return <ArrowUp className="w-4 h-4" />;
      case 'down':
        return <ArrowDown className="w-4 h-4" />;
      default:
        return <ArrowRight className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl border border-gray-100 p-6 animate-pulse">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
          <div className="ml-4 flex-1">
            <div className="h-4 bg-gray-200 rounded-full w-1/2 mb-2"></div>
            <div className="h-6 bg-gray-200 rounded-full w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`
        group bg-white rounded-2xl border ${colorClasses[color].border} p-6 
        transition-all duration-300 ease-in-out
        hover:shadow-lg ${colorClasses[color].shadow}
        hover:ring-2 ${colorClasses[color].ring}
        hover:-translate-y-0.5
      `}
    >
      <div className="flex items-center">
        {icon && (
          <div 
            className={`
              flex-shrink-0 ${colorClasses[color].iconBg} ${colorClasses[color].iconColor}
              p-3 rounded-xl transition-transform duration-300
              group-hover:scale-110 group-hover:rotate-3
            `}
          >
            {icon}
          </div>
        )}
        
        <div className={`${icon ? 'ml-4' : ''} flex-1 min-w-0`}>
          <p className="text-sm font-medium text-gray-600 truncate">
            {title}
          </p>
          
          <div className="mt-1.5 flex items-baseline">
            <p className="text-2xl font-bold tracking-tight text-gray-900">
              {value}
            </p>
            
            {trend && (
              <div 
                className={`
                  ml-2.5 flex items-center text-sm font-medium ${trendClasses[trendDirection]}
                  px-2 py-0.5 rounded-full
                `}
              >
                <TrendIcon direction={trendDirection} />
                <span className="ml-1">{trend}</span>
              </div>
            )}
          </div>
          
          {subtitle && (
            <p className="mt-1.5 text-sm text-gray-500">
              {subtitle}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsCard; 