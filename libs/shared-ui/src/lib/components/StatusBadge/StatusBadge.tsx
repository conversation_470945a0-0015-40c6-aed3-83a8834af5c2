import React from 'react';
import { cn } from '../../utils';

export interface StatusBadgeProps {
  status: 'healthy' | 'degraded' | 'down' | string;
  className?: string;
}

export default function StatusBadge({ status, className }: StatusBadgeProps) {
  const baseClasses = 'px-2.5 py-1 rounded-full text-xs font-medium';
  
  const statusClasses = {
    healthy: 'bg-green-100 text-green-800',
    degraded: 'bg-yellow-100 text-yellow-800',
    down: 'bg-red-100 text-red-800',
    default: 'bg-gray-100 text-gray-800'
  };

  return (
    <span className={cn(baseClasses, statusClasses[status as keyof typeof statusClasses] || statusClasses.default, className)}>
      {status?.charAt(0)?.toUpperCase() + status?.slice(1)}
    </span>
  );
}; 