import React from 'react';
import { colors } from '../../theme/colors';

export interface AlertProps {
  /**
   * Alert variant/type
   */
  variant?: 'success' | 'warning' | 'error' | 'info' | 'healthy' | 'critical';
  
  /**
   * Alert title
   */
  title?: string;
  
  /**
   * Alert message/content
   */
  message: string;
  
  /**
   * Whether the alert can be dismissed
   */
  dismissible?: boolean;
  
  /**
   * Callback when alert is dismissed
   */
  onDismiss?: () => void;
  
  /**
   * Optional icon
   */
  icon?: React.ReactNode;
  
  /**
   * Alert size
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Additional actions (buttons)
   */
  actions?: React.ReactNode;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Children content
   */
  children?: React.ReactNode;
}

export const Alert: React.FC<AlertProps> = ({
  variant = 'info',
  title,
  message,
  dismissible = false,
  onDismiss,
  icon,
  size = 'medium',
  actions,
  className = '',
  children,
}) => {
  const getAlertClasses = () => {
    const baseClasses = 'rounded-md border p-4';
    
    // Size classes
    const sizeClasses = {
      small: 'p-3 text-sm',
      medium: 'p-4 text-base',
      large: 'p-6 text-lg'
    };
    
    // Variant classes following agricultural theme
    const variantClasses = {
      success: 'bg-green-50 border-green-200 text-green-800',
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      error: 'bg-red-50 border-red-200 text-red-800',
      info: 'bg-blue-50 border-blue-200 text-blue-800',
      healthy: 'bg-green-50 border-green-200 text-green-800', // For crop health
      critical: 'bg-red-50 border-red-200 text-red-800', // For critical crop issues
    };
    
    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`.trim();
  };

  const getDefaultIcon = () => {
    const iconClasses = 'w-5 h-5 flex-shrink-0';
    
    const defaultIcons = {
      success: (
        <svg className={`${iconClasses} text-green-600`} fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      ),
      warning: (
        <svg className={`${iconClasses} text-yellow-600`} fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      ),
      error: (
        <svg className={`${iconClasses} text-red-600`} fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      ),
      info: (
        <svg className={`${iconClasses} text-blue-600`} fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
      ),
      healthy: (
        <svg className={`${iconClasses} text-green-600`} fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
        </svg>
      ),
      critical: (
        <svg className={`${iconClasses} text-red-600`} fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      ),
    };
    
    return defaultIcons[variant];
  };

  return (
    <div className={getAlertClasses()} role="alert">
      <div className="flex">
        {/* Icon */}
        <div className="flex-shrink-0">
          {icon || getDefaultIcon()}
        </div>
        
        {/* Content */}
        <div className="ml-3 flex-1">
          {title && (
            <h3 className="font-medium mb-1">
              {title}
            </h3>
          )}
          
          <div className="text-sm">
            {message}
          </div>
          
          {children && (
            <div className="mt-2">
              {children}
            </div>
          )}
          
          {actions && (
            <div className="mt-3">
              {actions}
            </div>
          )}
        </div>
        
        {/* Dismiss Button */}
        {dismissible && (
          <div className="ml-auto pl-3">
            <button
              type="button"
              className="inline-flex rounded-md p-1.5 hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-current"
              onClick={onDismiss}
              aria-label="Dismiss alert"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Agricultural-specific alert components
export const CropHealthAlert: React.FC<Omit<AlertProps, 'variant'> & { health: 'healthy' | 'warning' | 'critical' }> = ({ 
  health, 
  ...props 
}) => {
  const variantMap = {
    healthy: 'healthy' as const,
    warning: 'warning' as const,
    critical: 'critical' as const,
  };
  
  return <Alert {...props} variant={variantMap[health]} />;
};

export const WeatherAlert: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert {...props} variant="warning" />
);

export const SystemAlert: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert {...props} variant="info" />
);

export default Alert; 