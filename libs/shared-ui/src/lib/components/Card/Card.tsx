import React from 'react';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Card variant for different agricultural contexts
   */
  variant?: 'default' | 'farm' | 'crop' | 'plot' | 'dashboard';
  
  /**
   * Card size
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Whether the card is interactive (clickable)
   */
  interactive?: boolean;
  
  /**
   * Loading state
   */
  loading?: boolean;
  
  /**
   * Header content
   */
  header?: React.ReactNode;
  
  /**
   * Footer content
   */
  footer?: React.ReactNode;
  
  /**
   * Optional icon for the card
   */
  icon?: React.ReactNode;
  
  /**
   * Card status indicator
   */
  status?: 'healthy' | 'warning' | 'critical' | 'success' | 'info';
  
  children?: React.ReactNode;
}

export const Card: React.FC<CardProps> = ({
  variant = 'default',
  size = 'medium',
  interactive = false,
  loading = false,
  header,
  footer,
  icon,
  status,
  className = '',
  children,
  ...props
}) => {
  const getCardClasses = () => {
    const baseClasses = 'bg-white rounded-2xl border border-gray-100 transition-all duration-300 ease-in-out';
    
    const sizeClasses = {
      small: 'p-4',
      medium: 'p-6',
      large: 'p-8'
    };
    
    const variantClasses = {
      default: '',
      farm: 'bg-gradient-to-br from-green-50/50 to-green-100/50 border-green-100 hover:border-green-200 hover:shadow-lg hover:shadow-green-500/10 hover:ring-2 hover:ring-green-500/20',
      crop: 'bg-gradient-to-br from-orange-50/50 to-orange-100/50 border-orange-100 hover:border-orange-200 hover:shadow-lg hover:shadow-orange-500/10 hover:ring-2 hover:ring-orange-500/20', 
      plot: 'bg-gradient-to-br from-blue-50/50 to-blue-100/50 border-blue-100 hover:border-blue-200 hover:shadow-lg hover:shadow-blue-500/10 hover:ring-2 hover:ring-blue-500/20',
      dashboard: 'hover:shadow-lg hover:shadow-gray-500/10 hover:ring-2 hover:ring-gray-500/20'
    };
    
    const interactiveClasses = interactive 
      ? 'cursor-pointer hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0 active:shadow-md' 
      : '';
    
    const statusClasses = {
      healthy: 'border-l-4 border-l-green-500 bg-gradient-to-br from-green-50/30 to-transparent',
      warning: 'border-l-4 border-l-yellow-500 bg-gradient-to-br from-yellow-50/30 to-transparent',
      critical: 'border-l-4 border-l-red-500 bg-gradient-to-br from-red-50/30 to-transparent',
      success: 'border-l-4 border-l-green-600 bg-gradient-to-br from-green-50/30 to-transparent',
      info: 'border-l-4 border-l-blue-500 bg-gradient-to-br from-blue-50/30 to-transparent'
    };
    
    const statusClass = status ? statusClasses[status] : '';
    
    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${interactiveClasses} ${statusClass} ${className}`.trim();
  };

  if (loading) {
    return (
      <div className={getCardClasses()}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded-full w-3/4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded-full w-full"></div>
            <div className="h-3 bg-gray-200 rounded-full w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded-full w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={getCardClasses()} {...props}>
      {/* Card Header */}
      {(header || icon) && (
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            {icon && (
              <div className={`flex-shrink-0 text-gray-600 transition-transform duration-300 ${interactive ? 'group-hover:scale-110 group-hover:rotate-3' : ''}`}>
                {icon}
              </div>
            )}
            {header && (
              <div className="font-bold text-gray-900 tracking-tight">
                {header}
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Card Content */}
      {children && (
        <div className="text-gray-600">
          {children}
        </div>
      )}
      
      {/* Card Footer */}
      {footer && (
        <div className="mt-6 pt-4 border-t border-gray-100 text-sm text-gray-500">
          {footer}
        </div>
      )}
    </div>
  );
};

// Card sub-components for structured layouts
export const CardHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`font-bold text-gray-900 tracking-tight mb-4 ${className}`}>
    {children}
  </div>
);

export const CardContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`text-gray-600 ${className}`}>
    {children}
  </div>
);

export const CardFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`text-sm text-gray-500 mt-6 pt-4 border-t border-gray-100 ${className}`}>
    {children}
  </div>
);

export default Card; 