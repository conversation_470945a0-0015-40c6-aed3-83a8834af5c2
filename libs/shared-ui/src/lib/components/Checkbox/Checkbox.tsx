import React from 'react';

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  /**
   * Label for the checkbox
   */
  label?: string;
  
  /**
   * Error state
   */
  error?: boolean;
  
  /**
   * Error message
   */
  errorMessage?: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  error,
  errorMessage,
  className = '',
  disabled,
  ...props
}) => {
  return (
    <div className="flex items-start">
      <div className="flex items-center h-5">
        <input
          type="checkbox"
          className={`
            h-4 w-4 rounded
            border-gray-300
            text-green-600
            focus:ring-green-500
            disabled:opacity-50
            disabled:cursor-not-allowed
            ${error ? 'border-red-300' : ''}
            ${className}
          `}
          disabled={disabled}
          {...props}
        />
      </div>
      {label && (
        <div className="ml-2">
          <label
            htmlFor={props.id}
            className={`
              text-sm font-medium
              ${error ? 'text-red-600' : 'text-gray-900'}
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            {label}
          </label>
          {errorMessage && (
            <p className="mt-1 text-sm text-red-600">{errorMessage}</p>
          )}
        </div>
      )}
    </div>
  );
}; 