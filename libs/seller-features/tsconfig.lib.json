{"extends": "./tsconfig.json", "compilerOptions": {"rootDir": "../../", "outDir": "../../dist/out-tsc", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"], "declaration": true}, "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "../shared-ui/tsconfig.lib.json"}, {"path": "../shared-utils/tsconfig.lib.json"}]}