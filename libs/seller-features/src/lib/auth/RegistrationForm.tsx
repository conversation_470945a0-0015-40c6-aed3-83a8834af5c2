import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { 
  Button, 
  Input, 
  <PERSON>,
  Al<PERSON>,
  Spinner
} from '@befarmer-platform/shared-ui';
import { AuthService } from '@befarmer-platform/shared-utils';

interface RegistrationData {
  // Step 1: Personal Information
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;

  // Step 2: Contact Details
  phone: string;
  whatsapp: string;
  alternatePhone?: string;

  // Step 3: Address Information
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;

  // Step 4: Document Upload
  idProof: File | null;
  addressProof: File | null;

  // Step 5: Bank Details
  bankName: string;
  accountNumber: string;
  ifscCode: string;
  accountHolderName: string;

  // Step 6: Farm Basic Info
  farmName: string;
  farmType: 'individual' | 'company' | 'cooperative';
  farmSize: number;
  primaryCrops: string[];
}

const steps = [
  { title: 'Personal Information', subtitle: 'Basic details about yourself' },
  { title: 'Contact Details', subtitle: 'How we can reach you' },
  { title: 'Address', subtitle: 'Your residential address' },
  { title: 'Document Verification', subtitle: 'Upload required documents' },
  { title: 'Bank Account', subtitle: 'For receiving payments' },
  { title: 'Farm Information', subtitle: 'Basic details about your farm' }
];

export const RegistrationForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const { register, handleSubmit, watch, formState: { errors, isSubmitting } } = useForm<RegistrationData>();

  const renderStepIndicator = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between relative">
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;

          return (
            <div key={step.title} className="flex flex-col items-center relative z-10">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center border-2 
                  ${isActive ? 'border-orange-500 bg-orange-50' : 
                    isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300 bg-white'}`}
              >
                {isCompleted ? (
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <span className={`text-sm font-medium ${isActive ? 'text-orange-500' : 'text-gray-500'}`}>
                    {stepNumber}
                  </span>
                )}
              </div>
              <div className="mt-2 text-xs font-medium text-gray-500">{step.title}</div>
            </div>
          );
        })}
        {/* Progress bar */}
        <div className="absolute top-5 h-0.5 w-full bg-gray-200 -z-10">
          <div 
            className="absolute top-0 h-full bg-green-500 transition-all duration-300"
            style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );

  const renderPersonalInfo = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <Input
          label="First Name"
          {...register('firstName', { required: 'First name is required' })}
          error={errors.firstName?.message}
          placeholder="John"
        />
        <Input
          label="Last Name"
          {...register('lastName', { required: 'Last name is required' })}
          error={errors.lastName?.message}
          placeholder="Doe"
        />
      </div>
      <Input
        label="Email Address"
        type="email"
        {...register('email', {
          required: 'Email is required',
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: 'Invalid email address'
          }
        })}
        error={errors.email?.message}
        placeholder="<EMAIL>"
      />
      <Input
        label="Password"
        type="password"
        {...register('password', {
          required: 'Password is required',
          minLength: {
            value: 8,
            message: 'Password must be at least 8 characters'
          }
        })}
        error={errors.password?.message}
        placeholder="••••••••"
      />
      <Input
        label="Confirm Password"
        type="password"
        {...register('confirmPassword', {
          required: 'Please confirm your password',
          validate: value => value === watch('password') || 'Passwords do not match'
        })}
        error={errors.confirmPassword?.message}
        placeholder="••••••••"
      />
    </div>
  );

  const renderContactDetails = () => (
    <div className="space-y-4">
      <Input
        label="Phone Number"
        {...register('phone', {
          required: 'Phone number is required',
          pattern: {
            value: /^\+?[1-9]\d{9,14}$/,
            message: 'Invalid phone number'
          }
        })}
        error={errors.phone?.message}
        placeholder="+91 9876543210"
      />
      <Input
        label="WhatsApp Number"
        {...register('whatsapp', {
          required: 'WhatsApp number is required',
          pattern: {
            value: /^\+?[1-9]\d{9,14}$/,
            message: 'Invalid WhatsApp number'
          }
        })}
        error={errors.whatsapp?.message}
        placeholder="+91 9876543210"
      />
      <Input
        label="Alternate Phone (Optional)"
        {...register('alternatePhone', {
          pattern: {
            value: /^\+?[1-9]\d{9,14}$/,
            message: 'Invalid phone number'
          }
        })}
        error={errors.alternatePhone?.message}
        placeholder="+91 9876543210"
      />
    </div>
  );

  // ... Similar render functions for other steps ...

  const onSubmit = async (data: RegistrationData) => {
    try {
      if (currentStep < steps.length) {
        setCurrentStep(prev => prev + 1);
        return;
      }

      setError(null);
      await AuthService.register(data);
      // Redirect to verification pending page
      window.location.href = '/verification-pending';
    } catch (err) {
      setError('Registration failed. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900">Join BeFarma</h2>
          <p className="mt-2 text-sm text-gray-600">
            {steps[currentStep - 1].subtitle}
          </p>
        </div>

        {renderStepIndicator()}

        <Card className="p-8">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {error && (
              <Alert
                variant="error"
                message={error}
                className="mb-6"
              />
            )}

            {currentStep === 1 && renderPersonalInfo()}
            {currentStep === 2 && renderContactDetails()}
            {/* ... Other step renders ... */}

            <div className="flex justify-between pt-6">
              {currentStep > 1 && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setCurrentStep(prev => prev - 1)}
                >
                  Previous
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                className="ml-auto"
              >
                {currentStep === steps.length ? 'Complete Registration' : 'Next Step'}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default RegistrationForm; 