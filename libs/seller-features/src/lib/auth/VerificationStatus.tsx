import React from 'react';
import { useAuth } from '@befarmer-platform/shared-utils';
import { Card, Alert } from '@befarmer-platform/shared-ui';

export const VerificationStatus: React.FC = () => {
  const { isUserVerified } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center
          ${isUserVerified() ? 'bg-green-500' : 'bg-orange-500'}`}
        >
          {isUserVerified() ? (
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ) : (
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          )}
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          {isUserVerified() ? 'Account Verified' : 'Verification Pending'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {isUserVerified() 
            ? 'Your account has been successfully verified. You can now access all features.'
            : 'Your account is currently under review. We will notify you once the verification is complete.'}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card className="py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {isUserVerified() ? (
            <Alert
              variant="success"
              message="Your account is fully verified. You have access to all platform features."
              className="mb-4"
            />
          ) : (
            <div className="space-y-6">
              <Alert
                variant="warning"
                message="Your account is pending verification. This usually takes 24-48 hours."
                className="mb-4"
              />
              
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-medium text-gray-900">What happens next?</h3>
                <div className="mt-4 space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="ml-3 text-sm text-gray-600">
                      Our team will review your submitted documents
                    </p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="ml-3 text-sm text-gray-600">
                      We'll verify your identity and farm ownership
                    </p>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="ml-3 text-sm text-gray-600">
                      You'll receive an email once verification is complete
                    </p>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-medium text-gray-900">Need help?</h3>
                <p className="mt-2 text-sm text-gray-600">
                  If you have any questions about the verification process, please contact our support team at{' '}
                  <a href="mailto:<EMAIL>" className="text-green-600 hover:text-green-500">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default VerificationStatus; 