import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { 
  Button, 
  Input, 
  Card,
  Alert
} from '@befarmer-platform/shared-ui';
import { AuthService } from '@befarmer-platform/shared-utils';

interface PasswordResetData {
  email: string;
}

interface NewPasswordData {
  password: string;
  confirmPassword: string;
  token: string;
}

export const PasswordResetForm: React.FC<{ token?: string }> = ({ token }) => {
  const [isEmailSent, setIsEmailSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { register, handleSubmit, watch, formState: { errors, isSubmitting } } = 
    useForm<PasswordResetData | NewPasswordData>();

  const onSubmit = async (data: PasswordResetData | NewPasswordData) => {
    try {
      setError(null);
      
      if (token) {
        // Reset password with token
        const { password } = data as NewPasswordData;
        await AuthService.resetPassword(token, password);
        window.location.href = '/seller/login?reset=success';
      } else {
        // Request password reset email
        const { email } = data as PasswordResetData;
        await AuthService.requestPasswordReset(email);
        setIsEmailSent(true);
      }
    } catch (err) {
      setError(token ? 'Failed to reset password. Please try again.' : 'Failed to send reset email. Please try again.');
    }
  };

  const renderRequestForm = () => (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="w-20 h-20 mx-auto bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
          </svg>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Reset Your Password
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Enter your email address and we'll send you a link to reset your password
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card className="py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {isEmailSent ? (
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">Check your email</h3>
              <p className="mt-2 text-sm text-gray-600">
                We've sent you a link to reset your password. Please check your email and follow the instructions.
              </p>
              <Button
                variant="secondary"
                size="large"
                className="mt-6 w-full"
                onClick={() => window.location.href = '/login'}
              >
                Return to Login
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <Alert
                  variant="error"
                  message={error}
                  className="mb-4"
                />
              )}

              <Input
                label="Email Address"
                type="email"
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address'
                  }
                })}
                error={errors.root?.email?.message}
                placeholder="<EMAIL>"
              />

              <Button
                type="submit"
                variant="primary"
                size="large"
                loading={isSubmitting}
                className="w-full"
              >
                Send Reset Link
              </Button>

              <div className="text-center">
                <a 
                  href="/login" 
                  className="text-sm font-medium text-green-600 hover:text-green-500"
                >
                  Return to Login
                </a>
              </div>
            </form>
          )}
        </Card>
      </div>
    </>
  );

  const renderResetForm = () => (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="w-20 h-20 mx-auto bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Create New Password
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Please enter your new password
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card className="py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {error && (
              <Alert
                variant="error"
                message={error}
                className="mb-4"
              />
            )}

            <Input
              label="New Password"
              type="password"
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters'
                }
              })}
              error={errors.root?.password?.message}
              placeholder="••••••••"
            />

            <Input
              label="Confirm New Password"
              type="password"
              {...register('confirmPassword', {
                required: 'Please confirm your password',
                validate: (value: string) => value === watch('password') || 'Passwords do not match'
              })}
              error={errors.root?.confirmPassword?.message}
              placeholder="••••••••"
            />

            <Button
              type="submit"
              variant="primary"
              size="large"
              loading={isSubmitting}
              className="w-full"
            >
              Reset Password
            </Button>
          </form>
        </Card>
      </div>
    </>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {token ? renderResetForm() : renderRequestForm()}
    </div>
  );
};

export default PasswordResetForm; 