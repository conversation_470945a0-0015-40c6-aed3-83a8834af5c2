const { withNx } = require('@nx/rollup/with-nx');
const url = require('@rollup/plugin-url');
const svg = require('@svgr/rollup');

// Fallback configuration for when NX ProjectGraph is not available
const createFallbackConfig = (baseConfig, additionalConfig) => {
  const { resolve } = require('@rollup/plugin-node-resolve');
  const { babel } = require('@rollup/plugin-babel');

  return {
    input: baseConfig.main,
    output: {
      dir: baseConfig.outputPath,
      format: baseConfig.format[0] || 'esm',
      preserveModules: true,
      preserveModulesRoot: 'src',
    },
    external: baseConfig.external,
    plugins: [
      resolve(),
      babel({
        babelHelpers: 'bundled',
        exclude: 'node_modules/**',
      }),
      ...additionalConfig.plugins,
    ],
  };
};

const baseConfig = {
  main: './src/index.ts',
  outputPath: '../../dist/libs/seller-features',
  tsConfig: './tsconfig.lib.json',
  compiler: 'babel',
  external: ['react', 'react-dom', 'react/jsx-runtime'],
  format: ['esm'],
  assets: [{ input: '.', output: '.', glob: 'README.md' }],
};

const additionalConfig = {
  // Provide additional rollup configuration here. See: https://rollupjs.org/configuration-options
  plugins: [
    svg({
      svgo: false,
      titleProp: true,
      ref: true,
    }),
    url({
      limit: 10000, // 10kB
    }),
  ],
};

try {
  module.exports = withNx(baseConfig, additionalConfig);
} catch (error) {
  console.warn('Warning: NX ProjectGraph not available, using fallback configuration');
  module.exports = createFallbackConfig(baseConfig, additionalConfig);
}
