{"extends": "./tsconfig.json", "compilerOptions": {"rootDir": "../../", "outDir": "../../dist/out-tsc", "module": "node16", "moduleResolution": "node16", "jsx": "react-jsx", "types": ["jest", "node"]}, "include": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.ts"]}