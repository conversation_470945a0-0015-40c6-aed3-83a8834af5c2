{"graph": {"nodes": {"@befarmer-platform/seller-admin-app": {"name": "@befarmer-platform/seller-admin-app", "type": "app", "data": {"root": "apps/seller-admin-app", "projectType": "application", "targets": {"typecheck": {"dependsOn": ["^typecheck"], "options": {"cwd": "apps/seller-admin-app", "command": "tsc --build --emitDeclarationOnly"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.app.json", "{projectRoot}/tsconfig.spec.json", "{projectRoot}/src/**/*.js", "{projectRoot}/src/**/*.jsx", "{projectRoot}/src/**/*.ts", "{projectRoot}/src/**/*.tsx", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.test.ts", "{projectRoot}/src/**/*.spec.ts", "{projectRoot}/src/**/*.test.tsx", "{projectRoot}/src/**/*.spec.tsx", "{projectRoot}/src/**/*.test.js", "{projectRoot}/src/**/*.spec.js", "{projectRoot}/src/**/*.test.jsx", "{projectRoot}/src/**/*.spec.jsx", "{projectRoot}/src/**/*.d.ts", "!{projectRoot}/out-tsc", "!{projectRoot}/dist", "!{projectRoot}/eslint.config.js", "!{projectRoot}/eslint.config.cjs", "!{projectRoot}/eslint.config.mjs", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/jest/**/*.d.ts", "{projectRoot}/out-tsc/jest/**/*.d.ts.map", "{projectRoot}/out-tsc/jest/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build": {"options": {"cwd": "apps/seller-admin-app", "args": ["--node-env=production"], "command": "webpack-cli build"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps/seller-admin-app/dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "serve": {"continuous": true, "options": {"cwd": "apps/seller-admin-app", "args": ["--node-env=development"], "command": "webpack-cli serve"}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "preview": {"continuous": true, "options": {"cwd": "apps/seller-admin-app", "args": ["--node-env=production"], "command": "webpack-cli serve"}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"], "configurations": {}, "parallelism": true}, "build-deps": {"dependsOn": ["^build"], "configurations": {}, "options": {}, "parallelism": true, "executor": "nx:noop"}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "executor": "nx:run-commands", "options": {"command": "npx nx watch --projects @befarmer-platform/seller-admin-app --includeDependentProjects -- npx nx build-deps @befarmer-platform/seller-admin-app"}, "configurations": {}, "parallelism": true}, "lint": {"cache": true, "options": {"cwd": "apps/seller-admin-app", "command": "eslint ."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "test": {"options": {"cwd": "apps/seller-admin-app", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}, "command": "jest"}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{projectRoot}/test-output/jest/coverage"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true, "dependsOn": ["^build"]}}, "metadata": {"targetGroups": {}, "js": {"packageName": "@befarmer-platform/seller-admin-app", "isInPackageManagerWorkspaces": true}}, "name": "@befarmer-platform/seller-admin-app", "tags": ["npm:private"], "implicitDependencies": []}}, "seller-features": {"name": "seller-features", "type": "lib", "data": {"root": "libs/seller-features", "projectType": "library", "targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "options": {"cwd": "libs/seller-features", "command": "tsc --build --emitDeclarationOnly"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/tsconfig.spec.json", "{projectRoot}/src/**/*.js", "{projectRoot}/src/**/*.jsx", "{projectRoot}/src/**/*.ts", "{projectRoot}/src/**/*.tsx", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.test.ts", "{projectRoot}/src/**/*.spec.ts", "{projectRoot}/src/**/*.test.tsx", "{projectRoot}/src/**/*.spec.tsx", "{projectRoot}/src/**/*.test.js", "{projectRoot}/src/**/*.spec.js", "{projectRoot}/src/**/*.test.jsx", "{projectRoot}/src/**/*.spec.jsx", "{projectRoot}/src/**/*.d.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{workspaceRoot}/dist/out-tsc/**/*.d.ts", "{workspaceRoot}/dist/out-tsc/**/*.d.ts.map", "{workspaceRoot}/dist/out-tsc/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build": {"options": {"cwd": "libs/seller-features", "command": "rollup -c rollup.config.cjs"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["rollup"]}], "outputs": ["{workspaceRoot}/dist/libs/seller-features"], "metadata": {"technologies": ["typescript", "rollup"], "description": "<PERSON> Rollup", "help": {"command": "npx rollup --help", "example": {"options": {"sourcemap": true, "watch": true}}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build-deps": {"dependsOn": ["^build"], "configurations": {}, "options": {}, "parallelism": true, "executor": "nx:noop"}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "executor": "nx:run-commands", "options": {"command": "npx nx watch --projects seller-features --includeDependentProjects -- npx nx build-deps seller-features"}, "configurations": {}, "parallelism": true}, "lint": {"cache": true, "options": {"cwd": "libs/seller-features", "command": "eslint ."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "test": {"options": {"cwd": "libs/seller-features", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}, "command": "jest"}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}/coverage/libs/seller-features"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true, "dependsOn": ["^build"]}, "nx-release-publish": {"executor": "@nx/js:release-publish", "dependsOn": ["^nx-release-publish"], "options": {}, "configurations": {}, "parallelism": true}}, "name": "seller-features", "tags": ["npm:public"], "metadata": {"targetGroups": {}, "js": {"packageName": "@befarmer-platform/seller-features", "isInPackageManagerWorkspaces": true}}, "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/seller-features/src", "implicitDependencies": []}}, "admin-features": {"name": "admin-features", "type": "lib", "data": {"root": "libs/admin-features", "projectType": "library", "targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "options": {"cwd": "libs/admin-features", "command": "tsc --build --emitDeclarationOnly"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/tsconfig.spec.json", "{projectRoot}/src/**/*.js", "{projectRoot}/src/**/*.jsx", "{projectRoot}/src/**/*.ts", "{projectRoot}/src/**/*.tsx", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.test.ts", "{projectRoot}/src/**/*.spec.ts", "{projectRoot}/src/**/*.test.tsx", "{projectRoot}/src/**/*.spec.tsx", "{projectRoot}/src/**/*.test.js", "{projectRoot}/src/**/*.spec.js", "{projectRoot}/src/**/*.test.jsx", "{projectRoot}/src/**/*.spec.jsx", "{projectRoot}/src/**/*.d.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{workspaceRoot}/dist/out-tsc/**/*.d.ts", "{workspaceRoot}/dist/out-tsc/**/*.d.ts.map", "{workspaceRoot}/dist/out-tsc/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build": {"options": {"cwd": "libs/admin-features", "command": "rollup -c rollup.config.cjs"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["rollup"]}], "outputs": ["{workspaceRoot}/dist/libs/admin-features"], "metadata": {"technologies": ["typescript", "rollup"], "description": "<PERSON> Rollup", "help": {"command": "npx rollup --help", "example": {"options": {"sourcemap": true, "watch": true}}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build-deps": {"dependsOn": ["^build"], "configurations": {}, "options": {}, "parallelism": true, "executor": "nx:noop"}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "executor": "nx:run-commands", "options": {"command": "npx nx watch --projects admin-features --includeDependentProjects -- npx nx build-deps admin-features"}, "configurations": {}, "parallelism": true}, "lint": {"cache": true, "options": {"cwd": "libs/admin-features", "command": "eslint ."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "test": {"options": {"cwd": "libs/admin-features", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}, "command": "jest"}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}/coverage/libs/admin-features"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true, "dependsOn": ["^build"]}, "nx-release-publish": {"executor": "@nx/js:release-publish", "dependsOn": ["^nx-release-publish"], "options": {}, "configurations": {}, "parallelism": true}}, "name": "admin-features", "tags": ["npm:public"], "metadata": {"targetGroups": {}, "js": {"packageName": "@befarmer-platform/admin-features", "isInPackageManagerWorkspaces": true}}, "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/admin-features/src", "implicitDependencies": []}}, "shared-utils": {"name": "shared-utils", "type": "lib", "data": {"root": "libs/shared-utils", "projectType": "library", "targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "options": {"cwd": "libs/shared-utils", "command": "tsc --build --emitDeclarationOnly"}, "cache": true, "inputs": ["{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{workspaceRoot}/dist/out-tsc/**/*.d.ts", "{workspaceRoot}/dist/out-tsc/**/*.d.ts.map", "{workspaceRoot}/dist/out-tsc/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build": {"dependsOn": ["^build"], "options": {"cwd": "libs/shared-utils", "command": "tsc --build tsconfig.lib.json"}, "cache": true, "inputs": ["{projectRoot}/tsconfig.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{workspaceRoot}/dist/out-tsc/**/*.d.ts", "{workspaceRoot}/dist/out-tsc/**/*.d.ts.map", "{workspaceRoot}/dist/out-tsc/tsconfig.lib.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Builds the project with `tsc`.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build-deps": {"dependsOn": ["^build"], "configurations": {}, "options": {}, "parallelism": true, "executor": "nx:noop"}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "executor": "nx:run-commands", "options": {"command": "npx nx watch --projects shared-utils --includeDependentProjects -- npx nx build-deps shared-utils"}, "configurations": {}, "parallelism": true}, "lint": {"cache": true, "options": {"cwd": "libs/shared-utils", "command": "eslint ."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}}, "name": "shared-utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-utils/src", "tags": [], "implicitDependencies": []}}, "@befarmer-platform/seller-app": {"name": "@befarmer-platform/seller-app", "type": "app", "data": {"root": "apps/seller-app", "projectType": "application", "targets": {"typecheck": {"dependsOn": ["^typecheck"], "options": {"cwd": "apps/seller-app", "command": "tsc --build --emitDeclarationOnly"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.app.json", "{projectRoot}/tsconfig.storybook.json", "{projectRoot}/src/**/*.js", "{projectRoot}/src/**/*.jsx", "{projectRoot}/src/**/*.ts", "{projectRoot}/src/**/*.tsx", "{projectRoot}/src/**/*.stories.ts", "{projectRoot}/src/**/*.stories.js", "{projectRoot}/src/**/*.stories.jsx", "{projectRoot}/src/**/*.stories.tsx", "{projectRoot}/src/**/*.stories.mdx", "{projectRoot}/.storybook/*.js", "{projectRoot}/.storybook/*.ts", "!{projectRoot}/out-tsc", "!{projectRoot}/dist", "!{projectRoot}/src/**/*.spec.ts", "!{projectRoot}/src/**/*.test.ts", "!{projectRoot}/src/**/*.spec.tsx", "!{projectRoot}/src/**/*.test.tsx", "!{projectRoot}/src/**/*.spec.js", "!{projectRoot}/src/**/*.test.js", "!{projectRoot}/src/**/*.spec.jsx", "!{projectRoot}/src/**/*.test.jsx", "!{projectRoot}/eslint.config.js", "!{projectRoot}/eslint.config.cjs", "!{projectRoot}/eslint.config.mjs", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/**/*.d.ts.map", "{projectRoot}/dist/tsconfig.app.tsbuildinfo", "{projectRoot}/out-tsc/storybook/**/*.d.ts", "{projectRoot}/out-tsc/storybook/**/*.d.ts.map", "{projectRoot}/out-tsc/storybook/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build": {"options": {"cwd": "apps/seller-app", "args": ["--node-env=production"], "command": "webpack-cli build"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps/seller-app/dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "serve": {"continuous": true, "options": {"cwd": "apps/seller-app", "args": ["--node-env=development"], "command": "webpack-cli serve"}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "preview": {"continuous": true, "options": {"cwd": "apps/seller-app", "args": ["--node-env=production"], "command": "webpack-cli serve"}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"], "configurations": {}, "parallelism": true}, "build-deps": {"dependsOn": ["^build"], "configurations": {}, "options": {}, "parallelism": true, "executor": "nx:noop"}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "executor": "nx:run-commands", "options": {"command": "npx nx watch --projects @befarmer-platform/seller-app --includeDependentProjects -- npx nx build-deps @befarmer-platform/seller-app"}, "configurations": {}, "parallelism": true}, "lint": {"cache": true, "options": {"cwd": "apps/seller-app", "command": "eslint ."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build-storybook": {"options": {"cwd": "apps/seller-app", "command": "storybook build"}, "cache": true, "outputs": ["{projectRoot}/storybook-static", "{options.output-dir}", "{options.outputDir}", "{options.o}"], "inputs": ["production", "^production", {"externalDependencies": ["storybook", "@storybook/test-runner"]}], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "storybook": {"continuous": true, "options": {"cwd": "apps/seller-app", "command": "storybook dev"}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "test-storybook": {"options": {"cwd": "apps/seller-app", "command": "test-storybook"}, "inputs": [{"externalDependencies": ["storybook", "@storybook/test-runner"]}], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "static-storybook": {"dependsOn": ["build-storybook"], "continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build-storybook", "staticFilePath": "apps/seller-app/storybook-static"}, "configurations": {}, "parallelism": true}}, "metadata": {"targetGroups": {}, "js": {"packageName": "@befarmer-platform/seller-app", "isInPackageManagerWorkspaces": true}}, "name": "@befarmer-platform/seller-app", "tags": ["npm:private"], "implicitDependencies": []}}, "shared-ui": {"name": "shared-ui", "type": "lib", "data": {"root": "libs/shared-ui", "projectType": "library", "targets": {"typecheck": {"dependsOn": ["build", "^typecheck"], "options": {"cwd": "libs/shared-ui", "command": "tsc --build --emitDeclarationOnly"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.lib.json", "{projectRoot}/tsconfig.spec.json", "{projectRoot}/src/**/*.js", "{projectRoot}/src/**/*.jsx", "{projectRoot}/src/**/*.ts", "{projectRoot}/src/**/*.tsx", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.test.ts", "{projectRoot}/src/**/*.spec.ts", "{projectRoot}/src/**/*.test.tsx", "{projectRoot}/src/**/*.spec.tsx", "{projectRoot}/src/**/*.test.js", "{projectRoot}/src/**/*.spec.js", "{projectRoot}/src/**/*.test.jsx", "{projectRoot}/src/**/*.spec.jsx", "{projectRoot}/src/**/*.d.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{workspaceRoot}/dist/out-tsc/**/*.d.ts", "{workspaceRoot}/dist/out-tsc/**/*.d.ts.map", "{workspaceRoot}/dist/out-tsc/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build": {"options": {"cwd": "libs/shared-ui", "command": "rollup -c rollup.config.cjs"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["rollup"]}], "outputs": ["{workspaceRoot}/dist/libs/shared-ui"], "metadata": {"technologies": ["typescript", "rollup"], "description": "<PERSON> Rollup", "help": {"command": "npx rollup --help", "example": {"options": {"sourcemap": true, "watch": true}}}}, "syncGenerators": ["@nx/js:typescript-sync"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "build-deps": {"dependsOn": ["^build"], "configurations": {}, "options": {}, "parallelism": true, "executor": "nx:noop"}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "executor": "nx:run-commands", "options": {"command": "npx nx watch --projects shared-ui --includeDependentProjects -- npx nx build-deps shared-ui"}, "configurations": {}, "parallelism": true}, "lint": {"cache": true, "options": {"cwd": "libs/shared-ui", "command": "eslint ."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{projectRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}, "executor": "nx:run-commands", "configurations": {}, "parallelism": true}, "test": {"options": {"cwd": "libs/shared-ui", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}, "command": "jest"}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js", {"externalDependencies": ["jest"]}], "outputs": ["{workspaceRoot}/coverage/libs/shared-ui"], "executor": "nx:run-commands", "configurations": {}, "parallelism": true, "dependsOn": ["^build"]}, "nx-release-publish": {"executor": "@nx/js:release-publish", "dependsOn": ["^nx-release-publish"], "options": {}, "configurations": {}, "parallelism": true}}, "name": "shared-ui", "tags": ["npm:public"], "metadata": {"targetGroups": {}, "js": {"packageName": "@befarmer-platform/shared-ui", "isInPackageManagerWorkspaces": true}}, "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-ui/src", "implicitDependencies": []}}}, "dependencies": {"@befarmer-platform/seller-admin-app": [], "seller-features": [], "admin-features": [], "shared-utils": [], "@befarmer-platform/seller-app": [], "shared-ui": []}}}