{"singleQuote": true, "semi": true, "tabWidth": 2, "useTabs": false, "printWidth": 120, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "overrides": [{"files": "*.json", "options": {"printWidth": 80, "tabWidth": 2}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}, {"files": ["*.yml", "*.yaml"], "options": {"tabWidth": 2, "singleQuote": false}}]}