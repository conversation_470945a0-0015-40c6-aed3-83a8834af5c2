import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Sprout,
  Plus,
  Calendar,
  TrendingUp,
  Droplets,
  Sun,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  BarChart3,
  MapPin,
  AlertCircle,
  CheckCircle,
  Clock,
  Leaf,
  Package,
  Activity,
  Target,
  Zap
} from 'lucide-react';
import { cropService, Crop, CropSearchParams } from '@befarmer-platform/shared-utils';
import CreateCropModal from './CreateCropModal';
import CropViewModal from './CropViewModal';
import EditCropModal from './EditCropModal';

const EnhancedCropsPage: React.FC = () => {
  const navigate = useNavigate();
  const [crops, setCrops] = useState<Crop[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedCrop, setSelectedCrop] = useState<Crop | null>(null);
  const [stats, setStats] = useState({
    totalCrops: 0,
    activeCrops: 0,
    readyToHarvest: 0,
    totalYield: 0
  });

  // Fetch crops data
  useEffect(() => {
    fetchCrops();
    fetchStats();
  }, [currentPage, activeFilter, searchQuery]);

  const fetchCrops = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: CropSearchParams = {
        page: currentPage,
        limit: 12,
        ...(searchQuery && { query: searchQuery }),
        ...(activeFilter !== 'all' && { growthStage: activeFilter as any })
      };

      const response = await cropService.getCrops(params);

      if (response.success) {
        setCrops(response.data);
        setTotalPages(Math.ceil(response.pagination.total / response.pagination.limit));
      } else {
        setError('Failed to fetch crops');
      }
    } catch (err) {
      console.error('Error fetching crops:', err);
      setError('Failed to load crops. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await cropService.getCropStatistics({
        groupBy: 'status',
        period: 'current'
      });
      
      if (response.success && response.data) {
        setStats({
          totalCrops: response.data.totalCrops || 0,
          activeCrops: response.data.activeCrops || 0,
          readyToHarvest: response.data.readyToHarvest || 0,
          totalYield: response.data.totalYield || 0
        });
      }
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'SEEDLING': return 'bg-yellow-100 text-yellow-800';
      case 'VEGETATIVE': return 'bg-green-100 text-green-800';
      case 'FLOWERING': return 'bg-purple-100 text-purple-800';
      case 'FRUITING': return 'bg-orange-100 text-orange-800';
      case 'MATURITY': return 'bg-blue-100 text-blue-800';
      case 'HARVESTED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'HEALTHY': return 'bg-green-100 text-green-800';
      case 'DISEASE': return 'bg-red-100 text-red-800';
      case 'PEST_ATTACK': return 'bg-orange-100 text-orange-800';
      case 'NUTRIENT_DEFICIENCY': return 'bg-yellow-100 text-yellow-800';
      case 'WATER_STRESS': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'HEALTHY': return <CheckCircle className="h-4 w-4" />;
      case 'DISEASE': return <AlertCircle className="h-4 w-4" />;
      case 'PEST_ATTACK': return <AlertCircle className="h-4 w-4" />;
      case 'NUTRIENT_DEFICIENCY': return <AlertCircle className="h-4 w-4" />;
      case 'WATER_STRESS': return <Droplets className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchCrops();
  };

  const handleViewCrop = (crop: Crop) => {
    setSelectedCrop(crop);
    setShowViewModal(true);
  };

  const handleEditCrop = (crop: Crop) => {
    setSelectedCrop(crop);
    setShowEditModal(true);
  };

  const handleDeleteCrop = async (crop: Crop) => {
    if (window.confirm('Are you sure you want to delete this crop?')) {
      try {
        await cropService.deleteCrop(crop.cropId);
        fetchCrops();
      } catch (err) {
        console.error('Error deleting crop:', err);
        alert('Failed to delete crop');
      }
    }
  };

  if (loading && crops.length === 0) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading crops...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button 
            onClick={fetchCrops}
            className="mt-2 text-red-600 hover:text-red-800 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-green-100 p-3 rounded-full">
              <Sprout className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Crop Management</h1>
              <p className="text-gray-600">Monitor and manage your crop cultivation with real-time data</p>
            </div>
          </div>
          <button 
            onClick={() => setShowCreateModal(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Plant New Crop</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Crops</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCrops}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <Activity className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Crops</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeCrops}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Target className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Ready to Harvest</p>
              <p className="text-2xl font-bold text-gray-900">{stats.readyToHarvest}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Zap className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Yield (kg)</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalYield.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search crops by name, variety, or type..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </form>

          <div className="flex gap-2">
            <select
              value={activeFilter}
              onChange={(e) => setActiveFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">All Stages</option>
              <option value="SEEDLING">Seedling</option>
              <option value="VEGETATIVE">Vegetative</option>
              <option value="FLOWERING">Flowering</option>
              <option value="FRUITING">Fruiting</option>
              <option value="MATURITY">Maturity</option>
              <option value="HARVESTED">Harvested</option>
            </select>

            <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <span>More Filters</span>
            </button>
          </div>
        </div>
      </div>

      {/* Crops Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {crops.map((crop) => (
          <div key={crop._id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            {/* Crop Image */}
            <div className="h-48 bg-gradient-to-br from-green-400 to-green-600 rounded-t-lg relative overflow-hidden">
              {crop.images && crop.images.length > 0 ? (
                <img
                  src={crop.images[0]}
                  alt={crop.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <Leaf className="h-16 w-16 text-white opacity-50" />
                </div>
              )}

              {/* Health Status Badge */}
              <div className={`absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getHealthColor(crop.health.status)}`}>
                {getHealthIcon(crop.health.status)}
                <span>{crop.health.status.replace('_', ' ')}</span>
              </div>
            </div>

            {/* Crop Details */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{crop.name}</h3>
                  <p className="text-sm text-gray-600">{crop.variety} • {crop.type}</p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStageColor(crop.growthStage)}`}>
                  {crop.growthStage}
                </span>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-xs text-gray-500">Expected Yield</p>
                  <p className="text-sm font-medium">{crop.yield.expected} {crop.yield.unit}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Planted</p>
                  <p className="text-sm font-medium">{new Date(crop.plantingDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Harvest Date</p>
                  <p className="text-sm font-medium">{new Date(crop.expectedHarvestDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Water Status</p>
                  <p className="text-sm font-medium">{crop.waterAvailability}</p>
                </div>
              </div>

              {/* Tags */}
              {crop.tags && crop.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {crop.tags.slice(0, 3).map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      {tag}
                    </span>
                  ))}
                  {crop.tags.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      +{crop.tags.length - 3} more
                    </span>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button
                  onClick={() => handleViewCrop(crop)}
                  className="flex-1 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-1"
                >
                  <Eye className="h-4 w-4" />
                  <span>View</span>
                </button>
                <button
                  onClick={() => handleEditCrop(crop)}
                  className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <BarChart3 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteCrop(crop)}
                  className="px-3 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {crops.length === 0 && !loading && (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Sprout className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No crops found</h3>
          <p className="text-gray-600 mb-6">
            {searchQuery || activeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by planting your first crop'}
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Plant New Crop
          </button>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </p>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button
            onClick={() => setShowCreateModal(true)}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <Plus className="h-6 w-6 text-green-600 mb-2" />
            <h4 className="font-medium text-gray-900">Plant New Crop</h4>
            <p className="text-sm text-gray-600">Start a new cultivation cycle</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Calendar className="h-6 w-6 text-blue-600 mb-2" />
            <h4 className="font-medium text-gray-900">Schedule Activities</h4>
            <p className="text-sm text-gray-600">Plan irrigation and fertilization</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <BarChart3 className="h-6 w-6 text-purple-600 mb-2" />
            <h4 className="font-medium text-gray-900">View Analytics</h4>
            <p className="text-sm text-gray-600">Analyze crop performance</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Package className="h-6 w-6 text-orange-600 mb-2" />
            <h4 className="font-medium text-gray-900">Marketplace</h4>
            <p className="text-sm text-gray-600">List crops for sale</p>
          </button>
        </div>
      </div>

      {/* Create Crop Modal */}
      <CreateCropModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={fetchCrops}
      />

      {/* View Crop Modal */}
      <CropViewModal
        isOpen={showViewModal}
        onClose={() => {
          setShowViewModal(false);
          setSelectedCrop(null);
        }}
        cropId={selectedCrop?.cropId || null}
        onEdit={(crop) => {
          setShowViewModal(false);
          setSelectedCrop(crop);
          setShowEditModal(true);
        }}
        onDelete={(crop) => {
          setShowViewModal(false);
          handleDeleteCrop(crop);
        }}
      />

      {/* Edit Crop Modal */}
      <EditCropModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedCrop(null);
        }}
        onSuccess={() => {
          setShowEditModal(false);
          setSelectedCrop(null);
          fetchCrops();
        }}
        crop={selectedCrop}
      />
    </div>
  );
};

export default EnhancedCropsPage;
