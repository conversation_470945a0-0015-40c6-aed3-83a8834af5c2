import React, { useState, useEffect } from 'react';
import { X, Calendar, Droplets, Leaf, Save } from 'lucide-react';
import { cropService, Crop, CropUpdateRequest } from '@befarmer-platform/shared-utils';

interface EditCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  crop: Crop | null;
}

const EditCropModal: React.FC<EditCropModalProps> = ({ isOpen, onClose, onSuccess, crop }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<CropUpdateRequest>>({});

  useEffect(() => {
    if (crop) {
      setFormData({
        name: crop.name,
        variety: crop.variety,
        expectedHarvestDate: crop.expectedHarvestDate.split('T')[0], // Convert to date input format
        yield: {
          expected: crop.yield.expected,
          actual: crop.yield.actual,
          unit: crop.yield.unit
        },
        health: {
          status: crop.health.status,
          issues: crop.health.issues,
          lastCheck: crop.health.lastCheck
        },
        resources: {
          water: crop.resources.water,
          fertilizer: crop.resources.fertilizer,
          pesticides: crop.resources.pesticides
        },
        tags: crop.tags,
        certifications: crop.certifications
      });
    }
  }, [crop]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev],
        [field]: value
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!crop) return;

    setLoading(true);
    setError(null);

    try {
      const response = await cropService.updateCrop(crop.cropId, formData as CropUpdateRequest);
      
      if (response.success) {
        onSuccess();
        onClose();
      } else {
        setError(response.error?.message || 'Failed to update crop');
      }
    } catch (err) {
      console.error('Error updating crop:', err);
      setError('Failed to update crop. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !crop) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-100 p-2 rounded-full">
              <Leaf className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Edit Crop</h2>
              <p className="text-sm text-gray-600">Update crop information</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Crop Name *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Variety *
                </label>
                <input
                  type="text"
                  required
                  value={formData.variety || ''}
                  onChange={(e) => handleInputChange('variety', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expected Harvest Date *
                </label>
                <input
                  type="date"
                  required
                  value={formData.expectedHarvestDate || ''}
                  onChange={(e) => handleInputChange('expectedHarvestDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Health Status
                </label>
                <select
                  value={formData.health?.status || ''}
                  onChange={(e) => handleNestedInputChange('health', 'status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="HEALTHY">Healthy</option>
                  <option value="DISEASE">Disease</option>
                  <option value="PEST_ATTACK">Pest Attack</option>
                  <option value="NUTRIENT_DEFICIENCY">Nutrient Deficiency</option>
                  <option value="WATER_STRESS">Water Stress</option>
                </select>
              </div>
            </div>
          </div>

          {/* Yield Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Yield Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expected Yield
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.yield?.expected || ''}
                  onChange={(e) => handleNestedInputChange('yield', 'expected', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Actual Yield
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.yield?.actual || ''}
                  onChange={(e) => handleNestedInputChange('yield', 'actual', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Unit
                </label>
                <select
                  value={formData.yield?.unit || ''}
                  onChange={(e) => handleNestedInputChange('yield', 'unit', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="KG">Kilograms (KG)</option>
                  <option value="TONS">Tons</option>
                  <option value="QUINTALS">Quintals</option>
                </select>
              </div>
            </div>
          </div>

          {/* Resource Usage */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Resource Usage</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Water (Liters)
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.resources?.water || ''}
                  onChange={(e) => handleNestedInputChange('resources', 'water', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fertilizer (kg)
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.resources?.fertilizer || ''}
                  onChange={(e) => handleNestedInputChange('resources', 'fertilizer', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pesticides (kg)
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.resources?.pesticides || ''}
                  onChange={(e) => handleNestedInputChange('resources', 'pesticides', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Tags</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags (comma separated)
              </label>
              <input
                type="text"
                value={formData.tags?.join(', ') || ''}
                onChange={(e) => handleInputChange('tags', e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="organic, fresh, premium"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
              <Save className="h-4 w-4" />
              <span>{loading ? 'Updating...' : 'Update Crop'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditCropModal;
