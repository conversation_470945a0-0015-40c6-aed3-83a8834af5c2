import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2,
  DollarSign,
  TrendingUp,
  MapPin,
  Calendar,
  Star,
  ShoppingCart,
  Truck
} from 'lucide-react';
import { cropService } from '@befarmer-platform/shared-utils';

const CropMarketplacePage: React.FC = () => {
  const [listings, setListings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    fetchListings();
  }, [currentPage, categoryFilter, searchQuery]);

  const fetchListings = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit: 12,
        ...(categoryFilter !== 'all' && { category: categoryFilter }),
        available: true
      };

      const response = await cropService.getMarketplaceListings(params);
      
      if (response.success) {
        setListings(response.data || []);
      } else {
        setError('Failed to fetch marketplace listings');
      }
    } catch (err) {
      console.error('Error fetching listings:', err);
      setError('Failed to load marketplace listings');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateListing = async (cropId: string) => {
    try {
      const listingData = {
        cropId,
        title: 'Fresh Organic Produce',
        description: 'High quality, freshly harvested crops',
        pricing: {
          pricePerKg: 25.00,
          minimumOrder: 10,
          bulkDiscounts: [
            { quantity: 50, discountPercent: 5 },
            { quantity: 100, discountPercent: 10 }
          ]
        },
        availability: {
          quantityAvailable: 1000,
          availableUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        delivery: {
          methods: ['PICKUP', 'LOCAL_DELIVERY'],
          deliveryRadius: 50,
          deliveryCharge: 25.00
        },
        images: []
      };

      const response = await cropService.createMarketplaceListing(listingData);
      
      if (response.success) {
        fetchListings();
        alert('Listing created successfully!');
      } else {
        alert('Failed to create listing');
      }
    } catch (err) {
      console.error('Error creating listing:', err);
      alert('Failed to create listing');
    }
  };

  if (loading && listings.length === 0) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading marketplace...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-orange-100 p-3 rounded-full">
              <Package className="h-8 w-8 text-orange-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Crop Marketplace</h1>
              <p className="text-gray-600">List your crops for sale and manage marketplace listings</p>
            </div>
          </div>
          <button 
            onClick={() => setShowCreateModal(true)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Listing</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <Package className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Listings</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <ShoppingCart className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">48</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Revenue</p>
              <p className="text-2xl font-bold text-gray-900">₹1,25,000</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Star className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-gray-900">4.8</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search marketplace listings..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              <option value="VEGETABLES">Vegetables</option>
              <option value="FRUITS">Fruits</option>
              <option value="GRAINS">Grains</option>
              <option value="PULSES">Pulses</option>
              <option value="SPICES">Spices</option>
            </select>
            
            <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <span>More Filters</span>
            </button>
          </div>
        </div>
      </div>

      {/* Marketplace Listings Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Mock listings since we don't have real data */}
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <div key={item} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            {/* Product Image */}
            <div className="h-48 bg-gradient-to-br from-orange-400 to-red-500 rounded-t-lg relative overflow-hidden">
              <div className="flex items-center justify-center h-full">
                <Package className="h-16 w-16 text-white opacity-50" />
              </div>
              
              {/* Status Badge */}
              <div className="absolute top-3 right-3 px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                Available
              </div>
            </div>

            {/* Product Details */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Fresh Organic Tomatoes</h3>
                  <p className="text-sm text-gray-600">Cherry Variety • Premium Quality</p>
                </div>
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600 ml-1">4.8</span>
                </div>
              </div>

              {/* Pricing */}
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900">₹25/kg</span>
                  <span className="text-sm text-gray-500">Min: 10kg</span>
                </div>
                <p className="text-sm text-green-600">Bulk discounts available</p>
              </div>

              {/* Key Info */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <p className="text-gray-500">Available</p>
                  <p className="font-medium">500 kg</p>
                </div>
                <div>
                  <p className="text-gray-500">Delivery</p>
                  <p className="font-medium">50km radius</p>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-center text-sm text-gray-600 mb-4">
                <MapPin className="h-4 w-4 mr-1" />
                <span>Bangalore, Karnataka</span>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button className="flex-1 bg-orange-600 text-white px-3 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center space-x-1">
                  <Eye className="h-4 w-4" />
                  <span>View</span>
                </button>
                <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <Edit className="h-4 w-4" />
                </button>
                <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <TrendingUp className="h-4 w-4" />
                </button>
                <button className="px-3 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {listings.length === 0 && !loading && (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Package className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No marketplace listings</h3>
          <p className="text-gray-600 mb-6">Start selling your crops by creating your first listing</p>
          <button 
            onClick={() => setShowCreateModal(true)}
            className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
          >
            Create First Listing
          </button>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Marketplace Tools</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button 
            onClick={() => setShowCreateModal(true)}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <Plus className="h-6 w-6 text-orange-600 mb-2" />
            <h4 className="font-medium text-gray-900">Create Listing</h4>
            <p className="text-sm text-gray-600">List new crops for sale</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <TrendingUp className="h-6 w-6 text-blue-600 mb-2" />
            <h4 className="font-medium text-gray-900">Price Analytics</h4>
            <p className="text-sm text-gray-600">Optimize your pricing</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Truck className="h-6 w-6 text-green-600 mb-2" />
            <h4 className="font-medium text-gray-900">Delivery Settings</h4>
            <p className="text-sm text-gray-600">Manage delivery options</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Star className="h-6 w-6 text-purple-600 mb-2" />
            <h4 className="font-medium text-gray-900">Reviews</h4>
            <p className="text-sm text-gray-600">View customer feedback</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CropMarketplacePage;
