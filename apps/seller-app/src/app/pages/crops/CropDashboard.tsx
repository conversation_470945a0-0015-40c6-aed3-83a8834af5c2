import React, { useState, useEffect } from 'react';
import { 
  Sprout, 
  Plus, 
  Search, 
  Filter,
  BarChart3,
  Package,
  TrendingUp,
  Calendar,
  Droplets,
  Leaf,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cropService, Crop } from '@befarmer-platform/shared-utils';

const CropDashboard: React.FC = () => {
  const [crops, setCrops] = useState<Crop[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [stats, setStats] = useState({
    totalCrops: 0,
    healthyCrops: 0,
    readyToHarvest: 0,
    totalExpectedYield: 0
  });

  useEffect(() => {
    fetchCrops();
  }, []);

  const fetchCrops = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await cropService.getCrops({
        page: 1,
        limit: 50
      });
      
      if (response.success) {
        setCrops(response.data);
        calculateStats(response.data);
      } else {
        setError('Failed to fetch crops');
      }
    } catch (err) {
      console.error('Error fetching crops:', err);
      setError('Failed to load crops');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (cropsData: Crop[]) => {
    const stats = {
      totalCrops: cropsData.length,
      healthyCrops: cropsData.filter(crop => crop.health.status === 'HEALTHY').length,
      readyToHarvest: cropsData.filter(crop => crop.growthStage === 'MATURITY').length,
      totalExpectedYield: cropsData.reduce((sum, crop) => sum + crop.yield.expected, 0)
    };
    setStats(stats);
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'PLANTING':
        return 'bg-yellow-100 text-yellow-800';
      case 'SEEDLING':
        return 'bg-green-100 text-green-800';
      case 'VEGETATIVE':
        return 'bg-green-100 text-green-800';
      case 'FLOWERING':
        return 'bg-purple-100 text-purple-800';
      case 'FRUITING':
        return 'bg-orange-100 text-orange-800';
      case 'MATURITY':
        return 'bg-blue-100 text-blue-800';
      case 'HARVESTED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredCrops = crops.filter(crop =>
    crop.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    crop.variety.toLowerCase().includes(searchQuery.toLowerCase()) ||
    crop.type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Crop Dashboard</h1>
            <p className="text-gray-600">Overview of your crop cultivation</p>
          </div>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Add Crop</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Crops</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCrops}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Healthy Crops</p>
              <p className="text-2xl font-bold text-gray-900">{stats.healthyCrops}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Calendar className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Ready to Harvest</p>
              <p className="text-2xl font-bold text-gray-900">{stats.readyToHarvest}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Expected Yield</p>
              <p className="text-2xl font-bold text-gray-900">{(stats.totalExpectedYield / 1000).toFixed(1)}T</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search crops by name, variety, or type..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button 
            onClick={fetchCrops}
            className="mt-2 text-red-600 hover:text-red-800 underline"
          >
            Try again
          </button>
        </div>
      )}

      {/* Crops Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCrops.map((crop) => (
          <div key={crop._id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            {/* Crop Header */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-2 rounded-lg">
                    <Leaf className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{crop.name}</h3>
                    <p className="text-sm text-gray-600">{crop.variety} • {crop.type}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStageColor(crop.growthStage)}`}>
                  {crop.growthStage}
                </span>
              </div>

              {/* Crop Details */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Health Status:</span>
                  <div className="flex items-center space-x-1">
                    {getHealthIcon(crop.health.status)}
                    <span className="font-medium">{crop.health.status}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Expected Yield:</span>
                  <span className="font-medium">{crop.yield.expected} {crop.yield.unit}</span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Planted:</span>
                  <span className="font-medium">{new Date(crop.plantingDate).toLocaleDateString()}</span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Harvest Date:</span>
                  <span className="font-medium">{new Date(crop.expectedHarvestDate).toLocaleDateString()}</span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Farming Method:</span>
                  <span className="font-medium">{crop.metadata.farmingMethod}</span>
                </div>
              </div>

              {/* Tags */}
              {crop.tags && crop.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {crop.tags.slice(0, 3).map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      {tag}
                    </span>
                  ))}
                  {crop.tags.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      +{crop.tags.length - 3}
                    </span>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button className="flex-1 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                  View Details
                </button>
                <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <BarChart3 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredCrops.length === 0 && !loading && (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Sprout className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery ? 'No crops found' : 'No crops yet'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchQuery 
              ? 'Try adjusting your search terms' 
              : 'Start by adding your first crop to track its progress'}
          </p>
          {!searchQuery && (
            <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
              Add Your First Crop
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default CropDashboard;
