import React, { useState } from 'react';
import { X, Calendar, Droplets, Leaf, MapPin } from 'lucide-react';
import { cropService, CropCreateRequest } from '@befarmer-platform/shared-utils';

interface CreateCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateCropModal: React.FC<CreateCropModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<CropCreateRequest>>({
    name: '',
    type: 'VEGETABLE',
    variety: '',
    plantingDate: '',
    expectedHarvestDate: '',
    numberOfPlots: 0,
    farmId: '',
    sellerId: 'seller_123', // This should come from auth context
    soilConditions: {
      type: 'LOAMY',
      ph: 6.5,
      nutrients: ['NITROGEN', 'PHOSPHORUS', 'POTASSIUM']
    },
    waterAvailability: 'ADEQUATE',
    metadata: {
      cropCategory: 'VEGETABLES',
      farmingMethod: 'ORGANIC',
      irrigationMethod: 'DRIP',
      harvestSeason: 'SUMMER',
      waterSource: 'BOREWELL',
      seedType: 'HYBRID'
    },
    cultivation: {
      irrigationNeeds: 'Regular watering every 2-3 days',
      fertilizerRequirements: 'Organic compost and vermicompost',
      pestControl: 'Neem oil and organic pesticides',
      climateConditions: 'Warm and humid climate preferred'
    },
    yield: {
      expected: 0,
      actual: 0,
      unit: 'KG'
    },
    resources: {
      water: 1000,
      fertilizer: 50,
      pesticides: 10
    },
    health: {
      status: 'HEALTHY',
      issues: [],
      lastCheck: new Date().toISOString()
    },
    images: ['https://example.com/tomato1.jpg'],
    tags: ['organic', 'cherry', 'tomato', 'fresh'],
    certifications: ['ORGANIC', 'PESTICIDE_FREE']
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev],
        [field]: value
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await cropService.createCrop(formData as CropCreateRequest);
      
      if (response.success) {
        onSuccess();
        onClose();
        // Reset form
        setFormData({
          name: '',
          type: 'VEGETABLE',
          variety: '',
          plantingDate: '',
          expectedHarvestDate: '',
          numberOfPlots: 0,
          farmId: '',
          sellerId: 'seller_123'
        });
      } else {
        setError(response.error?.message || 'Failed to create crop');
      }
    } catch (err) {
      console.error('Error creating crop:', err);
      setError('Failed to create crop. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <div className="bg-green-100 p-2 rounded-full">
              <Leaf className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Plant New Crop</h2>
              <p className="text-sm text-gray-600">Add a new crop to your farm</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Crop Name *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="e.g., Organic Cherry Tomatoes"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Crop Type *
                </label>
                <select
                  required
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="VEGETABLE">Vegetable</option>
                  <option value="FRUIT">Fruit</option>
                  <option value="GRAIN">Grain</option>
                  <option value="PULSE">Pulse</option>
                  <option value="SPICE">Spice</option>
                  <option value="OILSEED">Oilseed</option>
                  <option value="FIBER">Fiber</option>
                  <option value="FODDER">Fodder</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Variety *
                </label>
                <input
                  type="text"
                  required
                  value={formData.variety}
                  onChange={(e) => handleInputChange('variety', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="e.g., Cherry, Roma, Beefsteak"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Farm ID *
                </label>
                <input
                  type="text"
                  required
                  value={formData.farmId}
                  onChange={(e) => handleInputChange('farmId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter farm ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Number of Plots *
                </label>
                <input
                  type="number"
                  required
                  value={formData.numberOfPlots || 0}
                  onChange={(e) => handleInputChange('numberOfPlots', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter Number of plots"
                />
              </div>
            </div>
          </div>

          {/* Planting Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-green-600" />
              Planting Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Planting Date *
                </label>
                <input
                  type="date"
                  required
                  value={formData.plantingDate}
                  onChange={(e) => handleInputChange('plantingDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expected Harvest Date *
                </label>
                <input
                  type="date"
                  required
                  value={formData.expectedHarvestDate}
                  onChange={(e) => handleInputChange('expectedHarvestDate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Soil Conditions */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Soil & Environment</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Soil Type *
                </label>
                <select
                  required
                  value={formData.soilConditions?.type}
                  onChange={(e) => handleNestedInputChange('soilConditions', 'type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="LOAMY">Loamy</option>
                  <option value="CLAY">Clay</option>
                  <option value="SANDY">Sandy</option>
                  <option value="SILT">Silt</option>
                  <option value="PEAT">Peat</option>
                  <option value="CHALK">Chalk</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Soil pH *
                </label>
                <input
                  type="number"
                  required
                  min="0"
                  max="14"
                  step="0.1"
                  value={formData.soilConditions?.ph || ''}
                  onChange={(e) => handleNestedInputChange('soilConditions', 'ph', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="6.5"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Water Availability *
                </label>
                <select
                  required
                  value={formData.waterAvailability}
                  onChange={(e) => handleInputChange('waterAvailability', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="ADEQUATE">Adequate</option>
                  <option value="LIMITED">Limited</option>
                  <option value="ABUNDANT">Abundant</option>
                  <option value="SCARCE">Scarce</option>
                </select>
              </div>
            </div>
          </div>

          {/* Farming Method & Practices */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Farming Practices</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Farming Method *
                </label>
                <select
                  required
                  value={formData.metadata?.farmingMethod}
                  onChange={(e) => handleNestedInputChange('metadata', 'farmingMethod', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="ORGANIC">Organic</option>
                  <option value="CONVENTIONAL">Conventional</option>
                  <option value="HYDROPONIC">Hydroponic</option>
                  <option value="GREENHOUSE">Greenhouse</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Irrigation Method *
                </label>
                <select
                  required
                  value={formData.metadata?.irrigationMethod}
                  onChange={(e) => handleNestedInputChange('metadata', 'irrigationMethod', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="DRIP">Drip Irrigation</option>
                  <option value="SPRINKLER">Sprinkler</option>
                  <option value="FLOOD">Flood Irrigation</option>
                  <option value="RAIN_FED">Rain Fed</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Harvest Season *
                </label>
                <select
                  required
                  value={formData.metadata?.harvestSeason}
                  onChange={(e) => handleNestedInputChange('metadata', 'harvestSeason', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="SUMMER">Summer</option>
                  <option value="WINTER">Winter</option>
                  <option value="MONSOON">Monsoon</option>
                  <option value="SPRING">Spring</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Seed Type *
                </label>
                <select
                  required
                  value={formData.metadata?.seedType}
                  onChange={(e) => handleNestedInputChange('metadata', 'seedType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="HYBRID">Hybrid</option>
                  <option value="TRADITIONAL">Traditional</option>
                  <option value="GMO">GMO</option>
                </select>
              </div>
            </div>
          </div>

          {/* Expected Yield */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Expected Yield & Resources</h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expected Yield *
                </label>
                <input
                  type="number"
                  required
                  min="0"
                  value={formData.yield?.expected || ''}
                  onChange={(e) => handleNestedInputChange('yield', 'expected', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="5000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Unit *
                </label>
                <select
                  required
                  value={formData.yield?.unit}
                  onChange={(e) => handleNestedInputChange('yield', 'unit', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="KG">Kilograms (KG)</option>
                  <option value="TONS">Tons</option>
                  <option value="QUINTALS">Quintals</option>
                </select>
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags (comma separated)
              </label>
              <input
                type="text"
                value={formData.tags?.join(', ') || ''}
                onChange={(e) => handleInputChange('tags', e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="organic, cherry, tomato, fresh"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
              <span>{loading ? 'Creating...' : 'Plant Crop'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCropModal;
