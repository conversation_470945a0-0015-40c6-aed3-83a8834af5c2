import React, { useState, useEffect } from 'react';
import { 
  X,
  Calendar,
  MapPin,
  Droplets,
  Thermometer,
  Leaf,
  Target,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Camera,
  Tag,
  Award,
  Activity,
  BarChart3,
  Package,
  Edit,
  Trash2
} from 'lucide-react';
import { cropService, Crop } from '@befarmer-platform/shared-utils';

interface CropViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  cropId: string | null;
  onEdit?: (crop: Crop) => void;
  onDelete?: (crop: Crop) => void;
}

const CropViewModal: React.FC<CropViewModalProps> = ({ 
  isOpen, 
  onClose, 
  cropId, 
  onEdit, 
  onDelete 
}) => {
  const [crop, setCrop] = useState<Crop | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (isOpen && cropId) {
      fetchCrop(cropId);
    }
  }, [isOpen, cropId]);

  const fetchCrop = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await cropService.getCropById(id);
      
      if (response.success) {
        const cropData = response.crop || response.data;
        if (cropData) {
          setCrop(cropData);
        } else {
          setError('Crop data not found');
        }
      } else {
        setError('Crop not found');
      }
    } catch (err) {
      console.error('Error fetching crop:', err);
      setError('Failed to load crop details');
    } finally {
      setLoading(false);
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'HEALTHY': return 'text-green-600 bg-green-100';
      case 'DISEASE': return 'text-red-600 bg-red-100';
      case 'PEST_ATTACK': return 'text-orange-600 bg-orange-100';
      case 'NUTRIENT_DEFICIENCY': return 'text-yellow-600 bg-yellow-100';
      case 'WATER_STRESS': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'PLANTING': return 'bg-yellow-100 text-yellow-800';
      case 'SEEDLING': return 'bg-green-100 text-green-800';
      case 'VEGETATIVE': return 'bg-green-100 text-green-800';
      case 'FLOWERING': return 'bg-purple-100 text-purple-800';
      case 'FRUITING': return 'bg-orange-100 text-orange-800';
      case 'MATURITY': return 'bg-blue-100 text-blue-800';
      case 'HARVESTED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY': return <CheckCircle className="h-5 w-5" />;
      default: return <AlertCircle className="h-5 w-5" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-4">
            <div className="bg-green-100 p-3 rounded-full">
              <Leaf className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {crop ? crop.name : 'Loading...'}
              </h2>
              {crop && (
                <div className="flex items-center space-x-3">
                  <p className="text-gray-600">{crop.variety} • {crop.type}</p>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStageColor(crop.growthStage)}`}>
                    {crop.growthStage}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {crop && onEdit && (
              <button 
                onClick={() => onEdit(crop)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
              >
                <Edit className="h-4 w-4" />
                <span>Edit</span>
              </button>
            )}
            {crop && onDelete && (
              <button 
                onClick={() => onDelete(crop)}
                className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors flex items-center space-x-2"
              >
                <Trash2 className="h-4 w-4" />
                <span>Delete</span>
              </button>
            )}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading && (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
              <span className="ml-3 text-gray-600">Loading crop details...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                <span className="text-red-800">{error}</span>
              </div>
            </div>
          )}

          {crop && (
            <>
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div className="bg-white p-4 rounded-lg border">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-full ${getHealthColor(crop.health.status)}`}>
                      {getHealthIcon(crop.health.status)}
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Health Status</p>
                      <p className="text-lg font-bold text-gray-900">{crop.health.status}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 rounded-lg border">
                  <div className="flex items-center">
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Target className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Expected Yield</p>
                      <p className="text-lg font-bold text-gray-900">{crop.yield.expected} {crop.yield.unit}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 rounded-lg border">
                  <div className="flex items-center">
                    <div className="bg-green-100 p-3 rounded-full">
                      <Calendar className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Days to Harvest</p>
                      <p className="text-lg font-bold text-gray-900">
                        {Math.max(0, Math.ceil((new Date(crop.expectedHarvestDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 rounded-lg border">
                  <div className="flex items-center">
                    <div className="bg-purple-100 p-3 rounded-full">
                      <Droplets className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Water Status</p>
                      <p className="text-lg font-bold text-gray-900">{crop.waterAvailability}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Basic Information */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Crop ID</label>
                      <p className="text-gray-900">{crop.cropId}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Farm ID</label>
                      <p className="text-gray-900">{crop.farmId}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Plot ID</label>
                      <p className="text-gray-900">{crop.plotId}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Planting Date</label>
                      <p className="text-gray-900">{new Date(crop.plantingDate).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Expected Harvest</label>
                      <p className="text-gray-900">{new Date(crop.expectedHarvestDate).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Farming Method</label>
                      <p className="text-gray-900">{crop.metadata.farmingMethod}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Irrigation Method</label>
                      <p className="text-gray-900">{crop.metadata.irrigationMethod}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Harvest Season</label>
                      <p className="text-gray-900">{crop.metadata.harvestSeason}</p>
                    </div>
                  </div>
                </div>

                {/* Tags and Certifications */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  <div>
                    <h4 className="text-md font-semibold text-gray-900 mb-2 flex items-center">
                      <Tag className="h-4 w-4 mr-2" />
                      Tags
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {crop.tags.map((tag, index) => (
                        <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-md font-semibold text-gray-900 mb-2 flex items-center">
                      <Award className="h-4 w-4 mr-2" />
                      Certifications
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {crop.certifications.map((cert, index) => (
                        <span key={index} className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                          {cert}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CropViewModal;
