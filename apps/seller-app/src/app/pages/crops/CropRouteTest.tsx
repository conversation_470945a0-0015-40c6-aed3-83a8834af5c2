import React from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const CropRouteTest: React.FC = () => {
  const { cropId } = useParams<{ cropId: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-4 mb-6">
          <button
            onClick={() => navigate('/crops')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Crop Route Test</h1>
        </div>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Route Information</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p><strong>Current Path:</strong> {location.pathname}</p>
              <p><strong>Crop ID from params:</strong> {cropId || 'Not found'}</p>
              <p><strong>Full Location:</strong> {JSON.stringify(location, null, 2)}</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Test Navigation</h3>
            <div className="flex space-x-4">
              <button
                onClick={() => navigate('/crops')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Go to Crops List
              </button>
              <button
                onClick={() => navigate('/crops/test-crop-id-123')}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Test Crop View (test-crop-id-123)
              </button>
            </div>
          </div>

          {cropId && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Crop ID Found</h3>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-800">
                  ✅ Successfully received crop ID: <strong>{cropId}</strong>
                </p>
                <p className="text-green-600 text-sm mt-1">
                  The routing is working correctly! This means the CropViewPage should be able to receive the crop ID.
                </p>
              </div>
            </div>
          )}

          {!cropId && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Crop ID</h3>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800">
                  ⚠️ No crop ID found in the URL parameters.
                </p>
                <p className="text-yellow-600 text-sm mt-1">
                  This page should be accessed via /crops/:cropId
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CropRouteTest;
