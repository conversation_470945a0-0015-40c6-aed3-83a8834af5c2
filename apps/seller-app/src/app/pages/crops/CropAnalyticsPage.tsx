import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Calendar,
  Droplets,
  Thermometer,
  Leaf,
  ArrowLeft,
  Download,
  RefreshCw
} from 'lucide-react';
import { cropService } from '@befarmer-platform/shared-utils';

interface CropAnalyticsPageProps {
  cropId?: string;
  sellerId?: string;
}

const CropAnalyticsPage: React.FC<CropAnalyticsPageProps> = ({ 
  cropId, 
  sellerId = 'seller_123' 
}) => {
  const [analytics, setAnalytics] = useState<any>(null);
  const [marketInsights, setMarketInsights] = useState<any>(null);
  const [priceTrends, setPriceTrends] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('6months');

  useEffect(() => {
    fetchAnalytics();
  }, [cropId, sellerId, selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch different types of analytics based on what's available
      const promises = [];

      if (cropId) {
        // Individual crop analytics
        promises.push(cropService.getCropAnalyticsById(cropId));
      } else {
        // Seller-wide analytics
        promises.push(cropService.getCropAnalytics(sellerId, {
          fromDate: getDateFromPeriod(selectedPeriod),
          toDate: new Date().toISOString()
        }));
      }

      // Market insights
      promises.push(cropService.getMarketInsights({
        category: 'VEGETABLES',
        region: 'south',
        period: 'monthly'
      }));

      // Price trends
      promises.push(cropService.getPriceTrends({
        crop: 'tomato',
        period: selectedPeriod as any,
        region: 'karnataka'
      }));

      const [analyticsRes, marketRes, priceRes] = await Promise.all(promises);

      if (analyticsRes.success) {
        setAnalytics(analyticsRes.data);
      }

      if (marketRes.success) {
        setMarketInsights(marketRes.data);
      }

      if (priceRes.success) {
        setPriceTrends(priceRes.data);
      }

    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const getDateFromPeriod = (period: string): string => {
    const now = new Date();
    switch (period) {
      case '1month':
        return new Date(now.setMonth(now.getMonth() - 1)).toISOString();
      case '3months':
        return new Date(now.setMonth(now.getMonth() - 3)).toISOString();
      case '6months':
        return new Date(now.setMonth(now.getMonth() - 6)).toISOString();
      case '1year':
        return new Date(now.setFullYear(now.getFullYear() - 1)).toISOString();
      default:
        return new Date(now.setMonth(now.getMonth() - 6)).toISOString();
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
          <button 
            onClick={fetchAnalytics}
            className="mt-2 text-red-600 hover:text-red-800 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <div className="bg-purple-100 p-3 rounded-full">
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Crop Analytics</h1>
              <p className="text-gray-600">
                {cropId ? 'Individual crop performance' : 'Overall crop performance and insights'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="1month">Last Month</option>
              <option value="3months">Last 3 Months</option>
              <option value="6months">Last 6 Months</option>
              <option value="1year">Last Year</option>
            </select>
            
            <button 
              onClick={fetchAnalytics}
              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-5 w-5 text-gray-600" />
            </button>
            
            <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">₹2,45,000</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+12.5%</span>
              </div>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Yield</p>
              <p className="text-2xl font-bold text-gray-900">15,250 kg</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+8.3%</span>
              </div>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <Target className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Price/kg</p>
              <p className="text-2xl font-bold text-gray-900">₹16.07</p>
              <div className="flex items-center mt-1">
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-sm text-red-600">-2.1%</span>
              </div>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">94.2%</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+1.8%</span>
              </div>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Target className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Yield Trends */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Yield Trends</h3>
            <div className="flex items-center space-x-2">
              <Leaf className="h-5 w-5 text-green-600" />
              <span className="text-sm text-gray-600">Monthly Average</span>
            </div>
          </div>
          
          {/* Placeholder for chart */}
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Yield trend chart will be displayed here</p>
            </div>
          </div>
        </div>

        {/* Price Trends */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Price Trends</h3>
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <span className="text-sm text-gray-600">Market Price</span>
            </div>
          </div>
          
          {/* Placeholder for chart */}
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Price trend chart will be displayed here</p>
            </div>
          </div>
        </div>
      </div>

      {/* Market Insights */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Market Insights</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-3">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <h4 className="font-medium text-gray-900 mb-1">Demand Forecast</h4>
            <p className="text-2xl font-bold text-blue-600 mb-1">High</p>
            <p className="text-sm text-gray-600">Expected to increase by 15% next month</p>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-3">
              <Target className="h-6 w-6 text-green-600" />
            </div>
            <h4 className="font-medium text-gray-900 mb-1">Best Selling Season</h4>
            <p className="text-2xl font-bold text-green-600 mb-1">Summer</p>
            <p className="text-sm text-gray-600">Peak demand in May-July</p>
          </div>

          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="bg-yellow-100 p-3 rounded-full w-fit mx-auto mb-3">
              <DollarSign className="h-6 w-6 text-yellow-600" />
            </div>
            <h4 className="font-medium text-gray-900 mb-1">Price Recommendation</h4>
            <p className="text-2xl font-bold text-yellow-600 mb-1">₹18-20/kg</p>
            <p className="text-sm text-gray-600">Optimal pricing range</p>
          </div>
        </div>
      </div>

      {/* Performance Breakdown */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Breakdown</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Leaf className="h-5 w-5 text-green-600" />
              <span className="font-medium text-gray-900">Tomatoes</span>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-right">
                <p className="text-sm text-gray-600">Yield</p>
                <p className="font-semibold">8,500 kg</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Revenue</p>
                <p className="font-semibold">₹1,36,000</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="font-semibold text-green-600">96%</p>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Leaf className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-gray-900">Potatoes</span>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-right">
                <p className="text-sm text-gray-600">Yield</p>
                <p className="font-semibold">4,200 kg</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Revenue</p>
                <p className="font-semibold">₹63,000</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="font-semibold text-yellow-600">88%</p>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Leaf className="h-5 w-5 text-yellow-600" />
              <span className="font-medium text-gray-900">Wheat</span>
            </div>
            <div className="flex items-center space-x-6">
              <div className="text-right">
                <p className="text-sm text-gray-600">Yield</p>
                <p className="font-semibold">2,550 kg</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Revenue</p>
                <p className="font-semibold">₹46,000</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="font-semibold text-green-600">98%</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CropAnalyticsPage;
