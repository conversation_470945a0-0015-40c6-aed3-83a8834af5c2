import React, { useState, useEffect } from 'react';
import { cropService, Crop } from '@befarmer-platform/shared-utils';

const CropTestPage: React.FC = () => {
  const [crops, setCrops] = useState<Crop[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testGetCrops = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await cropService.getCrops({
        page: 1,
        limit: 10
      });
      
      console.log('API Response:', response);
      
      if (response.success) {
        setCrops(response.data);
        console.log('Crops loaded:', response.data);
      } else {
        setError('Failed to fetch crops');
      }
    } catch (err) {
      console.error('Error:', err);
      setError(`Error: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const testCreateCrop = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const newCrop = {
        numberOfPlots: 20,
        farmId: "farm_456",
        sellerId: "seller_123",
        name: "Organic Cherry Tomatoes",
        type: "VEGETABLE" as const,
        variety: "Cherry",
        plantingDate: "2024-01-15T00:00:00.000Z",
        expectedHarvestDate: "2024-04-15T00:00:00.000Z",
        soilConditions: {
          type: "LOAMY" as const,
          ph: 6.5,
          nutrients: ["NITROGEN", "PHOSPHORUS", "POTASSIUM"] as const
        },
        waterAvailability: "ADEQUATE" as const,
        metadata: {
          cropCategory: "VEGETABLES" as const,
          farmingMethod: "ORGANIC" as const,
          irrigationMethod: "DRIP" as const,
          harvestSeason: "SUMMER" as const,
          waterSource: "BOREWELL" as const,
          seedType: "HYBRID" as const
        },
        cultivation: {
          irrigationNeeds: "Regular watering every 2-3 days",
          fertilizerRequirements: "Organic compost and vermicompost",
          pestControl: "Neem oil and organic pesticides",
          climateConditions: "Warm and humid climate preferred"
        },
        yield: {
          expected: 5000,
          actual: 0,
          unit: "KG" as const
        },
        resources: {
          water: 1000,
          fertilizer: 50,
          pesticides: 10
        },
        health: {
          status: "HEALTHY" as const,
          issues: [],
          lastCheck: "2024-01-15T00:00:00.000Z"
        },
        images: ["https://example.com/tomato1.jpg"],
        tags: ["organic", "cherry", "tomato", "fresh"],
        certifications: ["ORGANIC", "PESTICIDE_FREE"] as const
      };
      
      const response = await cropService.createCrop(newCrop);
      console.log('Create Response:', response);
      
      if (response.success) {
        alert('Crop created successfully!');
        testGetCrops(); // Refresh the list
      } else {
        setError('Failed to create crop');
      }
    } catch (err) {
      console.error('Error creating crop:', err);
      setError(`Error creating crop: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Crop API Test Page</h1>
        
        <div className="flex space-x-4 mb-6">
          <button
            onClick={testGetCrops}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Test Get Crops'}
          </button>
          
          <button
            onClick={testCreateCrop}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Test Create Crop'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Crops ({crops.length})</h2>
          
          {crops.map((crop) => (
            <div key={crop._id} className="border border-gray-200 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-gray-900">{crop.name}</h3>
                  <p className="text-sm text-gray-600">{crop.variety} • {crop.type}</p>
                  <p className="text-sm text-gray-600">Growth Stage: {crop.growthStage}</p>
                  <p className="text-sm text-gray-600">Health: {crop.health.status}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Crop ID: {crop.cropId}</p>
                  <p className="text-sm text-gray-600">Farm ID: {crop.farmId}</p>
                  <p className="text-sm text-gray-600">Number of Plots: {crop.numberOfPlots}</p>
                  <p className="text-sm text-gray-600">Expected Yield: {crop.yield.expected} {crop.yield.unit}</p>
                </div>
              </div>
              
              {crop.tags && crop.tags.length > 0 && (
                <div className="mt-2">
                  <div className="flex flex-wrap gap-1">
                    {crop.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
          
          {crops.length === 0 && !loading && (
            <p className="text-gray-500 text-center py-8">No crops found. Try creating one!</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default CropTestPage;
