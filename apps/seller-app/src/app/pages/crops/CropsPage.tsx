import React, { useState } from 'react';
import { Sprout, Plus, Calendar, TrendingUp, Droplets, Sun, BarChart3, Package } from 'lucide-react';
import EnhancedCropsPage from './EnhancedCropsPage';
import CropAnalyticsPage from './CropAnalyticsPage';
import CropMarketplacePage from './CropMarketplacePage';

const CropsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('crops');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'crops':
        return <EnhancedCropsPage />;
      case 'analytics':
        return <CropAnalyticsPage />;
      case 'marketplace':
        return <CropMarketplacePage />;
      default:
        return <EnhancedCropsPage />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Navigation Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('crops')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'crops'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Sprout className="h-5 w-5" />
                <span>Crop Management</span>
              </div>
            </button>

            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'analytics'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Analytics</span>
              </div>
            </button>

            <button
              onClick={() => setActiveTab('marketplace')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'marketplace'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Marketplace</span>
              </div>
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {renderTabContent()}
    </div>
  );
};

export default CropsPage;
