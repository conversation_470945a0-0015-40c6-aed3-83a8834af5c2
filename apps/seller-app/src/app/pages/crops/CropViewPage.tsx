import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft,
  Edit,
  Trash2,
  Calendar,
  MapPin,
  Droplets,
  Thermometer,
  Leaf,
  Target,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Camera,
  Tag,
  Award,
  Activity,
  BarChart3,
  Package,
  DollarSign
} from 'lucide-react';
import { cropService, Crop } from '@befarmer-platform/shared-utils';
import EditCropModal from './EditCropModal';

const CropViewPage: React.FC = () => {
  const { cropId } = useParams<{ cropId: string }>();
  const navigate = useNavigate();
  const [crop, setCrop] = useState<Crop | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    console.log('CropViewPage - cropId from params:', cropId);
    if (cropId) {
      fetchCrop(cropId);
    }
  }, [cropId]);

  const fetchCrop = async (id: string) => {
    try {
      console.log('Fetching crop with ID:', id);
      setLoading(true);
      setError(null);

      const response = await cropService.getCropById(id);
      console.log('Crop fetch response:', response);

      if (response.success) {
        // Handle different possible response structures
        const cropData = response.crop || response.data;
        if (cropData) {
          setCrop(cropData);
        } else {
          setError('Crop data not found in response');
        }
      } else {
        setError('Crop not found');
      }
    } catch (err) {
      console.error('Error fetching crop:', err);
      setError('Failed to load crop details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!crop || !window.confirm('Are you sure you want to delete this crop?')) {
      return;
    }

    try {
      await cropService.deleteCrop(crop.cropId);
      navigate('/crops');
    } catch (err) {
      console.error('Error deleting crop:', err);
      alert('Failed to delete crop');
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'HEALTHY': return 'text-green-600 bg-green-100';
      case 'DISEASE': return 'text-red-600 bg-red-100';
      case 'PEST_ATTACK': return 'text-orange-600 bg-orange-100';
      case 'NUTRIENT_DEFICIENCY': return 'text-yellow-600 bg-yellow-100';
      case 'WATER_STRESS': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'PLANTING': return 'bg-yellow-100 text-yellow-800';
      case 'SEEDLING': return 'bg-green-100 text-green-800';
      case 'VEGETATIVE': return 'bg-green-100 text-green-800';
      case 'FLOWERING': return 'bg-purple-100 text-purple-800';
      case 'FRUITING': return 'bg-orange-100 text-orange-800';
      case 'MATURITY': return 'bg-blue-100 text-blue-800';
      case 'HARVESTED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY': return <CheckCircle className="h-5 w-5" />;
      default: return <AlertCircle className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading crop details...</span>
        </div>
      </div>
    );
  }

  if (error || !crop) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="h-6 w-6 text-red-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-red-800">Error Loading Crop</h3>
              <p className="text-red-600">{error}</p>
            </div>
          </div>
          <div className="mt-4">
            <button
              onClick={() => navigate('/crops')}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Back to Crops
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/crops')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <div className="bg-green-100 p-3 rounded-full">
              <Leaf className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{crop.name}</h1>
              <p className="text-gray-600">{crop.variety} • {crop.type}</p>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStageColor(crop.growthStage)}`}>
              {crop.growthStage}
            </span>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowEditModal(true)}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
            >
              <Edit className="h-4 w-4" />
              <span>Edit</span>
            </button>
            <button
              onClick={handleDelete}
              className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors flex items-center space-x-2"
            >
              <Trash2 className="h-4 w-4" />
              <span>Delete</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className={`p-3 rounded-full ${getHealthColor(crop.health.status)}`}>
              {getHealthIcon(crop.health.status)}
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Health Status</p>
              <p className="text-lg font-bold text-gray-900">{crop.health.status}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Target className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Expected Yield</p>
              <p className="text-lg font-bold text-gray-900">{crop.yield.expected} {crop.yield.unit}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <Calendar className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Days to Harvest</p>
              <p className="text-lg font-bold text-gray-900">
                {Math.max(0, Math.ceil((new Date(crop.expectedHarvestDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Droplets className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Water Status</p>
              <p className="text-lg font-bold text-gray-900">{crop.waterAvailability}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {[
              { key: 'overview', label: 'Overview', icon: Leaf },
              { key: 'cultivation', label: 'Cultivation', icon: Activity },
              { key: 'resources', label: 'Resources', icon: BarChart3 },
              { key: 'marketplace', label: 'Marketplace', icon: Package }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.key
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white shadow rounded-lg p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Crop ID</label>
                    <p className="text-gray-900">{crop.cropId}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Farm ID</label>
                    <p className="text-gray-900">{crop.farmId}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Plot ID</label>
                    <p className="text-gray-900">{crop.plotId}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Planting Date</label>
                    <p className="text-gray-900">{new Date(crop.plantingDate).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Expected Harvest</label>
                    <p className="text-gray-900">{new Date(crop.expectedHarvestDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Farming Method</label>
                    <p className="text-gray-900">{crop.metadata.farmingMethod}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Irrigation Method</label>
                    <p className="text-gray-900">{crop.metadata.irrigationMethod}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Harvest Season</label>
                    <p className="text-gray-900">{crop.metadata.harvestSeason}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Soil Conditions */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Soil Conditions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Thermometer className="h-5 w-5 text-orange-600" />
                    <span className="font-medium text-gray-900">Soil Type</span>
                  </div>
                  <p className="text-gray-700">{crop.soilConditions.type}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Activity className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-gray-900">pH Level</span>
                  </div>
                  <p className="text-gray-700">{crop.soilConditions.ph}</p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Leaf className="h-5 w-5 text-green-600" />
                    <span className="font-medium text-gray-900">Nutrients</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {crop.soilConditions.nutrients.map((nutrient, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                        {nutrient}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Tags and Certifications */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Tag className="h-5 w-5 mr-2" />
                  Tags
                </h3>
                <div className="flex flex-wrap gap-2">
                  {crop.tags.map((tag, index) => (
                    <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Award className="h-5 w-5 mr-2" />
                  Certifications
                </h3>
                <div className="flex flex-wrap gap-2">
                  {crop.certifications.map((cert, index) => (
                    <span key={index} className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                      {cert}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Images */}
            {crop.images && crop.images.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Camera className="h-5 w-5 mr-2" />
                  Images
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {crop.images.map((image, index) => (
                    <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={image}
                        alt={`${crop.name} ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'cultivation' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Cultivation Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Irrigation Needs</label>
                  <p className="text-gray-900 mt-1">{crop.cultivation.irrigationNeeds}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Fertilizer Requirements</label>
                  <p className="text-gray-900 mt-1">{crop.cultivation.fertilizerRequirements}</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Pest Control</label>
                  <p className="text-gray-900 mt-1">{crop.cultivation.pestControl}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Climate Conditions</label>
                  <p className="text-gray-900 mt-1">{crop.cultivation.climateConditions}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'resources' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Resource Usage</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-50 p-6 rounded-lg">
                <div className="flex items-center space-x-3 mb-3">
                  <Droplets className="h-8 w-8 text-blue-600" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Water</h4>
                    <p className="text-2xl font-bold text-blue-600">{crop.resources.water}L</p>
                  </div>
                </div>
              </div>
              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex items-center space-x-3 mb-3">
                  <Leaf className="h-8 w-8 text-green-600" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Fertilizer</h4>
                    <p className="text-2xl font-bold text-green-600">{crop.resources.fertilizer}kg</p>
                  </div>
                </div>
              </div>
              <div className="bg-orange-50 p-6 rounded-lg">
                <div className="flex items-center space-x-3 mb-3">
                  <AlertCircle className="h-8 w-8 text-orange-600" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Pesticides</h4>
                    <p className="text-2xl font-bold text-orange-600">{crop.resources.pesticides}kg</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'marketplace' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Marketplace Information</h3>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                List in Marketplace
              </button>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Not Listed Yet</h4>
              <p className="text-gray-600 mb-4">This crop is not currently listed in the marketplace</p>
              <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                Create Marketplace Listing
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Edit Crop Modal */}
      <EditCropModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={() => {
          setShowEditModal(false);
          if (cropId) fetchCrop(cropId);
        }}
        crop={crop}
      />
    </div>
  );
};

export default CropViewPage;
