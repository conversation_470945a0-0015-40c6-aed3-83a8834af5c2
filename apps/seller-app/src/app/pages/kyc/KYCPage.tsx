import React, { useState } from 'react';
import { Shield, Upload, CheckCircle, Clock, AlertCircle, FileText } from 'lucide-react';

const KYCPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const documentStatus = {
    identityProof: 'verified',
    addressProof: 'pending',
    landOwnership: 'pending',
    bankStatement: 'not_uploaded'
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'rejected':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Upload className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'verified':
        return 'Verified';
      case 'pending':
        return 'Under Review';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Not Uploaded';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'rejected':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="bg-blue-100 p-3 rounded-full">
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">KYC & Document Verification</h1>
            <p className="text-gray-600">Complete your verification to access all platform features</p>
          </div>
        </div>
      </div>

      {/* KYC Status Overview */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">Verification Status</h2>
          <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
            In Progress
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="bg-green-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Identity Verified</p>
            <p className="text-xs text-gray-600">Aadhar Card</p>
          </div>

          <div className="text-center">
            <div className="bg-yellow-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Address Pending</p>
            <p className="text-xs text-gray-600">Under Review</p>
          </div>

          <div className="text-center">
            <div className="bg-yellow-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Land Ownership</p>
            <p className="text-xs text-gray-600">Under Review</p>
          </div>

          <div className="text-center">
            <div className="bg-gray-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
              <Upload className="h-6 w-6 text-gray-400" />
            </div>
            <p className="text-sm font-medium text-gray-900">Bank Statement</p>
            <p className="text-xs text-gray-600">Not Uploaded</p>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-blue-900">Verification in Progress</h3>
              <p className="text-sm text-blue-700 mt-1">
                Your documents are being reviewed. This process typically takes 2-3 business days. 
                You'll receive an email notification once verification is complete.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Document Upload Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Required Documents</h2>

        <div className="space-y-4">
          {/* Identity Proof */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(documentStatus.identityProof)}
                <div>
                  <h3 className="font-medium text-gray-900">Identity Proof</h3>
                  <p className="text-sm text-gray-600">Aadhar Card, PAN Card, or Passport</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(documentStatus.identityProof)}`}>
                  {getStatusText(documentStatus.identityProof)}
                </span>
                {documentStatus.identityProof === 'verified' ? (
                  <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
                ) : (
                  <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                    Upload
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Address Proof */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(documentStatus.addressProof)}
                <div>
                  <h3 className="font-medium text-gray-900">Address Proof</h3>
                  <p className="text-sm text-gray-600">Utility Bill, Bank Statement, or Rental Agreement</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(documentStatus.addressProof)}`}>
                  {getStatusText(documentStatus.addressProof)}
                </span>
                <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
              </div>
            </div>
          </div>

          {/* Land Ownership */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(documentStatus.landOwnership)}
                <div>
                  <h3 className="font-medium text-gray-900">Land Ownership Proof</h3>
                  <p className="text-sm text-gray-600">Land Records, Patta, or Lease Agreement</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(documentStatus.landOwnership)}`}>
                  {getStatusText(documentStatus.landOwnership)}
                </span>
                <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
              </div>
            </div>
          </div>

          {/* Bank Statement */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(documentStatus.bankStatement)}
                <div>
                  <h3 className="font-medium text-gray-900">Bank Statement</h3>
                  <p className="text-sm text-gray-600">Last 3 months bank statement</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(documentStatus.bankStatement)}`}>
                  {getStatusText(documentStatus.bankStatement)}
                </span>
                <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                  Upload
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Help Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border border-gray-200 rounded-lg p-4">
            <FileText className="h-6 w-6 text-blue-600 mb-2" />
            <h3 className="font-medium text-gray-900 mb-2">Document Guidelines</h3>
            <p className="text-sm text-gray-600 mb-3">
              Learn about acceptable document formats and requirements.
            </p>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              View Guidelines →
            </button>
          </div>

          <div className="border border-gray-200 rounded-lg p-4">
            <Shield className="h-6 w-6 text-green-600 mb-2" />
            <h3 className="font-medium text-gray-900 mb-2">Contact Support</h3>
            <p className="text-sm text-gray-600 mb-3">
              Get help with your verification process from our support team.
            </p>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              Contact Support →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCPage;
