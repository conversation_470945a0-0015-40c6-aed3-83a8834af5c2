import React, { useState } from 'react';
import { Tractor, Plus, MapPin, Droplets, Leaf, BarChart3, Edit, Eye, Trash2 } from 'lucide-react';
import { useGetFarmsQuery, useDeleteFarmMutation, useAppSelector, Farm } from '@befarmer-platform/shared-utils';
import CreateFarmModal from '../../components/farms/CreateFarmModal';
import FarmDetailsModal from '../../components/farms/FarmDetailsModal';

const FarmsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);

  // Get seller ID from auth state
  const sellerId = useAppSelector(state => state.auth.user?.id) || '';

  // Fetch farms data
  const { data: farmsResponse, isLoading, error, refetch } = useGetFarmsQuery({ sellerId });
  const [deleteFarm] = useDeleteFarmMutation();

  // Transform farms data to add computed properties for backward compatibility
  const farms = (farmsResponse?.farms || []).map((farm: any) => ({
    ...farm,
    id: farm._id,
    size: farm.totalArea,
    organicCertified: farm.certifications?.includes('Organic Certification') || false,
    verificationStatus: farm.status === 'ACTIVE' ? 'verified' : 'pending',
    registrationDate: farm.createdAt,
    lastUpdated: farm.updatedAt,
    isActive: farm.status === 'ACTIVE',
    crops: farm.currentCrops || []
  }));

  const handleDeleteFarm = async (farmId: string) => {
    if (window.confirm('Are you sure you want to delete this farm?')) {
      try {
        await deleteFarm(farmId).unwrap();
        refetch();
      } catch (error) {
        console.error('Failed to delete farm:', error);
      }
    }
  };

  const handleViewDetails = (farm: Farm) => {
    setSelectedFarm(farm);
    setIsDetailsModalOpen(true);
  };

  const formatLocation = (location: any) => {
    // Handle both old and new location structures
    if (location.village && location.district) {
      return `${location.village}, ${location.district}, ${location.state}`;
    } else if (location.city) {
      return `${location.addressLine1 || location.city}, ${location.city}, ${location.state}`;
    }
    return `${location.state}`;
  };

  const getSoilTypeLabel = (soilType: string) => {
    const soilTypeMap: Record<string, string> = {
      'LOAMY': 'Loamy Soil',
      'CLAY': 'Clay Soil',
      'SANDY': 'Sandy Soil',
      'CLAY_LOAM': 'Clay Loam',
      'SANDY_LOAM': 'Sandy Loam',
      'SILT': 'Silt Soil',
      'ROCKY': 'Rocky Soil',
    };
    return soilTypeMap[soilType] || soilType;
  };

  const getWaterSourceLabel = (waterSource: string) => {
    const waterSourceMap: Record<string, string> = {
      'BOREWELL': 'Borewell',
      'CANAL': 'Canal',
      'RIVER': 'River',
      'RAINWATER': 'Rainwater',
      'WELL': 'Well',
      'MULTIPLE': 'Multiple Sources',
    };
    return waterSourceMap[waterSource] || waterSource;
  };

  // Calculate statistics
  const totalFarms = farms.length;
  const totalArea = farms.reduce((sum, farm) => sum + (farm.totalArea || farm.size || 0), 0);
  const activeCrops = farms.reduce((sum, farm) => sum + (farm.currentCrops?.length || farm.crops?.length || 0), 0);
  const organicFarms = farms.filter(farm =>
    farm.organicCertified ||
    farm.certifications?.includes('Organic Certification') ||
    farm.farmingPractices?.primaryMethod === 'ORGANIC'
  ).length;

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center">
            <p className="text-red-600">Failed to load farms. Please try again.</p>
            <button
              onClick={() => refetch()}
              className="mt-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-green-100 p-3 rounded-full">
              <Tractor className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Farm Management</h1>
              <p className="text-gray-600">Manage your farms and agricultural operations</p>
            </div>
          </div>
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add New Farm</span>
          </button>
        </div>
      </div>

      {/* Farm Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Tractor className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Farms</p>
              <p className="text-2xl font-bold text-gray-900">{totalFarms}</p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <MapPin className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Area</p>
              <p className="text-2xl font-bold text-gray-900">
                {totalArea.toFixed(1)} acres
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Leaf className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Crops</p>
              <p className="text-2xl font-bold text-gray-900">
                {activeCrops}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <BarChart3 className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Organic Farms</p>
              <p className="text-2xl font-bold text-gray-900">{organicFarms}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Farms List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Your Farms</h2>
        </div>

        <div className="divide-y divide-gray-200">
          {farms.length === 0 ? (
            <div className="p-12 text-center">
              <Tractor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No farms registered</h3>
              <p className="text-gray-600 mb-4">Get started by registering your first farm.</p>
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 mx-auto"
              >
                <Plus className="h-4 w-4" />
                <span>Register First Farm</span>
              </button>
            </div>
          ) : (
            farms.map((farm) => (
              <div key={farm.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4">
                      <div className="bg-green-100 p-2 rounded-lg">
                        <Tractor className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-medium text-gray-900">{farm.name}</h3>
                          {(farm.organicCertified ||
                            farm.certifications?.includes('Organic Certification') ||
                            farm.farmingPractices?.primaryMethod === 'ORGANIC') && (
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                              Organic
                            </span>
                          )}
                          {farm.farmingPractices?.primaryMethod === 'INTEGRATED' && (
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                              Integrated
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-4 w-4" />
                            <span>{formatLocation(farm.location)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <span>{farm.totalArea || farm.size} acres</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Droplets className="h-4 w-4" />
                            <span>{farm.waterSource}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{farm.currentCrops?.length || farm.crops?.length || 0} Active Crops</p>
                      <p className="text-sm text-gray-600">Soil: {farm.soilType}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      farm.status === 'ACTIVE' || farm.verificationStatus === 'verified'
                        ? 'bg-green-100 text-green-800'
                        : farm.status === 'PENDING' || farm.verificationStatus === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {farm.status || farm.verificationStatus?.charAt(0).toUpperCase() + farm.verificationStatus?.slice(1)}
                    </span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleViewDetails(farm)}
                        className="text-blue-600 hover:text-blue-800 p-1 rounded transition-colors"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        className="text-gray-600 hover:text-gray-800 p-1 rounded transition-colors"
                        title="Edit Farm"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteFarm(farm._id || farm.id)}
                        className="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                        title="Delete Farm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Farm Quick Stats */}
                <div className="mt-4 grid grid-cols-4 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs text-gray-600">Sustainability Score</p>
                    <p className="text-sm font-semibold text-gray-900">
                      {farm.farmingPractices?.sustainabilityScore || 'N/A'}
                      {farm.farmingPractices?.sustainabilityScore && '%'}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs text-gray-600">Farming Method</p>
                    <p className="text-sm font-semibold text-gray-900">
                      {farm.farmingPractices?.primaryMethod || 'Conventional'}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs text-gray-600">Status</p>
                    <p className={`text-sm font-semibold ${
                      farm.status === 'ACTIVE' || farm.isActive ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {farm.status || (farm.isActive ? 'Active' : 'Inactive')}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-xs text-gray-600">Last Updated</p>
                    <p className="text-sm font-semibold text-gray-900">
                      {new Date(farm.updatedAt || farm.lastUpdated).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <Plus className="h-6 w-6 text-green-600 mb-2" />
            <h3 className="font-medium text-gray-900">Register New Farm</h3>
            <p className="text-sm text-gray-600">Add a new farm to your portfolio</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <BarChart3 className="h-6 w-6 text-blue-600 mb-2" />
            <h3 className="font-medium text-gray-900">View Analytics</h3>
            <p className="text-sm text-gray-600">Analyze farm performance and trends</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <MapPin className="h-6 w-6 text-purple-600 mb-2" />
            <h3 className="font-medium text-gray-900">Search Nearby</h3>
            <p className="text-sm text-gray-600">Find farms in your area</p>
          </button>
        </div>
      </div>

      {/* Create Farm Modal */}
      <CreateFarmModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        sellerId={sellerId}
      />

      {/* Farm Details Modal */}
      <FarmDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedFarm(null);
        }}
        farm={selectedFarm}
      />
    </div>
  );
};

export default FarmsPage;
