import React from 'react';
import { FileText, Download, Calendar, TrendingUp } from 'lucide-react';

const ReportsPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="bg-indigo-100 p-3 rounded-full">
            <FileText className="h-8 w-8 text-indigo-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Documentation</h1>
            <p className="text-gray-600">Generate and download various reports</p>
          </div>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="bg-white shadow rounded-lg p-12 text-center">
        <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Reports Module Coming Soon</h2>
        <p className="text-gray-600 mb-6">
          Comprehensive reporting system for all your agricultural data including 
          crop reports, financial statements, and compliance documents.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
          <div className="p-4 bg-gray-50 rounded-lg">
            <Download className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Export Reports</h3>
            <p className="text-sm text-gray-600">PDF, Excel, and CSV formats</p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <Calendar className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Scheduled Reports</h3>
            <p className="text-sm text-gray-600">Automated report generation</p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Custom Analytics</h3>
            <p className="text-sm text-gray-600">Tailored business insights</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;
