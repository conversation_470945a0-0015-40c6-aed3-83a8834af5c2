import React from 'react';
import { BarChart3, TrendingUp, DollarSign, Calendar } from 'lucide-react';

const AnalyticsPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="bg-blue-100 p-3 rounded-full">
            <BarChart3 className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics & Insights</h1>
            <p className="text-gray-600">Track your farm performance and business metrics</p>
          </div>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="bg-white shadow rounded-lg p-12 text-center">
        <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Analytics Dashboard Coming Soon</h2>
        <p className="text-gray-600 mb-6">
          We're building comprehensive analytics to help you track farm performance, 
          revenue trends, and crop insights.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
          <div className="p-4 bg-gray-50 rounded-lg">
            <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Performance Metrics</h3>
            <p className="text-sm text-gray-600">Track yield, revenue, and efficiency</p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <DollarSign className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Financial Reports</h3>
            <p className="text-sm text-gray-600">Revenue, costs, and profit analysis</p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Seasonal Trends</h3>
            <p className="text-sm text-gray-600">Historical data and predictions</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
