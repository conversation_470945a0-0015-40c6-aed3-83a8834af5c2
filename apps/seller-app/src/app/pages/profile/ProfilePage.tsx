import React, { useEffect, useState } from 'react';
import {
  useGetSellerProfileQuery,
  useUpdateSellerProfileMutation,
  useUpdateBankDetailsMutation,
  useChangePasswordMutation,
  SellerUpdateRequest,
  BankDetailsRequest,
  ChangePasswordRequest,
  useAppDispatch,
  clearError
} from '@befarmer-platform/shared-utils';
import { User, Mail, Phone, MapPin, Calendar, Shield, Edit2, Save, X, Loader2, RefreshCw } from 'lucide-react';

const ProfilePage: React.FC = () => {
  const dispatch = useAppDispatch();

  // ✅ FIXED: Using RTK Query hooks that connect directly to seller service
  const {
    data: profile,
    isLoading,
    error,
    refetch
  } = useGetSellerProfileQuery(undefined);

  const [updateSellerProfile, { isLoading: isUpdating }] = useUpdateSellerProfileMutation();
  const [updateBankDetails, { isLoading: isUpdatingBank }] = useUpdateBankDetailsMutation();
  const [changePassword, { isLoading: isChangingPassword }] = useChangePasswordMutation();

  // Local state for editing
  const [isEditing, setIsEditing] = useState(false);
  const [isBankEditing, setIsBankEditing] = useState(false);
  const [isPasswordChanging, setIsPasswordChanging] = useState(false);

  const [editForm, setEditForm] = useState<SellerUpdateRequest>({
    name: '',
    phone: '',
    location: {
      state: '',
      district: '',
      coordinates: {
        latitude: 0,
        longitude: 0,
      },
    },
    preferences: {
      language: 'en',
      notifications: {
        email: true,
        sms: false,
        push: true,
      },
    },
  });

  const [bankForm, setBankForm] = useState<BankDetailsRequest>({
    accountNumber: '',
    bankName: '',
    ifscCode: '',
    accountHolderName: '',
    branchName: '',
  });

  const [passwordForm, setPasswordForm] = useState<ChangePasswordRequest>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Update form when profile data is loaded
  useEffect(() => {
    if (profile) {
      setEditForm({
        name: profile.personalInfo?.name || '',
        phone: profile.personalInfo?.contact || '',
        location: {
          state: profile.personalInfo?.address?.state || '',
          district: profile.personalInfo?.address?.city || '', // Using city as district for now
          coordinates: {
            latitude: 0,
            longitude: 0,
          },
        },
        preferences: {
          language: 'en',
          notifications: {
            email: true,
            sms: false,
            push: true,
          },
        },
      });

      // Update bank form with actual bank details
      if (profile.bankDetails) {
        setBankForm({
          accountNumber: profile.bankDetails.accountNumber || '',
          bankName: profile.bankDetails.bankName || '',
          ifscCode: profile.bankDetails.ifscCode || '',
          accountHolderName: profile.personalInfo?.name || '',
          branchName: '',
        });
      }
    }
  }, [profile]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      if (error) {
        dispatch(clearError());
      }
    };
  }, [error]);

  const getVerificationStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form to original values
      if (profile) {
        setEditForm({
          name: profile.personalInfo?.name || '',
          phone: profile.personalInfo?.contact || '',
          location: {
            state: profile.personalInfo?.address?.state || '',
            district: profile.personalInfo?.address?.city || '',
            coordinates: {
              latitude: 0,
              longitude: 0,
            },
          },
          preferences: {
            language: 'en',
            notifications: {
              email: true,
              sms: false,
              push: true,
            },
          },
        });
      }
    }
    setIsEditing(!isEditing);
  };

  const handleSave = async () => {
    if (!profile) return;

    try {
      // ✅ FIXED: Transform data to match API expected format
      const updateData = {
        name: editForm.name,
        phone: editForm.phone,
        location: {
          state: editForm.location?.state,
          district: editForm.location?.district,
          coordinates: editForm.location?.coordinates
        },
        preferences: editForm.preferences
      };

      await updateSellerProfile(updateData).unwrap();
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  const handleBankSave = async () => {
    try {
      await updateBankDetails(bankForm).unwrap();
      setIsBankEditing(false);
    } catch (error) {
      console.error('Failed to update bank details:', error);
    }
  };

  const handlePasswordSave = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      console.error('Passwords do not match');
      return;
    }

    try {
      await changePassword(passwordForm).unwrap();
      setIsPasswordChanging(false);
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error) {
      console.error('Failed to change password:', error);
    }
  };

  const handleRefresh = () => {
    // ✅ FIXED: Using RTK Query refetch
    refetch();
  };

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('location.')) {
      const locationField = field.split('.')[1];
      if (locationField === 'coordinates') {
        // Handle coordinates separately if needed
        return;
      }
      setEditForm(prev => ({
        ...prev,
        location: {
          ...prev.location!,
          [locationField]: value,
        },
      }));
    } else if (field.startsWith('preferences.')) {
      const prefField = field.split('.')[1];
      if (prefField === 'notifications') {
        // Handle notifications separately if needed
        return;
      }
      setEditForm(prev => ({
        ...prev,
        preferences: {
          ...prev.preferences!,
          [prefField]: value,
        },
      }));
    } else {
      setEditForm(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleBankInputChange = (field: string, value: string) => {
    setBankForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordInputChange = (field: string, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
  };



  // Calculate profile completion percentage
  const calculateProfileCompletion = () => {
    if (!profile) return 0;

    let completed = 0;
    const total = 5; // Total fields to check

    if (profile.personalInfo?.name) completed++;
    if (profile.personalInfo?.email) completed++;
    if (profile.personalInfo?.contact) completed++;
    if (profile.personalInfo?.address) completed++;
    if (profile.verificationStatus === 'VERIFIED') completed++;

    return Math.round((completed / total) * 100);
  };

  // Show loading state
  if (isLoading && !profile) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center space-x-4">
            <div className="bg-green-100 p-3 rounded-full">
              <Loader2 className="h-8 w-8 text-green-600 animate-spin" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Loading Profile...</h1>
              <p className="text-gray-600">Fetching your profile information</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">

      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="bg-green-100 p-3 rounded-full">
            <User className="h-8 w-8 text-green-600" />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">Profile Management</h1>
            <p className="text-gray-600">Manage your seller profile and account information</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Profile Information */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">Personal Information</h2>
          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  disabled={isUpdating}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  {isUpdating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isUpdating ? 'Saving...' : 'Save'}</span>
                </button>
                <button
                  onClick={handleEditToggle}
                  disabled={isUpdating}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </button>
              </>
            ) : (
              <button
                onClick={handleEditToggle}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Edit2 className="h-4 w-4" />
                <span>Edit Profile</span>
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <User className="h-5 w-5 text-gray-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">Full Name</p>
                {isEditing ? (
                  <input
                    type="text"
                    value={editForm.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                ) : (
                  <p className="text-gray-900">{profile?.personalInfo?.name || 'Not provided'}</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Mail className="h-5 w-5 text-gray-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">Email Address</p>
                <p className="text-gray-900">{profile?.personalInfo?.email || 'Not provided'}</p>
                {isEditing && (
                  <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Phone className="h-5 w-5 text-gray-400" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">Phone Number</p>
                {isEditing ? (
                  <input
                    type="tel"
                    value={editForm.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter your phone number"
                  />
                ) : (
                  <p className="text-gray-900">{profile?.personalInfo?.contact || 'Not provided'}</p>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Shield className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-500">Verification Status</p>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getVerificationStatusColor(profile?.verificationStatus || 'pending')}`}>
                  {profile?.verificationStatus || 'Pending'}
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-500">Member Since</p>
                <p className="text-gray-900">
                  {profile?.createdAt
                    ? new Date(profile.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    : 'March 2024'
                  }
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">Location</p>
                {isEditing ? (
                  <div className="space-y-2 mt-1">
                    <div className="grid grid-cols-2 gap-2">
                      <input
                        type="text"
                        value={editForm.location?.state || ''}
                        onChange={(e) => handleInputChange('location.state', e.target.value)}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="State"
                      />
                      <input
                        type="text"
                        value={editForm.location?.district || ''}
                        onChange={(e) => handleInputChange('location.district', e.target.value)}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="District"
                      />
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-900">
                    {profile?.personalInfo?.address ? (
                      <>
                        {profile.personalInfo.address.addressLine1}<br />
                        {profile.personalInfo.address.city}, {profile.personalInfo.address.state} {profile.personalInfo.address.pincode}
                      </>
                    ) : (
                      'Not provided'
                    )}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bank Details */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">Bank Details</h2>
          <div className="flex space-x-2">
            {isBankEditing ? (
              <>
                <button
                  onClick={handleBankSave}
                  disabled={isUpdatingBank}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  {isUpdatingBank ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isUpdatingBank ? 'Saving...' : 'Save'}</span>
                </button>
                <button
                  onClick={() => setIsBankEditing(false)}
                  disabled={isUpdatingBank}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsBankEditing(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Edit2 className="h-4 w-4" />
                <span>Edit Bank Details</span>
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Account Number</label>
              {isBankEditing ? (
                <input
                  type="text"
                  value={bankForm.accountNumber}
                  onChange={(e) => handleBankInputChange('accountNumber', e.target.value)}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter account number"
                />
              ) : (
                <p className="text-gray-900 mt-1">{bankForm.accountNumber || 'Not provided'}</p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Bank Name</label>
              {isBankEditing ? (
                <input
                  type="text"
                  value={bankForm.bankName}
                  onChange={(e) => handleBankInputChange('bankName', e.target.value)}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter bank name"
                />
              ) : (
                <p className="text-gray-900 mt-1">{bankForm.bankName || 'Not provided'}</p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">IFSC Code</label>
              {isBankEditing ? (
                <input
                  type="text"
                  value={bankForm.ifscCode}
                  onChange={(e) => handleBankInputChange('ifscCode', e.target.value)}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter IFSC code"
                />
              ) : (
                <p className="text-gray-900 mt-1">{bankForm.ifscCode || 'Not provided'}</p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Account Holder Name</label>
              {isBankEditing ? (
                <input
                  type="text"
                  value={bankForm.accountHolderName}
                  onChange={(e) => handleBankInputChange('accountHolderName', e.target.value)}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter account holder name"
                />
              ) : (
                <p className="text-gray-900 mt-1">{bankForm.accountHolderName || 'Not provided'}</p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Branch Name</label>
              {isBankEditing ? (
                <input
                  type="text"
                  value={bankForm.branchName}
                  onChange={(e) => handleBankInputChange('branchName', e.target.value)}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter branch name"
                />
              ) : (
                <p className="text-gray-900 mt-1">{bankForm.branchName || 'Not provided'}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Password Change */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">Change Password</h2>
          <div className="flex space-x-2">
            {isPasswordChanging ? (
              <>
                <button
                  onClick={handlePasswordSave}
                  disabled={isChangingPassword}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  {isChangingPassword ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isChangingPassword ? 'Changing...' : 'Change Password'}</span>
                </button>
                <button
                  onClick={() => setIsPasswordChanging(false)}
                  disabled={isChangingPassword}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsPasswordChanging(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Shield className="h-4 w-4" />
                <span>Change Password</span>
              </button>
            )}
          </div>
        </div>

        {isPasswordChanging && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Current Password</label>
              <input
                type="password"
                value={passwordForm.currentPassword}
                onChange={(e) => handlePasswordInputChange('currentPassword', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter current password"
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">New Password</label>
              <input
                type="password"
                value={passwordForm.newPassword}
                onChange={(e) => handlePasswordInputChange('newPassword', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter new password"
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Confirm New Password</label>
              <input
                type="password"
                value={passwordForm.confirmPassword}
                onChange={(e) => handlePasswordInputChange('confirmPassword', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Confirm new password"
              />
            </div>
          </div>
        )}
      </div>

      {/* Account Status */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Account Status</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900">Profile Completion</h3>
            <p className="text-2xl font-bold text-green-600 mt-2">
              {calculateProfileCompletion()}%
            </p>
            <p className="text-sm text-gray-600 mt-1">
              {calculateProfileCompletion() === 100 ? 'Complete' : 'Missing some details'}
            </p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900">KYC Status</h3>
            <p className={`text-2xl font-bold mt-2 ${
              profile?.verificationStatus === 'VERIFIED' ? 'text-green-600' :
              profile?.verificationStatus === 'REJECTED' ? 'text-red-600' : 'text-yellow-600'
            }`}>
              {profile?.verificationStatus === 'VERIFIED' ? 'Verified' :
               profile?.verificationStatus === 'REJECTED' ? 'Rejected' : 'Pending'}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              {profile?.verificationStatus === 'VERIFIED' ? 'KYC completed' :
               profile?.verificationStatus === 'REJECTED' ? 'Documents rejected' : 'Documents under review'}
            </p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900">Account Type</h3>
            <p className="text-2xl font-bold text-blue-600 mt-2">Seller</p>
            <p className="text-sm text-gray-600 mt-1">Agricultural seller account</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Shield className="h-6 w-6 text-green-600 mb-2" />
            <h3 className="font-medium text-gray-900">Complete KYC</h3>
            <p className="text-sm text-gray-600">Upload verification documents</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <User className="h-6 w-6 text-blue-600 mb-2" />
            <h3 className="font-medium text-gray-900">Update Profile</h3>
            <p className="text-sm text-gray-600">Edit personal information</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <MapPin className="h-6 w-6 text-purple-600 mb-2" />
            <h3 className="font-medium text-gray-900">Add Address</h3>
            <p className="text-sm text-gray-600">Update contact details</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
            <Phone className="h-6 w-6 text-orange-600 mb-2" />
            <h3 className="font-medium text-gray-900">Verify Phone</h3>
            <p className="text-sm text-gray-600">Confirm phone number</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
