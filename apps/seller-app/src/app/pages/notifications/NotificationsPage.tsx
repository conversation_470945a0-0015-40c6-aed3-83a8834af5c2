import React from 'react';
import { Bell, CheckCircle, AlertCircle, Info } from 'lucide-react';

const NotificationsPage: React.FC = () => {
  const notifications = [
    {
      id: '1',
      type: 'success',
      title: 'Crop Ready for Harvest',
      message: 'Your tomato crop in Plot A is ready for harvest. Expected yield: 500kg',
      time: '2 hours ago',
      read: false
    },
    {
      id: '2',
      type: 'warning',
      title: 'Weather Alert',
      message: 'Heavy rain expected in your area tomorrow. Consider protective measures.',
      time: '5 hours ago',
      read: false
    },
    {
      id: '3',
      type: 'info',
      title: 'Order Received',
      message: 'New order for 50kg potatoes from Green Grocers. Order ID: ORD-004',
      time: '1 day ago',
      read: true
    },
    {
      id: '4',
      type: 'success',
      title: 'Payment Received',
      message: 'Payment of ₹22,000 received for Order ORD-003',
      time: '2 days ago',
      read: true
    }
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  const getNotificationBg = (type: string, read: boolean) => {
    const opacity = read ? '50' : '100';
    switch (type) {
      case 'success':
        return `bg-green-${opacity}`;
      case 'warning':
        return `bg-yellow-${opacity}`;
      case 'error':
        return `bg-red-${opacity}`;
      default:
        return `bg-blue-${opacity}`;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-blue-100 p-3 rounded-full">
              <Bell className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
              <p className="text-gray-600">Stay updated with important alerts and messages</p>
            </div>
          </div>
          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
            Mark all as read
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Notifications</h2>
        </div>

        <div className="divide-y divide-gray-200">
          {notifications.map((notification) => (
            <div 
              key={notification.id} 
              className={`p-6 hover:bg-gray-50 transition-colors ${
                !notification.read ? 'bg-blue-50' : ''
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className={`p-2 rounded-full ${getNotificationBg(notification.type, notification.read)}`}>
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className={`text-sm font-medium ${
                      !notification.read ? 'text-gray-900' : 'text-gray-700'
                    }`}>
                      {notification.title}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">{notification.time}</span>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      )}
                    </div>
                  </div>
                  <p className={`mt-1 text-sm ${
                    !notification.read ? 'text-gray-700' : 'text-gray-600'
                  }`}>
                    {notification.message}
                  </p>
                  {!notification.read && (
                    <button className="mt-2 text-xs text-blue-600 hover:text-blue-800">
                      Mark as read
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Crop Alerts</h3>
              <p className="text-sm text-gray-600">Notifications about crop status and harvest readiness</p>
            </div>
            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600 rounded" />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Weather Alerts</h3>
              <p className="text-sm text-gray-600">Weather warnings and forecasts for your area</p>
            </div>
            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600 rounded" />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Order Updates</h3>
              <p className="text-sm text-gray-600">New orders and order status changes</p>
            </div>
            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600 rounded" />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Payment Notifications</h3>
              <p className="text-sm text-gray-600">Payment confirmations and reminders</p>
            </div>
            <input type="checkbox" defaultChecked className="h-4 w-4 text-blue-600 rounded" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;
