import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { validators, validateForm } from '@befarmer-platform/shared-utils';
import { Button, Input, Card, Alert } from '@befarmer-platform/shared-ui';
import { Leaf, Sprout, Mail, ArrowLeft, Loader2 } from 'lucide-react';

interface ForgotPasswordFormData {
  email: string;
}

export const ForgotPasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<ForgotPasswordFormData>({ email: '' });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetStatus, setResetStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [resetMessage, setResetMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateResetForm = (): boolean => {
    const rules = {
      email: [
        { required: true },
        { custom: validators.email }
      ],
    };

    const { isValid, errors: validationErrors } = validateForm(formData, rules);
    setErrors(validationErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetStatus('idle');
    setResetMessage('');

    if (!validateResetForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual API call to request password reset
      await new Promise(resolve => setTimeout(resolve, 2000));

      setResetStatus('success');
      setResetMessage(
        'Password reset instructions have been sent to your email. ' +
        'Please check your inbox and follow the instructions to reset your password.'
      );
    } catch (error) {
      setResetStatus('error');
      setResetMessage(
        error instanceof Error 
          ? error.message 
          : 'Failed to send reset instructions. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left Panel - Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 py-12 bg-white relative">
        <div className="sm:mx-auto sm:w-full sm:max-w-md space-y-8">
          {/* Logo and Header */}
          <div className="text-center space-y-6">
            <div 
              className="mx-auto h-20 w-20 bg-gradient-to-br from-green-500 to-orange-500 rounded-3xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300 cursor-pointer"
              onClick={() => navigate('/')}
            >
              <Leaf className="h-10 w-10 text-white" strokeWidth={1.5} />
            </div>
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl font-display">
                Reset Password
              </h2>
              <p className="mt-3 text-lg text-gray-600">
                Enter your email address and we'll send you instructions to reset your password
              </p>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white py-8 px-4 shadow-sm rounded-2xl border border-gray-100">
            {resetStatus === 'success' ? (
              <div className="text-center space-y-6 animate-fadeIn">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <Mail className="h-8 w-8 text-green-600" />
                </div>
                <Alert
                  variant="success"
                  message={resetMessage}
                  className="bg-green-50 border-green-100"
                />
                <Link
                  to="/auth/login"
                  className="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-green-600 hover:text-green-500 transition-colors duration-200"
                >
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Return to Login
                </Link>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                {resetStatus === 'error' && (
                  <Alert
                    variant="error"
                    message={resetMessage}
                    className="animate-shake"
                  />
                )}

                <Input
                  name="email"
                  type="email"
                  label="Email Address"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  error={errors.email}
                  placeholder="Enter your email address"
                  startIcon={<Mail className="h-5 w-5 text-gray-400" />}
                />

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn-primary"
                >
                  {isSubmitting ? (
                    <Loader2 className="animate-spin h-5 w-5 mx-auto" />
                  ) : (
                    'Send Reset Instructions'
                  )}
                </button>

                <div className="text-center">
                  <Link
                    to="/auth/login"
                    className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 transition-colors duration-200"
                  >
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Back to Login
                  </Link>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>

      {/* Right Panel - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/90 to-orange-500/90 mix-blend-multiply" />
          <img
            className="h-full w-full object-cover"
            src="https://images.pexels.com/photos/2132171/pexels-photo-2132171.jpeg"
            alt="Agricultural field at sunset"
          />
          <div className="absolute inset-0 bg-black/20" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 p-12">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Sprout className="h-8 w-8 text-white" />
              <h3 className="text-2xl font-bold text-white">BeFarma</h3>
            </div>
            <p className="text-lg text-white/90">
              Secure access to your agricultural management platform.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage; 