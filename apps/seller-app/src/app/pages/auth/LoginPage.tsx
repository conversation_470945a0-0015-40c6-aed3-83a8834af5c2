import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  useAppDispatch,
  loginUser,
  selectAuthLoading,
  selectAuthError,
  clearError,
  validators,
  validateForm
} from '@befarmer-platform/shared-utils';
import { useSelector } from 'react-redux';
import { Leaf, Sprout, Mail, Lock, Eye, EyeOff, Loader2 } from 'lucide-react';

interface LoginFormData {
  email: string;
  password: string;
}

export const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const isLoading = useSelector(selectAuthLoading);
  const authError = useSelector(selectAuthError);

  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const from = (location.state as any)?.from?.pathname || '/dashboard';

  // Clear auth error when component mounts or form data changes
  useEffect(() => {
    if (authError) {
      dispatch(clearError());
    }
  }, [formData, dispatch, authError]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateLoginForm = (): boolean => {
    const rules = {
      email: [
        { required: true },
        { custom: validators.email }
      ],
      password: [
        { required: true },
        { minLength: 6 }
      ],
    };

    const { isValid, errors: validationErrors } = validateForm(formData, rules);
    setErrors(validationErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateLoginForm()) {
      return;
    }

    try {
      await dispatch(loginUser(formData)).unwrap();
      navigate(from, { replace: true });
    } catch (error) {
      // Error is handled by Redux and displayed via authError selector
      console.error('Login failed:', error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left Panel - Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 py-12 bg-white relative">
        <div className="sm:mx-auto sm:w-full sm:max-w-md space-y-8">
          {/* Logo and Header */}
          <div className="text-center space-y-6">
            <div 
              className="mx-auto h-20 w-20 bg-gradient-to-br from-green-500 to-orange-500 rounded-3xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300 cursor-pointer"
              onClick={() => navigate('/')}
            >
              <Leaf className="h-10 w-10 text-white" strokeWidth={1.5} />
            </div>
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl font-display">
                Welcome Back
              </h2>
              <p className="mt-3 text-lg text-gray-600">
                Sign in to manage your agricultural ventures
              </p>
            </div>
          </div>

          {/* Login Form */}
          <form className="space-y-6 mt-8" onSubmit={handleSubmit}>
            {authError && (
              <div className="rounded-xl bg-red-50 border border-red-200 p-4 animate-shake">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{authError}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Email Field */}
            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-400 group-hover:text-green-500 transition-colors duration-200">
                  <Mail className="h-5 w-5" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email"
                  className={`input-base pl-12 ${
                    errors.email 
                      ? 'border-red-300 focus:ring-red-500 animate-shake' 
                      : 'hover:border-green-300'
                  }`}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 animate-slideIn">{errors.email}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-400 group-hover:text-green-500 transition-colors duration-200">
                  <Lock className="h-5 w-5" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Enter your password"
                  className={`input-base pl-12 pr-12 ${
                    errors.password 
                      ? 'border-red-300 focus:ring-red-500 animate-shake' 
                      : 'hover:border-green-300'
                  }`}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-200"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600 animate-slideIn">{errors.password}</p>
              )}
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded transition-colors duration-200"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>

              <Link
                to="/auth/forgot-password"
                className="text-sm font-medium text-green-600 hover:text-green-500 transition-colors duration-200"
              >
                Forgot password?
              </Link>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary w-full"
            >
              {isLoading ? (
                <Loader2 className="animate-spin h-5 w-5" />
              ) : (
                'Sign In'
              )}
            </button>

            {/* Sign Up Link */}
            <div className="text-center">
              <p className="text-sm text-gray-600">
                Don't have an account?{' '}
                <Link
                  to="/auth/register"
                  className="font-medium text-green-600 hover:text-green-500 transition-colors duration-200"
                >
                  Sign up now
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Right Panel - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/90 to-orange-500/90 mix-blend-multiply" />
          <img
            className="h-full w-full object-cover"
            src="https://images.pexels.com/photos/2132171/pexels-photo-2132171.jpeg"
            alt="Agricultural field at sunset"
          />
          <div className="absolute inset-0 bg-black/20" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 p-12">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Sprout className="h-8 w-8 text-white" />
              <h3 className="text-2xl font-bold text-white">BeFarma</h3>
            </div>
            <p className="text-lg text-white/90">
              Your trusted partner in agricultural innovation and sustainable farming practices.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage; 