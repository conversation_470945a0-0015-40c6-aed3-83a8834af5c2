import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { validators, validateForm, ValidationResult } from '@befarmer-platform/shared-utils';
import { Button, Input, Card, Alert } from '@befarmer-platform/shared-ui';
import { Leaf, Sprout, Lock, Eye, EyeOff, ArrowLeft, CheckCircle, Loader2 } from 'lucide-react';

interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

export const ResetPasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const resetToken = searchParams.get('token');
  
  const [formData, setFormData] = useState<ResetPasswordFormData>({
    password: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetStatus, setResetStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [resetMessage, setResetMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (!resetToken) {
      setResetStatus('error');
      setResetMessage('Invalid or expired reset token. Please request a new password reset.');
    }
  }, [resetToken]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateResetForm = (): boolean => {
    const rules = {
      password: [
        { required: true },
        { minLength: 8 },
        { 
          custom: (value: string): ValidationResult => {
            const hasUppercase = /[A-Z]/.test(value);
            const hasLowercase = /[a-z]/.test(value);
            const hasNumber = /[0-9]/.test(value);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);
            
            if (!hasUppercase) return { isValid: false, message: 'Password must contain at least one uppercase letter' };
            if (!hasLowercase) return { isValid: false, message: 'Password must contain at least one lowercase letter' };
            if (!hasNumber) return { isValid: false, message: 'Password must contain at least one number' };
            if (!hasSpecial) return { isValid: false, message: 'Password must contain at least one special character' };
            
            return { isValid: true };
          }
        }
      ],
      confirmPassword: [
        { required: true },
        { custom: (value: string): ValidationResult => ({ 
          isValid: value === formData.password,
          message: value === formData.password ? undefined : 'Passwords do not match'
        })}
      ],
    };

    const { isValid, errors: validationErrors } = validateForm(formData, rules);
    setErrors(validationErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!resetToken) {
      return;
    }

    if (!validateResetForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual API call to reset password
      await new Promise(resolve => setTimeout(resolve, 2000));

      setResetStatus('success');
      setResetMessage(
        'Your password has been successfully reset. ' +
        'You can now log in with your new password.'
      );

      setTimeout(() => {
        navigate('/auth/login');
      }, 3000);
    } catch (error) {
      setResetStatus('error');
      setResetMessage(
        error instanceof Error 
          ? error.message 
          : 'Failed to reset password. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left Panel - Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 py-12 bg-white relative">
        <div className="sm:mx-auto sm:w-full sm:max-w-md space-y-8">
          {/* Logo and Header */}
          <div className="text-center space-y-6">
            <div 
              className="mx-auto h-20 w-20 bg-gradient-to-br from-green-500 to-orange-500 rounded-3xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300 cursor-pointer"
              onClick={() => navigate('/')}
            >
              <Leaf className="h-10 w-10 text-white" strokeWidth={1.5} />
            </div>
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl font-display">
                Reset Password
              </h2>
              <p className="mt-3 text-lg text-gray-600">
                Enter your new password to secure your account
              </p>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white py-8 px-4 shadow-sm rounded-2xl border border-gray-100">
            {resetStatus === 'success' ? (
              <div className="text-center space-y-6 animate-fadeIn">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <Alert
                  variant="success"
                  message={resetMessage}
                  className="bg-green-50 border-green-100"
                />
                <p className="text-sm text-gray-600">
                  Redirecting to login page...
                </p>
              </div>
            ) : resetStatus === 'error' && !resetToken ? (
              <div className="text-center space-y-6 animate-fadeIn">
                <Alert
                  variant="error"
                  message={resetMessage}
                  className="animate-shake"
                />
                <Link
                  to="/auth/forgot-password"
                  className="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-green-600 hover:text-green-500 transition-colors duration-200"
                >
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Request New Reset Link
                </Link>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                {resetStatus === 'error' && (
                  <Alert
                    variant="error"
                    message={resetMessage}
                    className="animate-shake"
                  />
                )}

                <div className="space-y-6">
                  <div>
                    <Input
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      label="New Password"
                      required
                      value={formData.password}
                      onChange={handleInputChange}
                      error={errors.password}
                      placeholder="Enter your new password"
                      startIcon={<Lock className="h-5 w-5 text-gray-400" />}
                      endIcon={
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="text-gray-400 hover:text-gray-600 focus:outline-none"
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </button>
                      }
                    />
                    <ul className="mt-2 text-sm text-gray-600 space-y-1">
                      <li className="flex items-center space-x-2">
                        <div className={`w-1.5 h-1.5 rounded-full ${/[A-Z]/.test(formData.password) ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span>At least one uppercase letter</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className={`w-1.5 h-1.5 rounded-full ${/[a-z]/.test(formData.password) ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span>At least one lowercase letter</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className={`w-1.5 h-1.5 rounded-full ${/[0-9]/.test(formData.password) ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span>At least one number</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className={`w-1.5 h-1.5 rounded-full ${/[!@#$%^&*(),.?":{}|<>]/.test(formData.password) ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span>At least one special character</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className={`w-1.5 h-1.5 rounded-full ${formData.password.length >= 8 ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span>Minimum 8 characters</span>
                      </li>
                    </ul>
                  </div>

                  <Input
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    label="Confirm New Password"
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    error={errors.confirmPassword}
                    placeholder="Confirm your new password"
                    startIcon={<Lock className="h-5 w-5 text-gray-400" />}
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting || !resetToken}
                  className="w-full btn-primary"
                >
                  {isSubmitting ? (
                    <Loader2 className="animate-spin h-5 w-5 mx-auto" />
                  ) : (
                    'Reset Password'
                  )}
                </button>

                <div className="text-center">
                  <Link
                    to="/auth/login"
                    className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 transition-colors duration-200"
                  >
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Back to Login
                  </Link>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>

      {/* Right Panel - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/90 to-orange-500/90 mix-blend-multiply" />
          <img
            className="h-full w-full object-cover"
            src="https://images.pexels.com/photos/2132171/pexels-photo-2132171.jpeg"
            alt="Agricultural field at sunset"
          />
          <div className="absolute inset-0 bg-black/20" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 p-12">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Sprout className="h-8 w-8 text-white" />
              <h3 className="text-2xl font-bold text-white">BeFarma</h3>
            </div>
            <p className="text-lg text-white/90">
              Secure access to your agricultural management platform.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage; 