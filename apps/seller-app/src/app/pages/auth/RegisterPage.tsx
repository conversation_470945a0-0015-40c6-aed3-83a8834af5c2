import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth, validators, agriculturalValidators, validateForm } from '@befarmer-platform/shared-utils';
import { Button, Input, Card, Alert, Spinner } from '@befarmer-platform/shared-ui';
import { Leaf, Sprout, User, Phone, MapPin, Upload, Wallet, ChevronRight, ChevronLeft, Eye, EyeOff, Loader2 } from 'lucide-react';

interface RegistrationFormData {
  // Step 1: Personal Information
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  
  // Step 2: Contact Details
  phone: string;
  whatsapp: string;
  
  // Step 3: Address Information
  streetAddress: string;
  city: string;
  state: string;
  pincode: string;
  
  // Step 4: Document Upload
  idProof: File | null;
  landOwnershipProof: File | null;
  
  // Step 5: Bank Details
  bankName: string;
  accountNumber: string;
  ifscCode: string;
  accountHolderName: string;
}

const initialFormData: RegistrationFormData = {
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  confirmPassword: '',
  phone: '',
  whatsapp: '',
  streetAddress: '',
  city: '',
  state: '',
  pincode: '',
  idProof: null,
  landOwnershipProof: null,
  bankName: '',
  accountNumber: '',
  ifscCode: '',
  accountHolderName: '',
};

const steps = [
  { title: 'Personal Info', icon: User },
  { title: 'Contact', icon: Phone },
  { title: 'Address', icon: MapPin },
  { title: 'Documents', icon: Upload },
  { title: 'Bank Details', icon: Wallet },
];

export const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<RegistrationFormData>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [registrationError, setRegistrationError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState({
    password: false,
    confirmPassword: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setFormData(prev => ({ ...prev, [name]: files[0] }));
      
      if (errors[name]) {
        setErrors(prev => ({ ...prev, [name]: '' }));
      }
    }
  };

  const validateStep = (step: number): boolean => {
    let rules: Record<string, any> = {};
    
    switch (step) {
      case 1:
        rules = {
          firstName: [{ required: true }],
          lastName: [{ required: true }],
          email: [{ required: true }, { custom: validators.email }],
          password: [{ required: true }, { minLength: 8 }],
          confirmPassword: [
            { required: true },
            { custom: (value: string) => value === formData.password || 'Passwords do not match' }
          ],
        };
        break;
        
      case 2:
        rules = {
          phone: [{ required: true }, { custom: validators.phone }],
          whatsapp: [{ custom: validators.phone }],
        };
        break;
        
      case 3:
        rules = {
          streetAddress: [{ required: true }],
          city: [{ required: true }],
          state: [{ required: true }],
          pincode: [{ required: true }, { custom: validators.pincode }],
        };
        break;
        
      case 4:
        rules = {
          idProof: [{ required: true }],
          landOwnershipProof: [{ required: true }],
        };
        break;
        
      case 5:
        rules = {
          bankName: [{ required: true }],
          accountNumber: [{ required: true }, { custom: agriculturalValidators.bankAccount }],
          ifscCode: [{ required: true }, { custom: agriculturalValidators.ifscCode }],
          accountHolderName: [{ required: true }],
        };
        break;
    }

    const { isValid, errors: validationErrors } = validateForm(formData, rules);
    setErrors(validationErrors);
    return isValid;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setRegistrationError('');

    if (!validateStep(currentStep)) {
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual API call to register user
      await new Promise(resolve => setTimeout(resolve, 2000));

      const mockTokens = {
        token: 'mock_access_token_' + Date.now(),
        refreshToken: 'mock_refresh_token_' + Date.now(),
      };

      await login(mockTokens);
      navigate('/dashboard');
    } catch (error) {
      setRegistrationError(
        error instanceof Error 
          ? error.message 
          : 'Registration failed. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = (field: 'password' | 'confirmPassword') => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6 animate-fadeIn">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <Input
                id="register-firstName"
                name="firstName"
                label="First Name"
                value={formData.firstName}
                onChange={handleInputChange}
                error={errors.firstName}
                required
                placeholder="Enter your first name"
                fullWidth
              />
              <Input
                id="register-lastName"
                name="lastName"
                label="Last Name"
                value={formData.lastName}
                onChange={handleInputChange}
                error={errors.lastName}
                required
                placeholder="Enter your last name"
                fullWidth
              />
            </div>
            <Input
              id="register-email"
              name="email"
              type="email"
              label="Email Address"
              value={formData.email}
              onChange={handleInputChange}
              error={errors.email}
              required
              placeholder="Enter your email"
              fullWidth
            />
            <Input
              id="register-password"
              name="password"
              type={showPassword.password ? 'text' : 'password'}
              label="Password"
              value={formData.password}
              onChange={handleInputChange}
              error={errors.password}
              required
              placeholder="Choose a strong password"
              fullWidth
              endIcon={showPassword.password ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              clickableEndIcon
              onEndIconClick={() => togglePasswordVisibility('password')}
            />
            <Input
              id="register-confirmPassword"
              name="confirmPassword"
              type={showPassword.confirmPassword ? 'text' : 'password'}
              label="Confirm Password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              error={errors.confirmPassword}
              required
              placeholder="Confirm your password"
              fullWidth
              endIcon={showPassword.confirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              clickableEndIcon
              onEndIconClick={() => togglePasswordVisibility('confirmPassword')}
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-6 animate-fadeIn">
            <Input
              id="register-phone"
              name="phone"
              label="Phone Number"
              value={formData.phone}
              onChange={handleInputChange}
              error={errors.phone}
              required
              placeholder="Enter your phone number"
              fullWidth
            />
            <Input
              id="register-whatsapp"
              name="whatsapp"
              label="WhatsApp Number (Optional)"
              value={formData.whatsapp}
              onChange={handleInputChange}
              error={errors.whatsapp}
              placeholder="Enter your WhatsApp number"
              fullWidth
            />
          </div>
        );

      case 3:
        return (
          <div className="space-y-6 animate-fadeIn">
            <Input
              id="register-streetAddress"
              name="streetAddress"
              label="Street Address"
              value={formData.streetAddress}
              onChange={handleInputChange}
              error={errors.streetAddress}
              required
              placeholder="Enter your street address"
              fullWidth
            />
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <Input
                id="register-city"
                name="city"
                label="City"
                value={formData.city}
                onChange={handleInputChange}
                error={errors.city}
                required
                placeholder="Enter your city"
                fullWidth
              />
              <Input
                id="register-state"
                name="state"
                label="State"
                value={formData.state}
                onChange={handleInputChange}
                error={errors.state}
                required
                placeholder="Enter your state"
                fullWidth
              />
            </div>
            <Input
              id="register-pincode"
              name="pincode"
              label="PIN Code"
              value={formData.pincode}
              onChange={handleInputChange}
              error={errors.pincode}
              required
              placeholder="Enter your PIN code"
              fullWidth
            />
          </div>
        );

      case 4:
        return (
          <div className="space-y-6 animate-fadeIn">
            <div className="rounded-xl border-2 border-dashed border-gray-300 p-6 hover:border-green-500 transition-colors duration-200">
              <input
                type="file"
                name="idProof"
                onChange={handleFileChange}
                className="hidden"
                id="register-idProof"
              />
              <label
                htmlFor="register-idProof"
                className="flex flex-col items-center justify-center cursor-pointer space-y-3"
              >
                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                  <Upload className="h-6 w-6 text-gray-600" />
                </div>
                <div className="text-center">
                  <span className="text-sm font-medium text-gray-900 block">Upload ID Proof</span>
                  <span className="text-xs text-gray-500 block mt-1">Click to upload or drag and drop</span>
                </div>
              </label>
              {formData.idProof && (
                <p className="mt-3 text-sm text-green-600 text-center">{formData.idProof.name}</p>
              )}
              {errors.idProof && (
                <p className="mt-2 text-sm text-red-600 text-center">{errors.idProof}</p>
              )}
            </div>

            <div className="rounded-xl border-2 border-dashed border-gray-300 p-6 hover:border-green-500 transition-colors duration-200">
              <input
                type="file"
                name="landOwnershipProof"
                onChange={handleFileChange}
                className="hidden"
                id="register-landOwnershipProof"
              />
              <label
                htmlFor="register-landOwnershipProof"
                className="flex flex-col items-center justify-center cursor-pointer space-y-3"
              >
                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                  <Upload className="h-6 w-6 text-gray-600" />
                </div>
                <div className="text-center">
                  <span className="text-sm font-medium text-gray-900 block">Upload Land Ownership Proof</span>
                  <span className="text-xs text-gray-500 block mt-1">Click to upload or drag and drop</span>
                </div>
              </label>
              {formData.landOwnershipProof && (
                <p className="mt-3 text-sm text-green-600 text-center">{formData.landOwnershipProof.name}</p>
              )}
              {errors.landOwnershipProof && (
                <p className="mt-2 text-sm text-red-600 text-center">{errors.landOwnershipProof}</p>
              )}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6 animate-fadeIn">
            <Input
              id="register-bankName"
              name="bankName"
              label="Bank Name"
              value={formData.bankName}
              onChange={handleInputChange}
              error={errors.bankName}
              required
              placeholder="Enter your bank name"
              fullWidth
            />
            <Input
              id="register-accountNumber"
              name="accountNumber"
              label="Account Number"
              value={formData.accountNumber}
              onChange={handleInputChange}
              error={errors.accountNumber}
              required
              placeholder="Enter your account number"
              fullWidth
            />
            <Input
              id="register-ifscCode"
              name="ifscCode"
              label="IFSC Code"
              value={formData.ifscCode}
              onChange={handleInputChange}
              error={errors.ifscCode}
              required
              placeholder="Enter IFSC code"
              fullWidth
            />
            <Input
              id="register-accountHolderName"
              name="accountHolderName"
              label="Account Holder Name"
              value={formData.accountHolderName}
              onChange={handleInputChange}
              error={errors.accountHolderName}
              required
              placeholder="Enter account holder name"
              fullWidth
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left Panel - Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 py-12 bg-white relative">
        <div className="sm:mx-auto sm:w-full sm:max-w-md space-y-8">
          {/* Logo and Header */}
          <div className="text-center space-y-6">
            <div 
              className="mx-auto h-20 w-20 bg-gradient-to-br from-green-500 to-orange-500 rounded-3xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300 cursor-pointer"
              onClick={() => navigate('/')}
            >
              <Leaf className="h-10 w-10 text-white" strokeWidth={1.5} />
            </div>
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl font-display">
                Join BeFarma
              </h2>
              <p className="mt-3 text-lg text-gray-600">
                Create your account to start your agricultural journey
              </p>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="relative pt-8 pb-12">
            <div className="absolute top-12 w-full h-0.5 bg-gray-200">
              <div 
                className="h-full bg-green-500 transition-all duration-300"
                style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
              />
            </div>
            <div className="relative flex justify-between">
              {steps.map((step, index) => {
                const StepIcon = step.icon;
                return (
                  <div
                    key={step.title}
                    className={`flex flex-col items-center ${
                      index + 1 <= currentStep ? 'text-green-600' : 'text-gray-400'
                    }`}
                  >
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        index + 1 <= currentStep
                          ? 'bg-green-100 text-green-600'
                          : 'bg-gray-100 text-gray-400'
                      } transition-colors duration-200`}
                    >
                      <StepIcon className="h-5 w-5" />
                    </div>
                    <span className="mt-3 text-xs font-medium hidden sm:block">
                      {step.title}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Form */}
          <form className="mt-8 space-y-8" onSubmit={handleSubmit}>
            {registrationError && (
              <Alert
                variant="error"
                message={registrationError}
                className="animate-shake"
              />
            )}

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              {renderStepContent()}
            </div>

            <div className="flex justify-between space-x-4 pt-4">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={handleBack}
                  className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
                >
                  <ChevronLeft className="h-5 w-5 mr-1" />
                  Back
                </button>
              )}
              
              {currentStep < steps.length ? (
                <button
                  type="button"
                  onClick={handleNext}
                  className="flex-1 flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 rounded-xl transition-colors duration-200"
                >
                  Next Step
                  <ChevronRight className="h-5 w-5 ml-1" />
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 rounded-xl transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <Loader2 className="animate-spin h-5 w-5" />
                  ) : (
                    'Complete Registration'
                  )}
                </button>
              )}
            </div>

            {/* Sign In Link */}
            <div className="text-center pt-4">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <Link
                  to="/auth/login"
                  className="font-medium text-green-600 hover:text-green-500 transition-colors duration-200"
                >
                  Sign in instead
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Right Panel - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/90 to-orange-500/90 mix-blend-multiply" />
          <img
            className="h-full w-full object-cover"
            src="https://images.pexels.com/photos/2132171/pexels-photo-2132171.jpeg"
            alt="Agricultural field at sunset"
          />
          <div className="absolute inset-0 bg-black/20" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 p-12">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Sprout className="h-8 w-8 text-white" />
              <h3 className="text-2xl font-bold text-white">BeFarma</h3>
            </div>
            <p className="text-lg text-white/90">
              Join our community of farmers and embrace modern agricultural practices.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage; 