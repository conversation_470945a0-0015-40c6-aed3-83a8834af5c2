import React from 'react';
import { CreditCard, DollarSign, TrendingUp, PiggyBank } from 'lucide-react';

const FinancialPage: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="bg-green-100 p-3 rounded-full">
            <CreditCard className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Financial Management</h1>
            <p className="text-gray-600">Manage payments, earnings, and financial records</p>
          </div>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="bg-white shadow rounded-lg p-12 text-center">
        <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Financial Dashboard Coming Soon</h2>
        <p className="text-gray-600 mb-6">
          Complete financial management tools including payment tracking, 
          earnings reports, and expense management.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
          <div className="p-4 bg-gray-50 rounded-lg">
            <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Payment Tracking</h3>
            <p className="text-sm text-gray-600">Monitor incoming payments</p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Revenue Reports</h3>
            <p className="text-sm text-gray-600">Detailed earnings analysis</p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <PiggyBank className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900">Expense Management</h3>
            <p className="text-sm text-gray-600">Track farming costs</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialPage;
