import React from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@befarmer-platform/shared-utils';
import { Sprout, Cloud, IndianRupee, Map, CheckCircle2, Circle } from 'lucide-react';

// Simple Card components since shared-ui might not be available
const Card: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <div className={`bg-white shadow rounded-lg ${className}`}>
    {children}
  </div>
);

const StatsCard: React.FC<{
  title: string;
  value: string;
  icon: React.ReactNode;
  trend: string;
  color: string;
}> = ({ title, value, icon, trend, color }) => (
  <Card className="p-6">
    <div className="flex items-center">
      <div className={`p-2 rounded-lg bg-${color}-100 text-${color}-600`}>
        {icon}
      </div>
      <div className="ml-4">
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-2xl font-semibold text-gray-900">{value}</p>
        <p className="text-sm text-gray-500">{trend}</p>
      </div>
    </div>
  </Card>
);

const DashboardGrid: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {children}
  </div>
);

export const DashboardPage: React.FC = () => {
  const user = useSelector(selectCurrentUser);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-10 space-y-1">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900">
            Welcome back, {user?.name || 'Seller'}!
          </h1>
          <p className="text-lg text-gray-600">
            Manage your farm, monitor crops, and track your agricultural business
          </p>
          {user?.verificationStatus === 'pending' && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Account Verification Pending:</strong> Please complete your KYC verification to access all features.
              </p>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        <DashboardGrid>
          <StatsCard
            title="Active Crops"
            value="0"
            icon={<Sprout className="h-6 w-6" />}
            trend="+0% from last month"
            color="green"
          />

          <StatsCard
            title="Weather"
            value="Sunny"
            icon={<Cloud className="h-6 w-6" />}
            trend="28°C current temperature"
            color="blue"
          />

          <StatsCard
            title="Revenue"
            value="₹0"
            icon={<IndianRupee className="h-6 w-6" />}
            trend="+₹0 from last month"
            color="orange"
          />

          <StatsCard
            title="Plots"
            value="0"
            icon={<Map className="h-6 w-6" />}
            trend="0 ha total area"
            color="purple"
          />
        </DashboardGrid>

        {/* Getting Started */}
        <div className="mt-10">
          <Card className="bg-white shadow-sm rounded-2xl overflow-hidden">
            <div className="p-6 sm:p-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="h-10 w-10 bg-gradient-to-br from-green-500 to-orange-500 rounded-xl flex items-center justify-center">
                  <Sprout className="h-6 w-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Getting Started
                </h2>
              </div>
              
              <div className="space-y-6">
                <div className="flex items-start group">
                  <div className="flex-shrink-0">
                    <CheckCircle2 className="h-6 w-6 text-green-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-base font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                      Create your account
                    </p>
                    <p className="mt-1 text-sm text-gray-600">
                      Welcome to BeFarma! Your agricultural journey begins here.
                    </p>
                  </div>
                </div>

                <div className="flex items-start group">
                  <div className="flex-shrink-0">
                    <Circle className="h-6 w-6 text-gray-300" />
                  </div>
                  <div className="ml-4">
                    <p className="text-base font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                      Register your farm
                    </p>
                    <p className="mt-1 text-sm text-gray-600">
                      Add your farm details, location, and upload necessary documentation.
                    </p>
                  </div>
                </div>

                <div className="flex items-start group">
                  <div className="flex-shrink-0">
                    <Circle className="h-6 w-6 text-gray-300" />
                  </div>
                  <div className="ml-4">
                    <p className="text-base font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                      Configure plots
                    </p>
                    <p className="mt-1 text-sm text-gray-600">
                      Set up your farming plots, define boundaries, and specify soil types.
                    </p>
                  </div>
                </div>

                <div className="flex items-start group">
                  <div className="flex-shrink-0">
                    <Circle className="h-6 w-6 text-gray-300" />
                  </div>
                  <div className="ml-4">
                    <p className="text-base font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                      Start planting
                    </p>
                    <p className="mt-1 text-sm text-gray-600">
                      Begin your farming journey by planning and managing your crops.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage; 