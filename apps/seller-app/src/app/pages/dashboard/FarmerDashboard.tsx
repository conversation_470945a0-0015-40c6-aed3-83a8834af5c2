import React from 'react';
import { 
  Layout, 
  DashboardGrid, 
  StatsCard, 
  StatusBadge, 
  DataTable, 
  <PERSON><PERSON>,
  Card
} from '@befarmer-platform/shared-ui';

// Mock data for demonstration
const farmerStats = [
  {
    title: 'Total Revenue',
    value: '₹2,45,000',
    trend: '+12%',
    trendDirection: 'up' as const,
    color: 'orange' as const,
    subtitle: 'This month',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
      </svg>
    )
  },
  {
    title: 'Active Plots',
    value: '24',
    trend: '+3',
    trendDirection: 'up' as const,
    color: 'green' as const,
    subtitle: 'Under cultivation',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clipRule="evenodd" />
      </svg>
    )
  },
  {
    title: 'Healthy Crops',
    value: '89%',
    trend: '+5%',
    trendDirection: 'up' as const,
    color: 'green' as const,
    subtitle: 'Crop health status',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    )
  },
  {
    title: 'Water Usage',
    value: '1,250L',
    trend: '-8%',
    trendDirection: 'down' as const,
    color: 'blue' as const,
    subtitle: 'Daily average',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415zM10 9a1 1 0 011 1v.01a1 1 0 11-2 0V10a1 1 0 011-1z" clipRule="evenodd" />
      </svg>
    )
  }
];

const cropData = [
  {
    id: 1,
    cropName: 'Tomatoes',
    plotNumber: 'P-001',
    plantingDate: '2024-01-15',
    expectedHarvest: '2024-04-15',
    healthStatus: 'healthy',
    growthStage: 'growing',
    yieldExpected: '2.5 tons'
  },
  {
    id: 2,
    cropName: 'Wheat',
    plotNumber: 'P-002',
    plantingDate: '2024-02-01',
    expectedHarvest: '2024-05-01',
    healthStatus: 'warning',
    growthStage: 'maturing',
    yieldExpected: '3.2 tons'
  },
  {
    id: 3,
    cropName: 'Rice',
    plotNumber: 'P-003',
    plantingDate: '2024-01-20',
    expectedHarvest: '2024-04-20',
    healthStatus: 'healthy',
    growthStage: 'ready',
    yieldExpected: '4.1 tons'
  },
  {
    id: 4,
    cropName: 'Corn',
    plotNumber: 'P-004',
    plantingDate: '2024-02-10',
    expectedHarvest: '2024-05-10',
    healthStatus: 'critical',
    growthStage: 'growing',
    yieldExpected: '1.8 tons'
  },
  {
    id: 5,
    cropName: 'Soybeans',
    plotNumber: 'P-005',
    plantingDate: '2024-01-25',
    expectedHarvest: '2024-04-25',
    healthStatus: 'healthy',
    growthStage: 'planting',
    yieldExpected: '2.0 tons'
  }
];


const FarmerDashboard: React.FC = () => {
  return (
    <Layout
      title="Farm Dashboard"
      subtitle="Overview of your farming operations"
      headerActions={
        <div className="flex space-x-3">
          <Button variant="secondary" size="small">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            Export Data
          </Button>
        </div>
      }
    >
      {/* Stats Cards Section */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold text-befarma-text mb-4">Farm Overview</h2>
        <DashboardGrid columns={4} gap="medium">
          {farmerStats.map((stat, index) => (
            <StatsCard key={index} {...stat} />
          ))}
        </DashboardGrid>
      </div>

      {/* Quick Actions Section */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold text-befarma-text mb-4">Quick Actions</h2>
        <DashboardGrid columns={3} gap="medium">
          <Card className="p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-100 text-green-600 p-3 rounded-lg">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-befarma-text">Plant New Crop</h3>
                <p className="text-sm text-befarma-neutral">Start a new planting cycle</p>
              </div>
            </div>
            <div className="mt-4">
              <Button variant="secondary" size="small" fullWidth>
                Get Started
              </Button>
            </div>
          </Card>

          <Card className="p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-100 text-blue-600 p-3 rounded-lg">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 1a1 1 0 000 2h6a1 1 0 100-2H7zm0 4a1 1 0 000 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-befarma-text">Schedule Irrigation</h3>
                <p className="text-sm text-befarma-neutral">Manage water schedules</p>
              </div>
            </div>
            <div className="mt-4">
              <Button variant="secondary" size="small" fullWidth>
                Schedule Now
              </Button>
            </div>
          </Card>

          <Card className="p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-orange-100 text-orange-600 p-3 rounded-lg">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-befarma-text">View Reports</h3>
                <p className="text-sm text-befarma-neutral">Analyze farm performance</p>
              </div>
            </div>
            <div className="mt-4">
              <Button variant="secondary" size="small" fullWidth>
                View Reports
              </Button>
            </div>
          </Card>
        </DashboardGrid>
      </div>

      {/* Weather and Alerts Section */}
      <DashboardGrid columns={2} gap="medium">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-befarma-text mb-4">Weather Forecast</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-befarma-neutral">Today</span>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">28°C</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-befarma-neutral">Tomorrow</span>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                </svg>
                <span className="text-sm font-medium">24°C</span>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-befarma-text mb-4">Recent Alerts</h3>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-befarma-warning rounded-full mt-2"></div>
              <div className="ml-3">
                <p className="text-sm text-befarma-text">Corn crop needs attention</p>
                <p className="text-xs text-befarma-neutral">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-befarma-success rounded-full mt-2"></div>
              <div className="ml-3">
                <p className="text-sm text-befarma-text">Rice crop ready for harvest</p>
                <p className="text-xs text-befarma-neutral">4 hours ago</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-befarma-info rounded-full mt-2"></div>
              <div className="ml-3">
                <p className="text-sm text-befarma-text">Irrigation scheduled completed</p>
                <p className="text-xs text-befarma-neutral">6 hours ago</p>
              </div>
            </div>
          </div>
        </Card>
      </DashboardGrid>
    </Layout>
  );
};

export default FarmerDashboard; 