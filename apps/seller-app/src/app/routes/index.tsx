import React from 'react';
import { RouteObject, Navigate } from 'react-router-dom';
import { ProtectedRoute, GuestRoute } from '@befarmer-platform/shared-utils';
import MainLayout from '../components/layout/MainLayout';

// Lazy load pages
const DashboardPage = React.lazy(() => import('../pages/dashboard/DashboardPage'));
const LoginPage = React.lazy(() => import('../pages/auth/LoginPage'));
const RegisterPage = React.lazy(() => import('../pages/auth/RegisterPage'));
const ForgotPasswordPage = React.lazy(() => import('../pages/auth/ForgotPasswordPage'));
const ResetPasswordPage = React.lazy(() => import('../pages/auth/ResetPasswordPage'));

// Service Integration Test
const ServiceIntegrationTest = React.lazy(() => import('../components/ServiceIntegrationTest'));

// Agricultural modules
const ProfilePage = React.lazy(() => import('../pages/profile/ProfilePage'));
const KYCPage = React.lazy(() => import('../pages/kyc/KYCPage'));
const FarmsPage = React.lazy(() => import('../pages/farms/FarmsPage'));
const CropsPage = React.lazy(() => import('../pages/crops/CropsPage'));
const CropViewPage = React.lazy(() => import('../pages/crops/CropViewPage'));
const OrdersPage = React.lazy(() => import('../pages/orders/OrdersPage'));
const AnalyticsPage = React.lazy(() => import('../pages/analytics/AnalyticsPage'));
const FinancialPage = React.lazy(() => import('../pages/financial/FinancialPage'));
const ReportsPage = React.lazy(() => import('../pages/reports/ReportsPage'));
const NotificationsPage = React.lazy(() => import('../pages/notifications/NotificationsPage'));
const SettingsPage = React.lazy(() => import('../pages/settings/SettingsPage'));

// Create a simple UnauthorizedPage component if it doesn't exist
const UnauthorizedPage = React.lazy(() =>
  Promise.resolve({
    default: () => (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Unauthorized</h1>
          <p className="text-gray-600 mb-4">You don't have permission to access this page.</p>
          <button
            onClick={() => window.history.back()}
            className="btn-primary"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  })
);

export const routes: RouteObject[] = [
  // Service Integration Test (public for testing)
  {
    path: '/test-services',
    element: <ServiceIntegrationTest />,
  },

  // Auth routes (public)
  {
    path: '/auth/login',
    element: (
      <GuestRoute>
        <LoginPage />
      </GuestRoute>
    ),
  },
  {
    path: '/auth/register',
    element: (
      <GuestRoute>
        <RegisterPage />
      </GuestRoute>
    ),
  },
  {
    path: '/auth/forgot-password',
    element: (
      <GuestRoute>
        <ForgotPasswordPage />
      </GuestRoute>
    ),
  },
  {
    path: '/auth/reset-password',
    element: (
      <GuestRoute>
        <ResetPasswordPage />
      </GuestRoute>
    ),
  },

  // Protected routes with MainLayout
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <DashboardPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/profile',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <ProfilePage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/kyc',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <KYCPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/farms',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <FarmsPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/crops',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <CropsPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/crops/:cropId',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <CropViewPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/orders',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <OrdersPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/analytics',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <AnalyticsPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/financial',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <FinancialPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/reports',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <ReportsPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/notifications',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <NotificationsPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/settings',
    element: (
      <ProtectedRoute>
        <MainLayout>
          <SettingsPage />
        </MainLayout>
      </ProtectedRoute>
    ),
  },

  // Error pages
  {
    path: '/unauthorized',
    element: <UnauthorizedPage />,
  },

  // Root redirect
  {
    path: '/',
    element: <Navigate to="/dashboard" replace />,
  },

  // Catch all route - redirect to login for unauthenticated, dashboard for authenticated
  {
    path: '*',
    element: <Navigate to="/auth/login" replace />,
  },
];