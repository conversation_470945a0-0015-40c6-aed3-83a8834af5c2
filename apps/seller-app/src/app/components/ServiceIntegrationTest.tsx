import React, { useState, useEffect } from 'react';
import { 
  sellerService, 
  farmService, 
  cropService, 
  orderService,
  analyticsService,
  notificationService 
} from '@befarmer-platform/shared-utils';

interface ServiceStatus {
  name: string;
  status: 'testing' | 'success' | 'error';
  message: string;
  baseUrl: string;
}

export const ServiceIntegrationTest: React.FC = () => {
  const [services, setServices] = useState<ServiceStatus[]>([
    { name: 'Seller Service', status: 'testing', message: 'Initializing...', baseUrl: 'http://localhost:3001/api/v1/sellers' },
    { name: 'Farm Service', status: 'testing', message: 'Initializing...', baseUrl: 'http://localhost:3005/api/farms' },
    { name: 'Crop Service', status: 'testing', message: 'Initializing...', baseUrl: 'http://localhost:3004/api/crops' },
    { name: 'Order Service', status: 'testing', message: 'Initializing...', baseUrl: 'http://localhost:3009/api/orders' },
    { name: 'Analytics Service', status: 'testing', message: 'Initializing...', baseUrl: 'http://localhost:3003/api/analytics' },
    { name: 'Notification Service', status: 'testing', message: 'Initializing...', baseUrl: 'http://localhost:3008/api/notifications' },
  ]);

  const updateServiceStatus = (name: string, status: 'success' | 'error', message: string) => {
    setServices(prev => prev.map(service => 
      service.name === name ? { ...service, status, message } : service
    ));
  };

  const testServices = async () => {
    // Test Seller Service
    try {
      // Test the profile endpoint (this will likely fail without auth, but we can check the error)
      await sellerService.getSellerProfile();
      updateServiceStatus('Seller Service', 'success', 'Profile endpoint accessible');
    } catch (error: any) {
      const message = error.message || 'Unknown error';
      if (message.includes('401') || message.includes('unauthorized')) {
        updateServiceStatus('Seller Service', 'success', 'Service accessible (auth required)');
      } else if (message.includes('Network Error') || message.includes('ECONNREFUSED')) {
        updateServiceStatus('Seller Service', 'error', 'Service not running');
      } else {
        updateServiceStatus('Seller Service', 'success', `Service responding: ${message}`);
      }
    }

    // Test Farm Service
    try {
      await farmService.getFarms();
      updateServiceStatus('Farm Service', 'success', 'Farms endpoint accessible');
    } catch (error: any) {
      const message = error.message || 'Unknown error';
      if (message.includes('401') || message.includes('unauthorized')) {
        updateServiceStatus('Farm Service', 'success', 'Service accessible (auth required)');
      } else if (message.includes('Network Error') || message.includes('ECONNREFUSED')) {
        updateServiceStatus('Farm Service', 'error', 'Service not running');
      } else {
        updateServiceStatus('Farm Service', 'success', `Service responding: ${message}`);
      }
    }

    // Test Crop Service
    try {
      await cropService.getCrops();
      updateServiceStatus('Crop Service', 'success', 'Crops endpoint accessible');
    } catch (error: any) {
      const message = error.message || 'Unknown error';
      if (message.includes('401') || message.includes('unauthorized')) {
        updateServiceStatus('Crop Service', 'success', 'Service accessible (auth required)');
      } else if (message.includes('Network Error') || message.includes('ECONNREFUSED')) {
        updateServiceStatus('Crop Service', 'error', 'Service not running');
      } else {
        updateServiceStatus('Crop Service', 'success', `Service responding: ${message}`);
      }
    }

    // Test Order Service
    try {
      await orderService.getOrders();
      updateServiceStatus('Order Service', 'success', 'Orders endpoint accessible');
    } catch (error: any) {
      const message = error.message || 'Unknown error';
      if (message.includes('401') || message.includes('unauthorized')) {
        updateServiceStatus('Order Service', 'success', 'Service accessible (auth required)');
      } else if (message.includes('Network Error') || message.includes('ECONNREFUSED')) {
        updateServiceStatus('Order Service', 'error', 'Service not running');
      } else {
        updateServiceStatus('Order Service', 'success', `Service responding: ${message}`);
      }
    }

    // Test Analytics Service
    try {
      await analyticsService.getDashboardAnalytics();
      updateServiceStatus('Analytics Service', 'success', 'Dashboard endpoint accessible');
    } catch (error: any) {
      const message = error.message || 'Unknown error';
      if (message.includes('401') || message.includes('unauthorized')) {
        updateServiceStatus('Analytics Service', 'success', 'Service accessible (auth required)');
      } else if (message.includes('Network Error') || message.includes('ECONNREFUSED')) {
        updateServiceStatus('Analytics Service', 'error', 'Service not running');
      } else {
        updateServiceStatus('Analytics Service', 'success', `Service responding: ${message}`);
      }
    }

    // Test Notification Service
    try {
      await notificationService.getNotifications();
      updateServiceStatus('Notification Service', 'success', 'Notifications endpoint accessible');
    } catch (error: any) {
      const message = error.message || 'Unknown error';
      if (message.includes('401') || message.includes('unauthorized')) {
        updateServiceStatus('Notification Service', 'success', 'Service accessible (auth required)');
      } else if (message.includes('Network Error') || message.includes('ECONNREFUSED')) {
        updateServiceStatus('Notification Service', 'error', 'Service not running');
      } else {
        updateServiceStatus('Notification Service', 'success', `Service responding: ${message}`);
      }
    }
  };

  useEffect(() => {
    testServices();
  }, []);

  const getStatusColor = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'testing': return 'text-yellow-600';
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'testing': return '⏳';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Service Integration Test</h2>
      <p className="text-gray-600 mb-6">
        Testing updated microservice endpoints based on postman collections
      </p>
      
      <div className="space-y-4">
        {services.map((service) => (
          <div key={service.name} className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-lg">{service.name}</h3>
              <span className={`text-2xl ${getStatusColor(service.status)}`}>
                {getStatusIcon(service.status)}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              <strong>Base URL:</strong> {service.baseUrl}
            </p>
            <p className={`text-sm ${getStatusColor(service.status)}`}>
              <strong>Status:</strong> {service.message}
            </p>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Integration Summary</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>✅ Updated all service base URLs to match postman collections</li>
          <li>✅ Removed API gateway routing for direct microservice access</li>
          <li>✅ Updated endpoint paths to match postman specifications</li>
          <li>✅ Added new endpoints from postman collections (KYC, bank details, etc.)</li>
          <li>✅ Maintained backward compatibility with legacy methods</li>
        </ul>
      </div>

      <button 
        onClick={testServices}
        className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Retest Services
      </button>
    </div>
  );
};

export default ServiceIntegrationTest;
