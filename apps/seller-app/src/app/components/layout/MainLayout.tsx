import React from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Layout } from '@befarmer-platform/shared-ui';
import { 
  selectCurrentUser, 
  useAppDispatch, 
  logoutUser, 
  clearCredentials 
} from '@befarmer-platform/shared-utils';
import { 
  Home, 
  User, 
  Tractor, 
  Sprout, 
  Map, 
  ShoppingBag, 
  BarChart3, 
  Bell, 
  Settings,
  FileText,
  CreditCard,
  Shield
} from 'lucide-react';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const user = useSelector(selectCurrentUser);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Navigation items for agricultural seller platform
  const navigation = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: <Home className="w-5 h-5" />
    },
    {
      name: 'Profile',
      path: '/profile',
      icon: <User className="w-5 h-5" />
    },
    {
      name: 'KYC & Documents',
      path: '/kyc',
      icon: <Shield className="w-5 h-5" />
    },
    {
      name: 'Farm Management',
      path: '/farms',
      icon: <Tractor className="w-5 h-5" />
    },
    {
      name: 'Crop Management',
      path: '/crops',
      icon: <Sprout className="w-5 h-5" />
    },
    {
      name: 'Orders',
      path: '/orders',
      icon: <ShoppingBag className="w-5 h-5" />
    },
    {
      name: 'Analytics',
      path: '/analytics',
      icon: <BarChart3 className="w-5 h-5" />
    },
    {
      name: 'Financial',
      path: '/financial',
      icon: <CreditCard className="w-5 h-5" />
    },
    {
      name: 'Reports',
      path: '/reports',
      icon: <FileText className="w-5 h-5" />
    },
    {
      name: 'Notifications',
      path: '/notifications',
      icon: <Bell className="w-5 h-5" />
    },
    {
      name: 'Settings',
      path: '/settings',
      icon: <Settings className="w-5 h-5" />
    }
  ];

  // Mock notifications - in real app, this would come from Redux state
  const notifications = [
    {
      id: '1',
      title: 'Crop Ready for Harvest',
      message: 'Your tomato crop is ready for harvest.',
      time: '2 hours ago',
      read: false
    },
    {
      id: '2',
      title: 'Weather Alert',
      message: 'Heavy rain expected in your area tomorrow.',
      time: '5 hours ago',
      read: false
    },
    {
      id: '3',
      title: 'Order Received',
      message: 'New order for 50kg potatoes received.',
      time: '1 day ago',
      read: true
    }
  ];

  const handleLogout = async () => {
    try {
      // Dispatch logout action
      await dispatch(logoutUser()).unwrap();
      
      // Clear credentials
      dispatch(clearCredentials());
      
      // Navigate to login
      navigate('/auth/login', { replace: true });
    } catch (error) {
      console.error('Logout failed:', error);
      // Force logout even if API call fails
      dispatch(clearCredentials());
      navigate('/auth/login', { replace: true });
    }
  };

  return (
    <Layout
      navigation={navigation}
      userEmail={user?.email}
      userRole={user?.role}
      onLogout={handleLogout}
      notifications={notifications}
      showSidebar={true}
    >
      {children}
    </Layout>
  );
};

export default MainLayout;
