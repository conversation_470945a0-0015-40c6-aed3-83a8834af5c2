import React from 'react';
import {
  sellerServiceClient,
  farmServiceClient,
  cropServiceClient,
  orderServiceClient,
  analyticsServiceClient,
  notificationServiceClient,
  environment
} from '@befarmer-platform/shared-utils';

/**
 * Enhanced debug component to show which API endpoints are being used
 * This helps verify that all services are using the correct axios instances
 */
const ApiDebugInfo: React.FC = () => {
  // ✅ UPDATED: Expected base URLs matching postman collections exactly
  const expectedBaseURLs = {
    'Seller Service': 'http://localhost:3001/api/v1/sellers',
    'Farm Service': 'http://localhost:3005/api/farms',
    'Crop Service': 'http://localhost:3004/api/crops',
    'Order Service': 'http://localhost:3009/api/orders',
    'Analytics Service': 'http://localhost:3003/api/analytics',
    'Notification Service': 'http://localhost:3008/api/notifications',
  };

  const services = [
    { name: 'Seller Service', client: sellerServiceClient },
    { name: 'Farm Service', client: farmServiceClient },
    { name: 'Crop Service', client: cropServiceClient },
    { name: 'Order Service', client: orderServiceClient },
    { name: 'Analytics Service', client: analyticsServiceClient },
    { name: 'Notification Service', client: notificationServiceClient },
  ];

  const allCorrect = services.every(service =>
    service.client.defaults.baseURL === expectedBaseURLs[service.name]
  );

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <h3 className="text-sm font-medium text-blue-900 mb-3">🔧 API Configuration Debug</h3>
      <div className="text-xs text-blue-800 space-y-2">
        <div className="mb-2">
          <p><strong>Environment API URL:</strong> {environment.apiUrl}</p>
          <p><strong>Configuration:</strong> Direct Microservice Access (No API Gateway)</p>
        </div>

        <div className="space-y-1">
          {services.map((service) => {
            const expectedURL = expectedBaseURLs[service.name];
            const isCorrect = service.client.defaults.baseURL === expectedURL;
            return (
              <div key={service.name} className="space-y-1">
                <div className="flex items-center justify-between">
                  <span><strong>{service.name}:</strong></span>
                  <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                    isCorrect
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {isCorrect ? '✅' : '❌'}
                  </span>
                </div>
                <div className="text-xs pl-2">
                  <p><strong>Current:</strong> {service.client.defaults.baseURL}</p>
                  <p><strong>Expected:</strong> {expectedURL}</p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-3 pt-2 border-t border-blue-200">
          <p><strong>Overall Status:</strong>
            <span className={`ml-1 px-2 py-1 rounded text-xs font-medium ${
              allCorrect
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {allCorrect ? '✅ All services correctly configured for direct microservice access' : '❌ Some services have incorrect URLs'}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ApiDebugInfo;
