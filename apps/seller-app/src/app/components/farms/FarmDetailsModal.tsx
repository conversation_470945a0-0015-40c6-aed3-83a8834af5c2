import React from 'react';
import { X, MapPin, Droplets, Leaf, Calendar, Award, BarChart3 } from 'lucide-react';
import { Farm } from '@befarmer-platform/shared-utils';

interface FarmDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  farm: Farm | null;
}

const FarmDetailsModal: React.FC<FarmDetailsModalProps> = ({ isOpen, onClose, farm }) => {
  if (!isOpen || !farm) return null;

  const getSoilTypeLabel = (soilType: string) => {
    const soilTypeMap: Record<string, string> = {
      'LOAMY': 'Loamy Soil',
      'CLAY': 'Clay Soil',
      'SANDY': 'Sandy Soil',
      'CLAY_LOAM': 'Clay Loam',
      'SANDY_LOAM': 'Sandy Loam',
      'SILT': 'Silt Soil',
      'ROCKY': 'Rocky Soil',
    };
    return soilTypeMap[soilType] || soilType;
  };

  const getWaterSourceLabel = (waterSource: string) => {
    const waterSourceMap: Record<string, string> = {
      'BOREWELL': 'Borewell',
      'CANAL': 'Canal',
      'RIVER': 'River',
      'RAINWATER': 'Rainwater',
      'WELL': 'Well',
      'MULTIPLE': 'Multiple Sources',
    };
    return waterSourceMap[waterSource] || waterSource;
  };

  const formatLocation = (location: any) => {
    // Handle both old and new location structures
    if (location.village && location.district) {
      return `${location.village}, ${location.district}, ${location.state} - ${location.pincode}`;
    } else if (location.city) {
      return `${location.addressLine1 || location.city}, ${location.city}, ${location.state} - ${location.pincode}`;
    }
    return `${location.state} - ${location.pincode}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-green-100 p-2 rounded-lg">
              <Leaf className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{farm.name}</h2>
              <p className="text-sm text-gray-600">{formatLocation(farm.location)}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Farm Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center">
                <MapPin className="h-8 w-8 text-blue-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Farm Size</p>
                  <p className="text-2xl font-bold text-blue-900">{farm.totalArea || farm.size} acres</p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center">
                <Leaf className="h-8 w-8 text-green-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-900">Active Crops</p>
                  <p className="text-2xl font-bold text-green-900">{farm.currentCrops?.length || farm.crops?.length || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-purple-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-purple-900">Status</p>
                  <p className="text-lg font-bold text-purple-900 capitalize">{farm.status || farm.verificationStatus}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Farm Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Farm Information</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Soil Type:</span>
                  <span className="text-sm text-gray-900">{farm.soilType}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Water Source:</span>
                  <span className="text-sm text-gray-900">{farm.waterSource}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Farming Method:</span>
                  <span className="text-sm text-gray-900">{farm.farmingPractices?.primaryMethod || 'Conventional'}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Sustainability Score:</span>
                  <span className="text-sm text-gray-900">
                    {farm.farmingPractices?.sustainabilityScore || 'N/A'}
                    {farm.farmingPractices?.sustainabilityScore && '%'}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Registration Date:</span>
                  <span className="text-sm text-gray-900">
                    {new Date(farm.createdAt || farm.registrationDate).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Last Updated:</span>
                  <span className="text-sm text-gray-900">
                    {new Date(farm.updatedAt || farm.lastUpdated).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Location Details</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">State:</span>
                  <span className="text-sm text-gray-900">{farm.location.state}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">{farm.location.district ? 'District:' : 'City:'}</span>
                  <span className="text-sm text-gray-900">{farm.location.district || farm.location.city}</span>
                </div>

                {farm.location.village && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-600">Village:</span>
                    <span className="text-sm text-gray-900">{farm.location.village}</span>
                  </div>
                )}

                {farm.location.addressLine1 && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-600">Address:</span>
                    <span className="text-sm text-gray-900">{farm.location.addressLine1}</span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Pincode:</span>
                  <span className="text-sm text-gray-900">{farm.location.pincode}</span>
                </div>

                {farm.location.coordinates && (
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-600">Coordinates:</span>
                    <span className="text-sm text-gray-900">
                      {farm.location.coordinates.latitude.toFixed(4)}, {farm.location.coordinates.longitude.toFixed(4)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Infrastructure and Certifications */}
          {(farm.infrastructure?.length > 0 || farm.certifications?.length > 0) && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">Farm Infrastructure & Certifications</h3>

              {farm.infrastructure?.length > 0 && (
                <div className="mb-3">
                  <p className="text-sm font-medium text-blue-700 mb-2">Infrastructure:</p>
                  <div className="flex flex-wrap gap-2">
                    {farm.infrastructure.map((item, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                        {item}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {farm.certifications?.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-blue-700 mb-2">Certifications:</p>
                  <div className="flex flex-wrap gap-2">
                    {farm.certifications.map((cert, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                        {cert}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Irrigation Systems */}
          {farm.farmingPractices?.irrigationSystems?.length > 0 && (
            <div className="bg-cyan-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-cyan-900 mb-3">Irrigation Systems</h3>
              <div className="flex flex-wrap gap-2">
                {farm.farmingPractices.irrigationSystems.map((system, index) => (
                  <span key={index} className="px-3 py-1 bg-cyan-100 text-cyan-800 rounded-full text-sm">
                    {system}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Organic Certification Details */}
          {(farm.organicCertified || farm.farmingPractices?.primaryMethod === 'ORGANIC') && farm.certificationDetails && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-green-900 mb-3 flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Organic Certification
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-green-700">Certifying Body</p>
                  <p className="text-sm text-green-900">{farm.certificationDetails.certifyingBody}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-green-700">Certificate Number</p>
                  <p className="text-sm text-green-900">{farm.certificationDetails.certificateNumber}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-green-700">Valid Until</p>
                  <p className="text-sm text-green-900">
                    {new Date(farm.certificationDetails.validUntil).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Current Crops Information */}
          {(farm.currentCrops?.length > 0 || farm.crops?.length > 0) && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Leaf className="h-5 w-5 mr-2 text-green-600" />
                Active Crops ({farm.currentCrops?.length || farm.crops?.length || 0})
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(farm.currentCrops || farm.crops || []).map((crop, index) => (
                  <div key={crop._id || index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-gray-900">{crop.name}</h4>
                      {crop.area && <span className="text-sm text-gray-600">{crop.area} acres</span>}
                    </div>

                    <div className="space-y-1 text-sm text-gray-600">
                      <p><span className="font-medium">Variety:</span> {crop.variety}</p>
                      <p><span className="font-medium">Planted:</span> {new Date(crop.plantingDate).toLocaleDateString()}</p>
                      <p><span className="font-medium">Expected Harvest:</span> {new Date(crop.expectedHarvestDate || crop.expectedHarvest).toLocaleDateString()}</p>
                      {crop.season && (
                        <p><span className="font-medium">Season:</span> {crop.season}</p>
                      )}
                      {(crop.growthStage || crop.status) && (
                        <p><span className="font-medium">Status:</span>
                          <span className={`ml-1 px-2 py-1 rounded-full text-xs ${
                            (crop.growthStage === 'HARVESTED' || crop.status === 'HARVESTED') ? 'bg-green-100 text-green-800' :
                            (crop.growthStage === 'GROWING' || crop.status === 'GROWING') ? 'bg-blue-100 text-blue-800' :
                            (crop.growthStage === 'PLANTED' || crop.status === 'PLANTED') ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {crop.growthStage || crop.status}
                          </span>
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Crop Rotation Plan */}
          {farm.cropRotationPlan?.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                Crop Rotation Plan ({farm.cropRotationPlan.length})
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {farm.cropRotationPlan.map((plan, index) => (
                  <div key={plan._id || index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-gray-900">{plan.plannedCrop}</h4>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        plan.status === 'PLANTED' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {plan.status}
                      </span>
                    </div>

                    <div className="space-y-1 text-sm text-gray-600">
                      <p><span className="font-medium">Variety:</span> {plan.variety}</p>
                      <p><span className="font-medium">Season:</span> {plan.season}</p>
                      <p><span className="font-medium">Year:</span> {plan.year}</p>
                      <p><span className="font-medium">Number of Plots:</span> {plan.numberOfPlots}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}


        </div>

        <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default FarmDetailsModal;
