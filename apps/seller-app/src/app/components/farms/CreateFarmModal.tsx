import React, { useState } from 'react';
import { X, MapPin, Droplets, Leaf, Plus, Trash2 } from 'lucide-react';
import { useCreateFarmMutation } from '@befarmer-platform/shared-utils';

interface CreateFarmModalProps {
  isOpen: boolean;
  onClose: () => void;
  sellerId: string;
}

interface FarmFormData {
  name: string;
  location: {
    coordinates: {
      latitude: number;
      longitude: number;
    };
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2: string;
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  farmingPractices: {
    primaryMethod: 'INTEGRATED' | 'CONVENTIONAL' | 'ORGANIC';
    irrigationSystems: string[];
    sustainabilityScore: number;
  };
  currentCrops: Array<{
    name: string;
    variety: string;
    plantingDate: string;
    expectedHarvestDate: string;
    season: 'KHARIF' | 'RABI' | 'ZAID';
  }>;
}

const CreateFarmModal: React.FC<CreateFarmModalProps> = ({ isOpen, onClose, sellerId }) => {
  const [createFarm, { isLoading }] = useCreateFarmMutation();
  
  const [formData, setFormData] = useState<FarmFormData>({
    name: '',
    location: {
      coordinates: {
        latitude: 0,
        longitude: 0,
      },
      country: 'INDIA',
      state: '',
      city: '',
      pincode: '',
      addressLine1: '',
      addressLine2: '',
    },
    totalArea: 0,
    soilType: '',
    waterSource: '',
    infrastructure: [],
    certifications: [],
    farmingPractices: {
      primaryMethod: 'CONVENTIONAL',
      irrigationSystems: [],
      sustainabilityScore: 70,
    },
    currentCrops: [],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const soilTypeOptions = [
    { value: 'Alluvial', label: 'Alluvial Soil' },
    { value: 'Black Cotton', label: 'Black Cotton Soil' },
    { value: 'Red Soil', label: 'Red Soil' },
    { value: 'Laterite', label: 'Laterite Soil' },
    { value: 'Sandy', label: 'Sandy Soil' },
    { value: 'Clay', label: 'Clay Soil' },
    { value: 'Loamy', label: 'Loamy Soil' },
  ];

  const waterSourceOptions = [
    { value: 'Canal', label: 'Canal' },
    { value: 'Deep Bore Well', label: 'Deep Bore Well' },
    { value: 'Bore Well', label: 'Bore Well' },
    { value: 'River', label: 'River' },
    { value: 'Rainwater', label: 'Rainwater' },
    { value: 'Well', label: 'Well' },
    { value: 'Multiple Sources', label: 'Multiple Sources' },
  ];

  const farmingMethodOptions = [
    { value: 'CONVENTIONAL', label: 'Conventional' },
    { value: 'ORGANIC', label: 'Organic' },
    { value: 'INTEGRATED', label: 'Integrated' },
  ];

  const irrigationSystemOptions = [
    'Canal Irrigation',
    'Drip Irrigation',
    'Sprinkler Irrigation',
    'Bore Well',
    'Flood Irrigation',
    'Furrow Irrigation',
  ];

  const infrastructureOptions = [
    'Storage Shed',
    'Storage Facility',
    'Storage Warehouse',
    'Irrigation System',
    'Processing Unit',
    'Solar Pumps',
    'Cold Storage',
    'Greenhouse',
    'Machinery Shed',
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Farm name is required';
    }

    if (!formData.location.state.trim()) {
      newErrors.state = 'State is required';
    }

    if (!formData.location.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.location.addressLine1.trim()) {
      newErrors.addressLine1 = 'Address Line 1 is required';
    }

    if (!formData.location.addressLine2.trim()) {
      newErrors.addressLine2 = 'Address Line 2 is required';
    }

    if (!formData.location.pincode.trim()) {
      newErrors.pincode = 'Pincode is required';
    } else if (!/^\d{6}$/.test(formData.location.pincode)) {
      newErrors.pincode = 'Pincode must be 6 digits';
    }

    if (formData.totalArea <= 0) {
      newErrors.totalArea = 'Farm area must be greater than 0';
    }

    if (!formData.soilType.trim()) {
      newErrors.soilType = 'Soil type is required';
    }

    if (!formData.waterSource.trim()) {
      newErrors.waterSource = 'Water source is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const farmPayload = {
        ...formData,
        sellerId,
        status: 'ACTIVE',
      };

      await createFarm(farmPayload).unwrap();
      onClose();
      // Reset form
      setFormData({
        name: '',
        location: {
          coordinates: {
            latitude: 0,
            longitude: 0,
          },
          country: 'INDIA',
          state: '',
          city: '',
          pincode: '',
          addressLine1: '',
          addressLine2: '',
        },
        totalArea: 0,
        soilType: '',
        waterSource: '',
        infrastructure: [],
        certifications: [],
        farmingPractices: {
          primaryMethod: 'CONVENTIONAL',
          irrigationSystems: [],
          sustainabilityScore: 70,
        },
        currentCrops: [],
      });
      setErrors({});
    } catch (error) {
      console.error('Failed to create farm:', error);
    }
  };

  const addCrop = () => {
    setFormData(prev => ({
      ...prev,
      currentCrops: [
        ...prev.currentCrops,
        {
          name: '',
          variety: '',
          plantingDate: '',
          expectedHarvestDate: '',
          season: 'KHARIF',
        },
      ],
    }));
  };

  const removeCrop = (index: number) => {
    setFormData(prev => ({
      ...prev,
      currentCrops: prev.currentCrops.filter((_, i) => i !== index),
    }));
  };

  const updateCrop = (index: number, field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      currentCrops: prev.currentCrops.map((crop, i) =>
        i === index ? { ...crop, [field]: value } : crop
      ),
    }));
  };

  const toggleInfrastructure = (item: string) => {
    setFormData(prev => ({
      ...prev,
      infrastructure: prev.infrastructure.includes(item)
        ? prev.infrastructure.filter(i => i !== item)
        : [...prev.infrastructure, item]
    }));
  };

  const toggleIrrigationSystem = (system: string) => {
    setFormData(prev => ({
      ...prev,
      farmingPractices: {
        ...prev.farmingPractices,
        irrigationSystems: prev.farmingPractices.irrigationSystems.includes(system)
          ? prev.farmingPractices.irrigationSystems.filter(s => s !== system)
          : [...prev.farmingPractices.irrigationSystems, system]
      }
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Create New Farm</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Leaf className="h-5 w-5 text-green-600 mr-2" />
              Basic Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Farm Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter farm name"
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Farm Area (acres) *
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  value={formData.totalArea}
                  onChange={(e) => setFormData(prev => ({ ...prev, totalArea: parseFloat(e.target.value) || 0 }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.totalArea ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter farm area"
                />
                {errors.totalArea && <p className="text-red-500 text-sm mt-1">{errors.totalArea}</p>}
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <MapPin className="h-5 w-5 text-blue-600 mr-2" />
              Location Details
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  State *
                </label>
                <input
                  type="text"
                  value={formData.location.state}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    location: { ...prev.location, state: e.target.value }
                  }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.state ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter state"
                />
                {errors.state && <p className="text-red-500 text-sm mt-1">{errors.state}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City *
                </label>
                <input
                  type="text"
                  value={formData.location.city}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    location: { ...prev.location, city: e.target.value }
                  }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.city ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter city"
                />
                {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 1 *
                </label>
                <input
                  type="text"
                  value={formData.location.addressLine1}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    location: { ...prev.location, addressLine1: e.target.value }
                  }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.addressLine1 ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter address line 1"
                />
                {errors.addressLine1 && <p className="text-red-500 text-sm mt-1">{errors.addressLine1}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 2 *
                </label>
                <input
                  type="text"
                  value={formData.location.addressLine2}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    location: { ...prev.location, addressLine2: e.target.value }
                  }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.addressLine2 ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter address line 2"
                />
                {errors.addressLine2 && <p className="text-red-500 text-sm mt-1">{errors.addressLine2}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pincode *
                </label>
                <input
                  type="text"
                  value={formData.location.pincode}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    location: { ...prev.location, pincode: e.target.value }
                  }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.pincode ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter pincode"
                  maxLength={6}
                />
                {errors.pincode && <p className="text-red-500 text-sm mt-1">{errors.pincode}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Country
                </label>
                <input
                  type="text"
                  value={formData.location.country}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    location: { ...prev.location, country: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Country"
                />
              </div>
            </div>
          </div>

          {/* Farm Characteristics */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Droplets className="h-5 w-5 text-blue-600 mr-2" />
              Farm Characteristics
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Soil Type *
                </label>
                <select
                  value={formData.soilType}
                  onChange={(e) => setFormData(prev => ({ ...prev, soilType: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.soilType ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select soil type</option>
                  {soilTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.soilType && <p className="text-red-500 text-sm mt-1">{errors.soilType}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Water Source *
                </label>
                <select
                  value={formData.waterSource}
                  onChange={(e) => setFormData(prev => ({ ...prev, waterSource: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.waterSource ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select water source</option>
                  {waterSourceOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.waterSource && <p className="text-red-500 text-sm mt-1">{errors.waterSource}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Farming Method *
                </label>
                <select
                  value={formData.farmingPractices.primaryMethod}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    farmingPractices: {
                      ...prev.farmingPractices,
                      primaryMethod: e.target.value as any
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  {farmingMethodOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sustainability Score (0-100)
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={formData.farmingPractices.sustainabilityScore}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  farmingPractices: {
                    ...prev.farmingPractices,
                    sustainabilityScore: parseInt(e.target.value)
                  }
                }))}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-600 mt-1">
                <span>0</span>
                <span className="font-medium">{formData.farmingPractices.sustainabilityScore}</span>
                <span>100</span>
              </div>
            </div>
          </div>

          {/* Infrastructure */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Droplets className="h-5 w-5 text-blue-600 mr-2" />
              Farm Infrastructure
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {infrastructureOptions.map((item) => (
                <label key={item} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.infrastructure.includes(item)}
                    onChange={() => toggleInfrastructure(item)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{item}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Irrigation Systems */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Droplets className="h-5 w-5 text-cyan-600 mr-2" />
              Irrigation Systems
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {irrigationSystemOptions.map((system) => (
                <label key={system} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.farmingPractices.irrigationSystems.includes(system)}
                    onChange={() => toggleIrrigationSystem(system)}
                    className="h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{system}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Certifications */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Leaf className="h-5 w-5 text-green-600 mr-2" />
              Certifications
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {['Organic Certification', 'BT Cotton Certification', 'Fair Trade Certification', 'Rainforest Alliance', 'GlobalGAP'].map((cert) => (
                <label key={cert} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.certifications.includes(cert)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          certifications: [...prev.certifications, cert]
                        }));
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          certifications: prev.certifications.filter(c => c !== cert)
                        }));
                      }
                    }}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{cert}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Initial Crops */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Leaf className="h-5 w-5 text-green-600 mr-2" />
                Initial Crops (Optional)
              </h3>
              <button
                type="button"
                onClick={addCrop}
                className="flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Crop
              </button>
            </div>

            {formData.currentCrops.map((crop, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-md font-medium text-gray-800">Crop {index + 1}</h4>
                  <button
                    type="button"
                    onClick={() => removeCrop(index)}
                    className="text-red-500 hover:text-red-700 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Crop Name
                    </label>
                    <input
                      type="text"
                      value={crop.name}
                      onChange={(e) => updateCrop(index, 'name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="e.g., Basmati Rice"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Variety
                    </label>
                    <input
                      type="text"
                      value={crop.variety}
                      onChange={(e) => updateCrop(index, 'variety', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="e.g., Basmati 370"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Season
                    </label>
                    <select
                      value={crop.season}
                      onChange={(e) => updateCrop(index, 'season', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="KHARIF">Kharif</option>
                      <option value="RABI">Rabi</option>
                      <option value="ZAID">Zaid</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Planting Date
                    </label>
                    <input
                      type="date"
                      value={crop.plantingDate}
                      onChange={(e) => updateCrop(index, 'plantingDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expected Harvest Date
                    </label>
                    <input
                      type="date"
                      value={crop.expectedHarvestDate}
                      onChange={(e) => updateCrop(index, 'expectedHarvestDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isLoading ? 'Creating...' : 'Create Farm'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateFarmModal;
