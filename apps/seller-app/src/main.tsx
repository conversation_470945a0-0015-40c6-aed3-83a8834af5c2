import { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor, initializeAuth } from '@befarmer-platform/shared-utils';
import { setAxiosStore } from '@befarmer-platform/shared-utils';

import App from './app/App';
import './styles.css';

// Set up axios store reference for interceptors
setAxiosStore(store);

// Initialize auth state from localStorage
store.dispatch(initializeAuth());
const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <StrictMode>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <App />
      </PersistGate>
    </Provider>
  </StrictMode>
);
