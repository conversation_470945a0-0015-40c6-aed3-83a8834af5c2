# Seller App Environment Variables
# Development Configuration

# ✅ FIXED: Direct microservice URLs for direct service communication
# Each service connects directly to its respective microservice
REACT_APP_API_URL=http://localhost:3000

# ✅ UPDATED: Direct Microservice URLs matching postman collections exactly
REACT_APP_SELLER_SERVICE_URL=http://localhost:3001/api/v1/sellers
REACT_APP_ADMIN_SERVICE_URL=http://localhost:3002/api/v1
REACT_APP_ANALYTICS_SERVICE_URL=http://localhost:3003/api/analytics
REACT_APP_CROP_SERVICE_URL=http://localhost:3004/api/crops
REACT_APP_FARM_SERVICE_URL=http://localhost:3005/api/farms
REACT_APP_FINANCIAL_SERVICE_URL=http://localhost:3006/api/v1
REACT_APP_INVENTORY_SERVICE_URL=http://localhost:3007/api/v1
REACT_APP_NOTIFICATION_SERVICE_URL=http://localhost:3008/api/notifications
REACT_APP_ORDER_SERVICE_URL=http://localhost:3009/api/orders
REACT_APP_PLOT_SERVICE_URL=http://localhost:3010/api/v1

# App Configuration
REACT_APP_NAME=BeFarma Seller Portal
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_REAL_TIME_UPDATES=true

# Security
REACT_APP_JWT_EXPIRY=3600
REACT_APP_REFRESH_TOKEN_EXPIRY=86400
