{"name": "@befarmer-platform/seller-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/seller-app/src", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "apps/seller-app/dist"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "@befarmer-platform/seller-app:build"}, "configurations": {"development": {"buildTarget": "@befarmer-platform/seller-app:build:development", "hmr": true}, "production": {"buildTarget": "@befarmer-platform/seller-app:build:production", "hmr": false}}}, "lint": {"executor": "nx:run-commands", "options": {"cwd": "apps/seller-app", "command": "eslint ."}}, "preview": {"dependsOn": ["build"], "executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "@befarmer-platform/seller-app:build"}, "configurations": {"development": {"buildTarget": "@befarmer-platform/seller-app:build:development"}, "production": {"buildTarget": "@befarmer-platform/seller-app:build:production"}}}}}