/// <reference types='vitest' />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { join } from 'path';

export default defineConfig(({ mode }) => ({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/seller-app',
  server: {
    port: 4200,
    host: 'localhost',
  },
  preview: {
    port: 4300,
    host: 'localhost',
  },
  plugins: [react()],
  
  css: {
    postcss: {
      plugins: [
        require('tailwindcss')({
          config: join(__dirname, '../../tailwind.config.js'),
        }),
        require('autoprefixer'),
      ],
    },
  },
  
  // Define environment variables for the browser
  define: {
    // Define process.env for compatibility with existing code
    'process.env.NODE_ENV': JSON.stringify(mode === 'production' ? 'production' : 'development'),

    // ✅ FIXED: Direct microservice URLs without double JSON.stringify to prevent encoding issues
    'process.env.REACT_APP_API_URL': JSON.stringify(process.env.REACT_APP_API_URL || 'http://localhost:3000'),

    // ✅ UPDATED: Direct Microservice URLs matching postman collections exactly
    'process.env.REACT_APP_SELLER_SERVICE_URL': JSON.stringify(process.env.REACT_APP_SELLER_SERVICE_URL || 'http://localhost:3001/api/v1/sellers'),
    'process.env.REACT_APP_ADMIN_SERVICE_URL': JSON.stringify(process.env.REACT_APP_ADMIN_SERVICE_URL || 'http://localhost:3002/api/v1'),
    'process.env.REACT_APP_ANALYTICS_SERVICE_URL': JSON.stringify(process.env.REACT_APP_ANALYTICS_SERVICE_URL || 'http://localhost:3003/api/analytics'),
    'process.env.REACT_APP_CROP_SERVICE_URL': JSON.stringify(process.env.REACT_APP_CROP_SERVICE_URL || 'http://localhost:3004/api/crops'),
    'process.env.REACT_APP_FARM_SERVICE_URL': JSON.stringify(process.env.REACT_APP_FARM_SERVICE_URL || 'http://localhost:3005/api/farms'),
    'process.env.REACT_APP_FINANCIAL_SERVICE_URL': JSON.stringify(process.env.REACT_APP_FINANCIAL_SERVICE_URL || 'http://localhost:3006/api/v1'),
    'process.env.REACT_APP_INVENTORY_SERVICE_URL': JSON.stringify(process.env.REACT_APP_INVENTORY_SERVICE_URL || 'http://localhost:3007/api/v1'),
    'process.env.REACT_APP_NOTIFICATION_SERVICE_URL': JSON.stringify(process.env.REACT_APP_NOTIFICATION_SERVICE_URL || 'http://localhost:3008/api/notifications'),
    'process.env.REACT_APP_ORDER_SERVICE_URL': JSON.stringify(process.env.REACT_APP_ORDER_SERVICE_URL || 'http://localhost:3009/api/orders'),
    'process.env.REACT_APP_PLOT_SERVICE_URL': JSON.stringify(process.env.REACT_APP_PLOT_SERVICE_URL || 'http://localhost:3010/api/v1'),

    // App Configuration
    'process.env.REACT_APP_NAME': JSON.stringify(process.env.REACT_APP_NAME || 'BeFarma Seller Portal'),
    'process.env.REACT_APP_VERSION': JSON.stringify(process.env.REACT_APP_VERSION || '1.0.0'),
    'process.env.REACT_APP_ENVIRONMENT': JSON.stringify(process.env.REACT_APP_ENVIRONMENT || 'development'),

    // Feature Flags
    'process.env.REACT_APP_ENABLE_ANALYTICS': JSON.stringify(process.env.REACT_APP_ENABLE_ANALYTICS || 'true'),
    'process.env.REACT_APP_ENABLE_NOTIFICATIONS': JSON.stringify(process.env.REACT_APP_ENABLE_NOTIFICATIONS || 'true'),
    'process.env.REACT_APP_ENABLE_REAL_TIME_UPDATES': JSON.stringify(process.env.REACT_APP_ENABLE_REAL_TIME_UPDATES || 'true'),
  },
  
  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [ nxViteTsPaths() ],
  // },
  build: {
    outDir: './dist',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
  test: {
    watch: false,
    globals: true,
    environment: 'jsdom',
    include: ['{src,tests}/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: './test-output/vitest/coverage',
      provider: 'v8' as const,
    },
  },
}));
