// Uncomment this line to use CSS modules
// import styles from './app.module.scss';
import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { Layout } from '@befarmer-platform/shared-ui';
import { useAuth, clearCredentials } from '@befarmer-platform/shared-utils';
import { Sprout, Users, LineChart, Settings, Bell, Leaf } from 'lucide-react';
import { useDispatch } from 'react-redux';

// Lazy load pages
const LoginPage = lazy(() => import('./pages/auth/LoginPage'));
const ForgotPasswordPage = lazy(() => import('./pages/auth/ForgotPasswordPage'));
const ResetPasswordPage = lazy(() => import('./pages/auth/ResetPasswordPage'));
const DashboardPage = lazy(() => import('./pages/dashboard/DashboardPage'));
const FarmsPage = lazy(() => import('./pages/farms/FarmsPage'));
const SellersPage = lazy(() => import('./pages/sellers/SellersPage'));
const EnhancedSellersPage = lazy(() => import('./pages/sellers/EnhancedSellersPage'));
const CreateSellerPage = lazy(() => import('./pages/sellers/CreateSellerPage'));
const CropsPage = lazy(() => import('./pages/crops/CropsPage'));
const EnhancedCropsPage = lazy(() => import('./pages/crops/EnhancedCropsPage'));
const CreateCropPage = lazy(() => import('./pages/crops/CreateCropPage'));
const CropDetailsPage = lazy(() => import('./pages/crops/CropDetailsPage'));
const AnalyticsPage = lazy(() => import('./pages/analytics/AnalyticsPage'));
const EnhancedAnalyticsPage = lazy(() => import('./pages/analytics/EnhancedAnalyticsPage'));
const SettingsPage = lazy(() => import('./pages/settings/SettingsPage'));

// Protected Route Component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? <>{children}</> : <Navigate to="/auth/login" replace />;
};

// Simple Auth Layout Component
const AuthLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {children}
    </div>
  );
};

// Navigation Configuration
const mainNavigation = [
  // { name: 'Dashboard', path: '/dashboard', icon: <LineChart className="w-5 h-5" /> },
  { name: 'Farmers', path: '/farmers-enhanced', icon: <Users className="w-5 h-5" /> },
  { name: 'Farms', path: '/farms', icon: <Sprout className="w-5 h-5" /> },
  { name: 'Crops', path: '/crops-enhanced', icon: <Leaf className="w-5 h-5" /> },
  { name: 'Analytics', path: '/analytics-enhanced', icon: <LineChart className="w-5 h-5" /> },
  { name: 'Settings', path: '/settings', icon: <Settings className="w-5 h-5" /> },
];

export function App() {
  const { isAuthenticated, user, logout } = useAuth();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Mock notifications - in real app, this would come from a notifications service
  const notifications = [
    {
      id: '1',
      title: 'New Seller Registration',
      message: 'A new seller has registered and is awaiting approval.',
      time: '2 hours ago',
      read: false
    },
    {
      id: '2',
      title: 'Farm Verification Required',
      message: 'Three new farms need verification.',
      time: '5 hours ago',
      read: false
    },
    {
      id: '3',
      title: 'System Update',
      message: 'Platform maintenance scheduled for tonight.',
      time: '1 day ago',
      read: true
    }
  ];

  const handleLogout = () => {
    // Clear auth context
    logout();
    
    // Clear Redux store
    dispatch(clearCredentials());
    
    // Clear any local storage data
    localStorage.clear();
    
    // Clear any session storage data
    sessionStorage.clear();
    
    // Clear any cookies
    document.cookie.split(";").forEach((c) => {
      document.cookie = c
        .replace(/^ +/, "")
        .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });
    
    // Redirect to login page
    navigate('/login', { replace: true });
  };

  if (!isAuthenticated) {
    return (
      <Suspense fallback={<div>Loading...</div>}>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/reset-password" element={<ResetPasswordPage />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Suspense>
    );
  }

  return (
    <Layout
      navigation={mainNavigation}
      userEmail={user?.email}
      userRole={user?.role}
      onLogout={handleLogout}
      notifications={notifications}
    >
      <Suspense fallback={<div>Loading...</div>}>
        <Routes>
          {/* <Route path="/dashboard" element={<DashboardPage />} /> */}
          <Route path="/farms" element={<FarmsPage />} />

          {/* Original pages */}
          <Route path="/farmers" element={<SellersPage />} />
          <Route path="/crops" element={<CropsPage />} />
          <Route path="/analytics" element={<AnalyticsPage />} />

          {/* Enhanced pages */}
          <Route path="/farmers-enhanced" element={<EnhancedSellersPage />} />
          <Route path="/crops-enhanced" element={<EnhancedCropsPage />} />
          <Route path="/analytics-enhanced" element={<EnhancedAnalyticsPage />} />

          {/* Common routes */}
          <Route path="/farmers/create" element={<CreateSellerPage />} />
          <Route path="/crops/create" element={<CreateCropPage />} />
          <Route path="/crops/:id" element={<CropDetailsPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          {/* <Route path="/" element={<Navigate to="/dashboard" replace />} /> */}
          <Route path="*" element={<Navigate to="/farmers-enhanced" replace />} />
        </Routes>
      </Suspense>
    </Layout>
  );
}

export default App;
