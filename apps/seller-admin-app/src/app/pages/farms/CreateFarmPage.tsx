import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { farmService, FarmDetails } from '@befarmer-platform/shared-utils';
import { Card, Input, Button } from '@befarmer-platform/shared-ui';
import { MapPin, Ruler, Droplets, FileText, Camera, ChevronRight, ChevronLeft } from 'lucide-react';

interface FarmFormData {
  name: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
}

const SOIL_TYPES = [
  'Clay',
  'Sandy',
  'Loamy',
  'Silt',
  'Peat',
  'Chalk',
  'Alluvial'
];

const WATER_SOURCES = [
  'River',
  'Well',
  'Rainwater Harvesting',
  'Canal',
  'Groundwater',
  'Lake/Pond'
];

const INFRASTRUCTURE_OPTIONS = [
  'Storage Facility',
  'Irrigation System',
  'Processing Unit',
  'Greenhouse',
  'Cold Storage',
  'Packaging Unit',
  'Solar Panels'
];

const CERTIFICATION_OPTIONS = [
  'Organic',
  'Fair Trade',
  'GAP (Good Agricultural Practices)',
  'Rainforest Alliance',
  'USDA Organic',
  'Demeter Biodynamic'
];

const CreateFarmPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<FarmFormData>({
    name: '',
    location: {
      latitude: 0,
      longitude: 0,
      address: ''
    },
    totalArea: 0,
    soilType: '',
    waterSource: '',
    infrastructure: [],
    certifications: []
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLocationChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        [field]: value
      }
    }));
  };

  const handleCheckboxChange = (field: 'infrastructure' | 'certifications', value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);
      const farm = await farmService.createFarm(formData);
      navigate(`/farms/${farm.id}`);
    } catch (err) {
      setError('Failed to create farm. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Basic Information</h2>
            <Input
              label="Farm Name"
              value={formData.name}
              onChange={e => handleInputChange('name', e.target.value)}
              placeholder="Enter farm name"
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Latitude"
                type="number"
                value={formData.location.latitude}
                onChange={e => handleLocationChange('latitude', e.target.value)}
                placeholder="Enter latitude"
              />
              <Input
                label="Longitude"
                type="number"
                value={formData.location.longitude}
                onChange={e => handleLocationChange('longitude', e.target.value)}
                placeholder="Enter longitude"
              />
            </div>
            <Input
              label="Address"
              value={formData.location.address}
              onChange={e => handleLocationChange('address', e.target.value)}
              placeholder="Enter complete address"
            />
            <Input
              label="Total Area (acres)"
              type="number"
              value={formData.totalArea}
              onChange={e => handleInputChange('totalArea', parseFloat(e.target.value))}
              placeholder="Enter total area in acres"
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Farm Characteristics</h2>
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">Soil Type</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {SOIL_TYPES.map(type => (
                  <label
                    key={type}
                    className={`
                      flex items-center p-3 rounded-lg border cursor-pointer
                      ${formData.soilType === type
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-green-200'
                      }
                    `}
                  >
                    <input
                      type="radio"
                      name="soilType"
                      value={type}
                      checked={formData.soilType === type}
                      onChange={e => handleInputChange('soilType', e.target.value)}
                      className="sr-only"
                    />
                    <span className="text-sm">{type}</span>
                  </label>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">Water Source</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {WATER_SOURCES.map(source => (
                  <label
                    key={source}
                    className={`
                      flex items-center p-3 rounded-lg border cursor-pointer
                      ${formData.waterSource === source
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-200'
                      }
                    `}
                  >
                    <input
                      type="radio"
                      name="waterSource"
                      value={source}
                      checked={formData.waterSource === source}
                      onChange={e => handleInputChange('waterSource', e.target.value)}
                      className="sr-only"
                    />
                    <span className="text-sm">{source}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Infrastructure & Certifications</h2>
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">Infrastructure</label>
              <div className="grid grid-cols-2 gap-3">
                {INFRASTRUCTURE_OPTIONS.map(item => (
                  <label
                    key={item}
                    className={`
                      flex items-center p-3 rounded-lg border cursor-pointer
                      ${formData.infrastructure.includes(item)
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-gray-200 hover:border-orange-200'
                      }
                    `}
                  >
                    <input
                      type="checkbox"
                      checked={formData.infrastructure.includes(item)}
                      onChange={() => handleCheckboxChange('infrastructure', item)}
                      className="sr-only"
                    />
                    <span className="text-sm">{item}</span>
                  </label>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">Certifications</label>
              <div className="grid grid-cols-2 gap-3">
                {CERTIFICATION_OPTIONS.map(cert => (
                  <label
                    key={cert}
                    className={`
                      flex items-center p-3 rounded-lg border cursor-pointer
                      ${formData.certifications.includes(cert)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-purple-200'
                      }
                    `}
                  >
                    <input
                      type="checkbox"
                      checked={formData.certifications.includes(cert)}
                      onChange={() => handleCheckboxChange('certifications', cert)}
                      className="sr-only"
                    />
                    <span className="text-sm">{cert}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Create New Farm</h1>
        <div className="text-sm text-gray-500">
          Step {currentStep} of 3
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="h-2 bg-gray-200 rounded-full">
          <div
            className="h-2 bg-green-500 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / 3) * 100}%` }}
          />
        </div>
      </div>

      {/* Form Card */}
      <Card className="overflow-hidden">
        <div className="p-6">
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {renderStep()}

          <div className="flex justify-between mt-8">
            {currentStep > 1 && (
              <Button
                onClick={() => setCurrentStep(prev => prev - 1)}
                variant="secondary"
                className="flex items-center"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            )}
            {currentStep < 3 ? (
              <Button
                onClick={() => setCurrentStep(prev => prev + 1)}
                className="flex items-center ml-auto"
              >
                Next
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                loading={loading}
                className="flex items-center ml-auto"
              >
                Create Farm
              </Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CreateFarmPage; 