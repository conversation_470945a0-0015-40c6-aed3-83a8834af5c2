import React, { useState } from 'react';
import { Card, DataTable, Button, StatusBadge } from '@befarmer-platform/shared-ui';
import { 
  Sprout, 
  Plus, 
  Filter, 
  Search, 
  MapPin, 
  User,
  TrendingUp,
  MoreVertical
} from 'lucide-react';

interface Farm {
  id: string;
  name: string;
  location: string;
  owner: string;
  ownerContact: string;
  size: string;
  crops: string[];
  status: 'active' | 'inactive' | 'pending';
  yield: string;
  lastHarvest: string;
}

const FarmsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Mock data - in real app, this would come from API
  const farms: Farm[] = [
    {
      id: '1',
      name: 'Green Valley Farm',
      location: 'Karnataka, Bangalore Rural',
      owner: '<PERSON><PERSON>',
      ownerContact: '+91 9876543210',
      size: '25 acres',
      crops: ['Tomatoes', 'Potatoes', 'Onions'],
      status: 'active',
      yield: '92%',
      lastHarvest: '2024-02-15'
    },
    {
      id: '2',
      name: 'Sunshine Agro Farm',
      location: 'Maharashtra, Pune',
      owner: '<PERSON><PERSON>',
      ownerContact: '+91 9876543211',
      size: '30 acres',
      crops: ['Rice', 'Wheat', 'Corn'],
      status: 'active',
      yield: '88%',
      lastHarvest: '2024-02-10'
    },
    {
      id: '3',
      name: 'Nature\'s Best Farm',
      location: 'Tamil Nadu, Coimbatore',
      owner: 'Anand Krishnan',
      ownerContact: '+91 9876543212',
      size: '20 acres',
      crops: ['Cotton', 'Sugarcane'],
      status: 'inactive',
      yield: '0%',
      lastHarvest: '2023-12-20'
    },
    {
      id: '4',
      name: 'Organic Fields',
      location: 'Gujarat, Ahmedabad',
      owner: 'Mehul Patel',
      ownerContact: '+91 9876543213',
      size: '15 acres',
      crops: ['Vegetables', 'Fruits'],
      status: 'pending',
      yield: 'N/A',
      lastHarvest: 'N/A'
    }
  ];

  const getStatusBadgeVariant = (status: Farm['status']) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'info';
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Farm',
      render: (value: string, farm: Farm) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
            <Sprout className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{value}</p>
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <MapPin className="w-4 h-4" />
              {farm.location}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'owner',
      label: 'Owner',
      render: (value: string, farm: Farm) => (
        <div>
          <p className="font-medium text-gray-900">{value}</p>
          <p className="text-sm text-gray-500">{farm.ownerContact}</p>
        </div>
      )
    },
    {
      key: 'size',
      label: 'Size & Crops',
      render: (value: string, farm: Farm) => (
        <div>
          <p className="font-medium text-gray-900">{value}</p>
          <p className="text-sm text-gray-500">{farm.crops.join(', ')}</p>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: Farm['status']) => (
        <StatusBadge
          type="custom"
          status={value}
          customColor={getStatusBadgeVariant(value)}
        />
      )
    },
    {
      key: 'yield',
      label: 'Yield',
      render: (value: string, farm: Farm) => (
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            farm.status === 'active'
              ? 'bg-green-500'
              : farm.status === 'inactive'
              ? 'bg-red-500'
              : 'bg-yellow-500'
          }`} />
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'lastHarvest',
      label: 'Last Harvest',
      render: (value: string) => (
        <span className="text-gray-500">
          {value === 'N/A' ? value : new Date(value).toLocaleDateString()}
        </span>
      )
    },
    {
      key: 'actions',
      label: '',
      render: (_: any, farm: Farm) => (
        <div className="flex items-center gap-2">
          <button 
            className="p-1 text-gray-400 hover:bg-gray-100 rounded-full transition-colors"
            title="More options"
          >
            <MoreVertical className="w-4 h-4" />
          </button>
        </div>
      )
    }
  ];

  const filteredFarms = farms.filter(farm => {
    const matchesSearch = 
      farm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      farm.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      farm.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
      farm.crops.some(crop => crop.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = selectedStatus === 'all' || farm.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Actions Bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex-1 w-full sm:max-w-xs">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search farms..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <div className="flex gap-3 w-full sm:w-auto">
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
          </select>
          
          <Button
            variant="secondary"
            icon={<Filter className="w-5 h-5" />}
          >
            Filters
          </Button>
          
          <Button
            variant="primary"
            icon={<Plus className="w-5 h-5" />}
          >
            Add Farm
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
              <Sprout className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Farms</p>
              <p className="text-2xl font-semibold text-gray-900">{farms.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
              <User className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Active Farmers</p>
              <p className="text-2xl font-semibold text-gray-900">
                {farms.filter(f => f.status === 'active').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
              <MapPin className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Area</p>
              <p className="text-2xl font-semibold text-gray-900">
                {farms.reduce((acc, farm) => acc + parseInt(farm.size), 0)} acres
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Average Yield</p>
              <p className="text-2xl font-semibold text-gray-900">
                {Math.round(
                  farms
                    .filter(f => f.status === 'active')
                    .reduce((acc, farm) => acc + parseInt(farm.yield), 0) /
                  farms.filter(f => f.status === 'active').length
                )}%
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Farms Table */}
      <Card>
        <DataTable
          columns={columns}
          data={filteredFarms}
          emptyMessage="No farms found"
          sortable
        />
      </Card>
    </div>
  );
};

export default FarmsPage; 