import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { farmService } from '@befarmer-platform/shared-utils';
import {
  Card,
  DashboardGrid,
  StatsCard,
  StatusBadge,
  DataTable
} from '@befarmer-platform/shared-ui';
import {
  MapPin,
  Ruler,
  Droplets,
  Sprout,
  Tractor,
  FileText,
  Camera
} from 'lucide-react';

interface FarmDetails {
  id: string;
  name: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  plots: {
    id: string;
    area: number;
    status: 'available' | 'leased' | 'cultivating';
    currentCrop?: string;
    leaseStartDate?: Date;
    leaseEndDate?: Date;
  }[];
  photos: string[];
}

const FarmDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [farm, setFarm] = useState<FarmDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFarmDetails = async () => {
      try {
        const data = await farmService.getFarmDetails(id || '');
        setFarm(data);
      } catch (err) {
        setError('Failed to load farm details');
      } finally {
        setLoading(false);
      }
    };

    fetchFarmDetails();
  }, [id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-green-500" />
      </div>
    );
  }

  if (error || !farm) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-600">{error || 'Farm not found'}</p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{farm.name}</h1>
          <p className="text-gray-500 flex items-center mt-2">
            <MapPin className="w-4 h-4 mr-2" />
            {farm.location.address}
          </p>
        </div>
        <div className="flex space-x-4">
          <button className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition flex items-center">
            <Camera className="w-4 h-4 mr-2" />
            Add Photos
          </button>
          <button className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition flex items-center">
            <FileText className="w-4 h-4 mr-2" />
            Generate Report
          </button>
        </div>
      </div>

      {/* Farm Stats */}
      <DashboardGrid>
        <StatsCard
          title="Total Area"
          value={`${farm.totalArea} acres`}
          icon={<Ruler className="h-6 w-6" />}
          color="green"
        />
        <StatsCard
          title="Water Source"
          value={farm.waterSource}
          icon={<Droplets className="h-6 w-6" />}
          color="blue"
        />
        <StatsCard
          title="Active Plots"
          value={farm.plots.filter(p => p.status === 'cultivating').length.toString()}
          icon={<Sprout className="h-6 w-6" />}
          color="orange"
        />
        <StatsCard
          title="Infrastructure"
          value={farm.infrastructure.length.toString()}
          icon={<Tractor className="h-6 w-6" />}
          color="purple"
        />
      </DashboardGrid>

      {/* Plot Management */}
      <Card className="overflow-hidden">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Plot Management</h2>
          <DataTable
            columns={[
              { key: 'id', label: 'Plot ID' },
              { key: 'area', label: 'Area (acres)' },
              {
                key: 'status',
                label: 'Status',
                render: (value) => (
                  <StatusBadge
                    status={value}
                    className={
                      value === 'available'
                        ? 'bg-green-100 text-green-800'
                        : value === 'leased'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-orange-100 text-orange-800'
                    }
                  />
                ),
              },
              { key: 'currentCrop', label: 'Current Crop' },
              {
                key: 'leaseStartDate',
                label: 'Lease Start',
                render: (value) => value ? new Date(value).toLocaleDateString() : '-',
              },
              {
                key: 'leaseEndDate',
                label: 'Lease End',
                render: (value) => value ? new Date(value).toLocaleDateString() : '-',
              },
            ]}
            data={farm.plots}
          />
        </div>
      </Card>

      {/* Farm Photos */}
      <Card className="overflow-hidden">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Farm Photos</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {farm.photos.map((photo, index) => (
              <div
                key={index}
                className="aspect-square rounded-lg overflow-hidden"
              >
                <img
                  src={photo}
                  alt={`Farm photo ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Certifications */}
      <Card className="overflow-hidden">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Certifications</h2>
          <div className="flex flex-wrap gap-2">
            {farm.certifications.map((cert, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm"
              >
                {cert}
              </span>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default FarmDetailsPage; 