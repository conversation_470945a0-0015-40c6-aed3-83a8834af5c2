import React, { useState } from 'react';
import { Card, Input, Button, Alert } from '@befarmer-platform/shared-ui';
import { 
  User, 
  Mail, 
  Phone, 
  Bell, 
  Lock, 
  Globe, 
  Settings,
  Save
} from 'lucide-react';

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [profileForm, setProfileForm] = useState({
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    role: 'Administrator'
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    newSellerAlerts: true,
    farmVerificationAlerts: true,
    systemUpdates: true
  });

  const [systemSettings, setSystemSettings] = useState({
    language: 'en',
    timezone: 'Asia/Kolkata',
    dateFormat: 'DD/MM/YYYY',
    autoLogout: '30',
    theme: 'light'
  });

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle profile update
  };

  const handleNotificationToggle = (key: keyof typeof notificationSettings) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSystemSettingChange = (key: keyof typeof systemSettings, value: string) => {
    setSystemSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const tabs = [
    { id: 'profile', label: 'Profile Settings', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'system', label: 'System Settings', icon: Settings }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Settings Navigation */}
      <div className="bg-white rounded-lg border border-gray-200 p-1">
        <div className="flex space-x-1">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md flex-1 ${
                activeTab === tab.id
                  ? 'bg-green-50 text-green-700'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="w-5 h-5" />
              <span className="font-medium">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Profile Settings */}
      {activeTab === 'profile' && (
        <Card className="p-6">
          <form onSubmit={handleProfileSubmit}>
            <div className="space-y-6">
              <div className="flex items-center gap-6">
                <div className="w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center">
                  <User className="w-12 h-12 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Profile Picture</h3>
                  <p className="text-sm text-gray-500 mb-2">Update your profile photo</p>
                  <Button variant="secondary" size="small">Change Photo</Button>
                </div>
              </div>

              <div className="space-y-4">
                <Input
                  label="Full Name"
                  value={profileForm.name}
                  onChange={e => setProfileForm(prev => ({ ...prev, name: e.target.value }))}
                  startIcon={<User className="w-5 h-5 text-gray-400" />}
                />
                <Input
                  label="Email Address"
                  value={profileForm.email}
                  onChange={e => setProfileForm(prev => ({ ...prev, email: e.target.value }))}
                  startIcon={<Mail className="w-5 h-5 text-gray-400" />}
                />
                <Input
                  label="Phone Number"
                  value={profileForm.phone}
                  onChange={e => setProfileForm(prev => ({ ...prev, phone: e.target.value }))}
                  startIcon={<Phone className="w-5 h-5 text-gray-400" />}
                />
                <Input
                  label="Role"
                  value={profileForm.role}
                  disabled
                  startIcon={<Lock className="w-5 h-5 text-gray-400" />}
                />
              </div>

              <div className="flex justify-end">
                <Button
                  type="submit"
                  variant="primary"
                  icon={<Save className="w-5 h-5" />}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Notification Settings */}
      {activeTab === 'notifications' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Notification Preferences</h3>
          
          <div className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Communication Channels</h4>
              {[
                { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },
                { key: 'pushNotifications', label: 'Push Notifications', description: 'Receive push notifications on your device' },
                { key: 'smsNotifications', label: 'SMS Notifications', description: 'Receive notifications via SMS' }
              ].map(item => (
                <div key={item.key} className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div>
                    <p className="font-medium text-gray-900">{item.label}</p>
                    <p className="text-sm text-gray-500">{item.description}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={notificationSettings[item.key as keyof typeof notificationSettings]}
                      onChange={() => handleNotificationToggle(item.key as keyof typeof notificationSettings)}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                  </label>
                </div>
              ))}
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Notification Types</h4>
              {[
                { key: 'newSellerAlerts', label: 'New Seller Alerts', description: 'Get notified when new sellers register' },
                { key: 'farmVerificationAlerts', label: 'Farm Verification Alerts', description: 'Get notified about farm verification requests' },
                { key: 'systemUpdates', label: 'System Updates', description: 'Get notified about system updates and maintenance' }
              ].map(item => (
                <div key={item.key} className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div>
                    <p className="font-medium text-gray-900">{item.label}</p>
                    <p className="text-sm text-gray-500">{item.description}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={notificationSettings[item.key as keyof typeof notificationSettings]}
                      onChange={() => handleNotificationToggle(item.key as keyof typeof notificationSettings)}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* System Settings */}
      {activeTab === 'system' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">System Preferences</h3>
          
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                <select
                  value={systemSettings.language}
                  onChange={e => handleSystemSettingChange('language', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="en">English</option>
                  <option value="hi">Hindi</option>
                  <option value="kn">Kannada</option>
                  <option value="mr">Marathi</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                <select
                  value={systemSettings.timezone}
                  onChange={e => handleSystemSettingChange('timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="Asia/Kolkata">India (IST)</option>
                  <option value="GMT">GMT</option>
                  <option value="UTC">UTC</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                <select
                  value={systemSettings.dateFormat}
                  onChange={e => handleSystemSettingChange('dateFormat', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                  <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                  <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Auto Logout (minutes)</label>
                <select
                  value={systemSettings.autoLogout}
                  onChange={e => handleSystemSettingChange('autoLogout', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="15">15 minutes</option>
                  <option value="30">30 minutes</option>
                  <option value="60">1 hour</option>
                  <option value="120">2 hours</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Theme</label>
              <div className="grid grid-cols-2 gap-4">
                <button
                  onClick={() => handleSystemSettingChange('theme', 'light')}
                  className={`p-4 rounded-lg border ${
                    systemSettings.theme === 'light'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 rounded-full bg-white border border-gray-200" />
                    <span className="font-medium text-gray-900">Light</span>
                  </div>
                </button>

                <button
                  onClick={() => handleSystemSettingChange('theme', 'dark')}
                  className={`p-4 rounded-lg border ${
                    systemSettings.theme === 'dark'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 rounded-full bg-gray-900 border border-gray-700" />
                    <span className="font-medium text-gray-900">Dark</span>
                  </div>
                </button>
              </div>
            </div>

            <Alert
              variant="info"
              title="Theme Preview"
              message="Theme changes will take effect after you refresh the page."
            />

            <div className="flex justify-end">
              <Button
                variant="primary"
                icon={<Save className="w-5 h-5" />}
                onClick={() => {
                  // Handle saving system settings
                }}
              >
                Save Preferences
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default SettingsPage; 