import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Card, 
  Button, 
  Input, 
  StatsCard,
  Alert
} from '@befarmer-platform/shared-ui';
import { 
  User, 
  MapPin, 
  Phone, 
  Mail, 
  Lock,
  ChevronRight,
  ChevronLeft,
  Check
} from 'lucide-react';
import { useOnboardSellerMutation } from '@befarmer-platform/shared-utils';

interface FormData {
  // Personal Information
  name: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;

  // Address Information
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  pincode: string;

  // Farm Information
  farmName: string;
  farmType: string;
  farmSize: string;
  farmLocation: string;

  // Bank Information
  accountName: string;
  accountNumber: string;
  bankName: string;
  ifscCode: string;
  branch: string;
}

const INITIAL_FORM_DATA: FormData = {
  name: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  addressLine1: '',
  addressLine2: '',
  city: '',
  state: '',
  pincode: '',
  farmName: '',
  farmType: '',
  farmSize: '',
  farmLocation: '',
  accountName: '',
  accountNumber: '',
  bankName: '',
  ifscCode: '',
  branch: ''
};

const CreateSellerPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>(INITIAL_FORM_DATA);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  
  const [onboardSeller, { isLoading, error }] = useOnboardSellerMutation();

  const steps = [
    { number: 1, title: 'Personal Information', icon: User },
    { number: 2, title: 'Address Details', icon: MapPin },
    { number: 3, title: 'Farm Information', icon: MapPin },
    { number: 4, title: 'Bank Details', icon: Lock }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear error when user types
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Partial<FormData> = {};

    switch (step) {
      case 1:
        if (!formData.name) newErrors.name = 'Name is required';
        if (!formData.email) newErrors.email = 'Email is required';
        else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
          newErrors.email = 'Invalid email address';
        }
        if (!formData.phone) newErrors.phone = 'Phone is required';
        else if (!/^\d{10}$/.test(formData.phone)) {
          newErrors.phone = 'Invalid phone number';
        }
        if (!formData.password) newErrors.password = 'Password is required';
        else if (formData.password.length < 8) {
          newErrors.password = 'Password must be at least 8 characters';
        }
        if (formData.password !== formData.confirmPassword) {
          newErrors.confirmPassword = 'Passwords do not match';
        }
        break;

      case 2:
        if (!formData.addressLine1) newErrors.addressLine1 = 'Address is required';
        if (!formData.city) newErrors.city = 'City is required';
        if (!formData.state) newErrors.state = 'State is required';
        if (!formData.pincode) newErrors.pincode = 'Pincode is required';
        else if (!/^\d{6}$/.test(formData.pincode)) {
          newErrors.pincode = 'Invalid pincode';
        }
        break;

      case 3:
        if (!formData.farmName) newErrors.farmName = 'Farm name is required';
        if (!formData.farmType) newErrors.farmType = 'Farm type is required';
        if (!formData.farmSize) newErrors.farmSize = 'Farm size is required';
        if (!formData.farmLocation) newErrors.farmLocation = 'Farm location is required';
        break;

      case 4:
        if (!formData.accountName) newErrors.accountName = 'Account name is required';
        if (!formData.accountNumber) newErrors.accountNumber = 'Account number is required';
        if (!formData.bankName) newErrors.bankName = 'Bank name is required';
        if (!formData.ifscCode) newErrors.ifscCode = 'IFSC code is required';
        if (!formData.branch) newErrors.branch = 'Branch is required';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep(currentStep)) return;

    try {
      await onboardSeller({
        name: formData.name,
        email: formData.email,
        contact: formData.phone,
        password: formData.password,
        address: {
          addressLine1: formData.addressLine1,
          addressLine2: formData.addressLine2,
          city: formData.city,
          state: formData.state,
          pincode: formData.pincode,
        },
        farm: {
          name: formData.farmName,
          type: formData.farmType,
          size: formData.farmSize,
          location: formData.farmLocation,
        },
        bank: {
          accountName: formData.accountName,
          accountNumber: formData.accountNumber,
          bankName: formData.bankName,
          ifscCode: formData.ifscCode,
          branch: formData.branch,
        }
      }).unwrap();

      // Show success message and redirect
      navigate('/farmers');
    } catch (err) {
      console.error('Failed to create seller:', err);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <Input
              label="Full Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              error={errors.name}
              placeholder="Enter seller's full name"
              startIcon={<User className="w-5 h-5 text-gray-400" />}
            />
            <Input
              label="Email Address"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              error={errors.email}
              placeholder="Enter seller's email address"
              startIcon={<Mail className="w-5 h-5 text-gray-400" />}
            />
            <Input
              label="Phone Number"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              error={errors.phone}
              placeholder="Enter 10-digit phone number"
              startIcon={<Phone className="w-5 h-5 text-gray-400" />}
            />
            <Input
              label="Password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              error={errors.password}
              placeholder="Enter password"
              startIcon={<Lock className="w-5 h-5 text-gray-400" />}
            />
            <Input
              label="Confirm Password"
              name="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              error={errors.confirmPassword}
              placeholder="Confirm password"
              startIcon={<Lock className="w-5 h-5 text-gray-400" />}
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <Input
              label="Address Line 1"
              name="addressLine1"
              value={formData.addressLine1}
              onChange={handleInputChange}
              error={errors.addressLine1}
              placeholder="Enter street address"
              startIcon={<MapPin className="w-5 h-5 text-gray-400" />}
            />
            <Input
              label="Address Line 2"
              name="addressLine2"
              value={formData.addressLine2}
              onChange={handleInputChange}
              error={errors.addressLine2}
              placeholder="Enter apartment, suite, etc."
              startIcon={<MapPin className="w-5 h-5 text-gray-400" />}
            />
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="City"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                error={errors.city}
                placeholder="Enter city"
              />
              <Input
                label="State"
                name="state"
                value={formData.state}
                onChange={handleInputChange}
                error={errors.state}
                placeholder="Enter state"
              />
            </div>
            <Input
              label="Pincode"
              name="pincode"
              value={formData.pincode}
              onChange={handleInputChange}
              error={errors.pincode}
              placeholder="Enter 6-digit pincode"
            />
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <Input
              label="Farm Name"
              name="farmName"
              value={formData.farmName}
              onChange={handleInputChange}
              error={errors.farmName}
              placeholder="Enter farm name"
            />
            <Input
              label="Farm Type"
              name="farmType"
              value={formData.farmType}
              onChange={handleInputChange}
              error={errors.farmType}
              placeholder="Enter farm type"
            />
            <Input
              label="Farm Size (in acres)"
              name="farmSize"
              value={formData.farmSize}
              onChange={handleInputChange}
              error={errors.farmSize}
              placeholder="Enter farm size"
            />
            <Input
              label="Farm Location"
              name="farmLocation"
              value={formData.farmLocation}
              onChange={handleInputChange}
              error={errors.farmLocation}
              placeholder="Enter farm location"
              startIcon={<MapPin className="w-5 h-5 text-gray-400" />}
            />
          </div>
        );

      case 4:
        return (
          <div className="space-y-4">
            <Input
              label="Account Holder Name"
              name="accountName"
              value={formData.accountName}
              onChange={handleInputChange}
              error={errors.accountName}
              placeholder="Enter account holder name"
            />
            <Input
              label="Account Number"
              name="accountNumber"
              value={formData.accountNumber}
              onChange={handleInputChange}
              error={errors.accountNumber}
              placeholder="Enter account number"
            />
            <Input
              label="Bank Name"
              name="bankName"
              value={formData.bankName}
              onChange={handleInputChange}
              error={errors.bankName}
              placeholder="Enter bank name"
            />
            <Input
              label="IFSC Code"
              name="ifscCode"
              value={formData.ifscCode}
              onChange={handleInputChange}
              error={errors.ifscCode}
              placeholder="Enter IFSC code"
            />
            <Input
              label="Branch"
              name="branch"
              value={formData.branch}
              onChange={handleInputChange}
              error={errors.branch}
              placeholder="Enter branch name"
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-3xl mx-auto">
      {/* Progress Tracker */}
      <div className="mb-8">
        <div className="flex justify-between">
          {steps.map((step, idx) => (
            <div
              key={step.number}
              className={`flex items-center ${
                idx < steps.length - 1 ? 'flex-1' : ''
              }`}
            >
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  step.number === currentStep
                    ? 'border-green-500 bg-green-50 text-green-600'
                    : step.number < currentStep
                    ? 'border-green-500 bg-green-500 text-white'
                    : 'border-gray-300 bg-white text-gray-500'
                }`}
              >
                {step.number < currentStep ? (
                  <Check className="w-6 h-6" />
                ) : (
                  <step.icon className="w-5 h-5" />
                )}
              </div>
              {idx < steps.length - 1 && (
                <div
                  className={`flex-1 h-0.5 mx-2 ${
                    step.number < currentStep ? 'bg-green-500' : 'bg-gray-300'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2">
          {steps.map((step) => (
            <div
              key={step.number}
              className={`text-sm font-medium ${
                step.number === currentStep
                  ? 'text-green-600'
                  : step.number < currentStep
                  ? 'text-green-500'
                  : 'text-gray-500'
              }`}
              style={{ width: '25%', textAlign: 'center' }}
            >
              {step.title}
            </div>
          ))}
        </div>
      </div>

      {/* Form Card */}
      <Card className="p-6">
        <form onSubmit={handleSubmit}>
          {error && (
            <Alert
              variant="error"
              title="Error"
              message={typeof error === 'object' && 'message' in error ? (error.message as string) : 'Failed to create seller'}
              className="mb-4"
            />
          )}

          {renderStepContent()}

          <div className="flex justify-between mt-8">
            <Button
              type="button"
              variant="secondary"
              onClick={handleBack}
              disabled={currentStep === 1}
              icon={<ChevronLeft className="w-5 h-5" />}
            >
              Back
            </Button>
            
            {currentStep < steps.length ? (
              <Button
                type="button"
                variant="primary"
                onClick={handleNext}
                icon={<ChevronRight className="w-5 h-5" />}
              >
                Next
              </Button>
            ) : (
              <Button
                type="submit"
                variant="primary"
                loading={isLoading}
                icon={<Check className="w-5 h-5" />}
              >
                Create Seller
              </Button>
            )}
          </div>
        </form>
      </Card>
    </div>
  );
};

export default CreateSellerPage; 