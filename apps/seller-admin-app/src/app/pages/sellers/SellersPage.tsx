import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, DataTable, Button, StatusBadge, Spinner } from '@befarmer-platform/shared-ui';
import { 
  User, 
  Plus, 
  Filter, 
  Search, 
  Check, 
  X, 
  MoreVertical,
  Download,
  Upload,
  Trash2,
  Edit3,
  UserPlus
} from 'lucide-react';
import { useListSellersQuery } from '@befarmer-platform/shared-utils';

interface Seller {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  status: 'active' | 'pending' | 'suspended';
  farms: number;
  revenue: string;
  joinedDate: string;
}

const SellersPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedSellers, setSelectedSellers] = useState<string[]>([]);
  const { data, isLoading, error } = useListSellersQuery({ page: 1, limit: 20 });

  // Map API sellers to table format
  const sellers = (data?.data?.sellers || []).map((s: any) => ({
    id: s.sellerId,
    name: s.personalInfo?.name,
    email: s.personalInfo?.email,
    phone: s.personalInfo?.contact,
    location: `${s.personalInfo?.address?.state}, ${s.personalInfo?.address?.city}`,
    status: s.status?.toLowerCase() || 'pending',
    farms: s.farms || 0,
    revenue: s.revenue || '₹0',
    joinedDate: s.createdAt,
  }));

  const getStatusBadgeVariant = (status: Seller['status']) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'suspended':
        return 'error';
      default:
        return 'info';
    }
  };

  const handleSellerAction = (action: string, sellerId: string) => {
    switch (action) {
      case 'edit':
        navigate(`/sellers/${sellerId}/edit`);
        break;
      case 'delete':
        // Handle delete
        break;
      case 'approve':
        // Handle approve
        break;
      case 'reject':
        // Handle reject
        break;
      default:
        break;
    }
  };

  const handleBulkAction = (action: string) => {
    switch (action) {
      case 'export':
        // Handle export
        break;
      case 'delete':
        // Handle bulk delete
        break;
      default:
        break;
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Seller',
      render: (value: string, seller: Seller) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-50 to-orange-50 flex items-center justify-center border border-gray-100">
            <User className="w-5 h-5 text-gray-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{value}</p>
            <p className="text-sm text-gray-500">{seller.email}</p>
          </div>
        </div>
      )
    },
    {
      key: 'phone',
      label: 'Contact',
      render: (value: string, seller: Seller) => (
        <div>
          <p className="font-medium text-gray-700">{value}</p>
          <p className="text-sm text-gray-500">{seller.location}</p>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: Seller['status']) => (
        <StatusBadge
          type="custom"
          status={value}
          customColor={getStatusBadgeVariant(value)}
        />
      )
    },
    {
      key: 'farms',
      label: 'Farms',
      render: (value: number) => (
        <span className="font-medium text-gray-700">{value} farm{value !== 1 ? 's' : ''}</span>
      )
    },
    {
      key: 'revenue',
      label: 'Revenue',
      render: (value: string) => (
        <span className="font-medium text-gray-900">{value}</span>
      )
    },
    {
      key: 'joinedDate',
      label: 'Joined',
      render: (value: string) => (
        <span className="text-gray-500">{new Date(value).toLocaleDateString()}</span>
      )
    },
    {
      key: 'actions',
      label: '',
      render: (_: any, seller: Seller) => (
        <div className="flex items-center gap-2">
          <button 
            className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Edit"
            onClick={() => handleSellerAction('edit', seller.id)}
          >
            <Edit3 className="w-4 h-4" />
          </button>
          
          {seller.status === 'pending' && (
            <>
              <button 
                className="p-1.5 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                title="Approve"
                onClick={() => handleSellerAction('approve', seller.id)}
              >
                <Check className="w-4 h-4" />
              </button>
              <button 
                className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Reject"
                onClick={() => handleSellerAction('reject', seller.id)}
              >
                <X className="w-4 h-4" />
              </button>
            </>
          )}
          
          <button 
            className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            title="Delete"
            onClick={() => handleSellerAction('delete', seller.id)}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      )
    }
  ];

  const filteredSellers = sellers.filter((seller: Seller) => {
    const matchesSearch = 
      seller.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      seller.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      seller.phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      seller.location?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || seller.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Sellers</h1>
            <p className="text-gray-500 mt-1">Manage and monitor all sellers in the platform</p>
          </div>
          <Button
            variant="primary"
            icon={<UserPlus className="w-5 h-5" />}
            onClick={() => navigate('/farmers/create')}
            className="w-full sm:w-auto bg-green-600 hover:bg-green-700 shadow-sm"
          >
            Add New Seller
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
            <div className="text-green-600 text-sm font-medium">Total Sellers</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">{sellers.length}</div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <div className="text-blue-600 text-sm font-medium">Active Sellers</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">
              {sellers.filter((s: Seller) => s.status === 'active').length}
            </div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
            <div className="text-yellow-600 text-sm font-medium">Pending Approval</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">
              {sellers.filter((s: Seller) => s.status === 'pending').length}
            </div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
            <div className="text-purple-600 text-sm font-medium">Total Revenue</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">
              ₹{sellers.reduce((acc: number, s: Seller) => acc + parseInt(s.revenue.replace('₹', '').replace(',', '')), 0)}
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 w-full sm:max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name, email, phone or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex flex-wrap gap-3 w-full sm:w-auto">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white min-w-[140px]"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="suspended">Suspended</option>
            </select>
            
            <Button
              variant="secondary"
              icon={<Filter className="w-5 h-5" />}
              className="whitespace-nowrap"
            >
              More Filters
            </Button>
            
            <Button
              variant="secondary"
              icon={<Download className="w-5 h-5" />}
              onClick={() => handleBulkAction('export')}
              className="whitespace-nowrap"
            >
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Sellers Table */}
      <Card>
        {isLoading ? (
          <div className="flex items-center justify-center p-12">
            <Spinner size="large" className="text-green-500" />
            <p className="ml-3 text-gray-500">Loading sellers...</p>
          </div>
        ) : error ? (
          <div className="p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-50 flex items-center justify-center">
              <X className="w-8 h-8 text-red-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Sellers</h3>
            <p className="text-gray-500">There was a problem loading the sellers list. Please try again.</p>
            <Button variant="secondary" className="mt-4">
              Retry
            </Button>
          </div>
        ) : filteredSellers.length === 0 ? (
          <div className="p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-50 flex items-center justify-center">
              <User className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Sellers Found</h3>
            <p className="text-gray-500 mb-6">
              {searchTerm || selectedStatus !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first seller'}
            </p>
            {!searchTerm && selectedStatus === 'all' && (
              <Button
                variant="primary"
                icon={<UserPlus className="w-5 h-5" />}
                onClick={() => navigate('/farmers/create')}
                className="bg-green-600 hover:bg-green-700"
              >
                Add Your First Seller
              </Button>
            )}
          </div>
        ) : (
          <>
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  {filteredSellers.length} Seller{filteredSellers.length !== 1 ? 's' : ''}
                </h3>
                <Button
                  variant="secondary"
                  icon={<UserPlus className="w-5 h-5" />}
                  onClick={() => navigate('/farmers/create')}
                  className="text-green-600 hover:text-green-700"
                >
                  Add Seller
                </Button>
              </div>
            </div>
            <DataTable
              columns={columns}
              data={filteredSellers}
              emptyMessage="No sellers found"
              sortable
            />
          </>
        )}
      </Card>
    </div>
  );
};

export default SellersPage; 