import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  DataTable,
  Button,
  StatusBadge,
  StatsCard,
  Modal,
  Input,
  Card
} from '@befarmer-platform/shared-ui';
import {
  useGetAllSellersQuery,
  useVerifySellerMutation,
  useSuspendSellerMutation,
  useUpdateSellerMutation,
  useAppSelector
} from '@befarmer-platform/shared-utils';
import {
  Users,
  Plus,
  Eye,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Download,
  Shield,
  AlertTriangle,
  Tractor,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Settings
} from 'lucide-react';

// ✅ UPDATED: Mock data matching actual API response structure
const mockSellers = [
  {
    _id: '1',
    sellerId: 'FARMER-001',
    personalInfo: {
      address: {
        country: 'INDIA',
        state: 'Punjab',
        city: 'Ludhiana',
        pincode: '141001',
        addressLine1: 'Village Khanna',
        addressLine2: 'Near Gurudwara'
      },
      name: '<PERSON><PERSON>',
      contact: '+91-**********',
      email: 'r<PERSON><PERSON>@agritech.com'
    },
    documents: {
      identityProof: 'ID-12345',
      landOwnership: 'LAND-67890',
      certifications: ['ORGANIC-CERT-001']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'State Bank of India',
      ifscCode: 'SBIN0001234'
    },
    status: 'ACTIVE' as const,
    verificationStatus: 'VERIFIED' as const,
    farms: ['farm1', 'farm2'],
    createdAt: '2024-01-15T10:00:00.000Z',
    updatedAt: '2024-01-15T10:00:00.000Z',
    statusHistory: [],
    __v: 0
  },
  {
    _id: '2',
    sellerId: 'FARMER-002',
    personalInfo: {
      address: {
        country: 'INDIA',
        state: 'Haryana',
        city: 'Karnal',
        pincode: '132001',
        addressLine1: 'Sector 12',
        addressLine2: 'Model Town'
      },
      name: 'Priya Sharma',
      contact: '+91-**********',
      email: '<EMAIL>'
    },
    documents: {
      identityProof: 'ID-12346',
      landOwnership: 'LAND-67891',
      certifications: ['QUALITY-CERT-002']
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'Punjab National Bank',
      ifscCode: 'PUNB0001234'
    },
    status: 'ACTIVE' as const,
    verificationStatus: 'PENDING' as const,
    farms: ['farm3'],
    createdAt: '2024-02-20T10:00:00.000Z',
    updatedAt: '2024-02-20T10:00:00.000Z',
    statusHistory: [],
    __v: 0
  },
  {
    _id: '3',
    sellerId: 'FARMER-003',
    personalInfo: {
      address: {
        country: 'INDIA',
        state: 'Uttar Pradesh',
        city: 'Meerut',
        pincode: '250001',
        addressLine1: 'Civil Lines',
        addressLine2: 'Near Railway Station'
      },
      name: 'Amit Singh',
      contact: '+91-**********',
      email: '<EMAIL>'
    },
    documents: {
      identityProof: 'ID-12347',
      landOwnership: 'LAND-67892',
      certifications: []
    },
    bankDetails: {
      accountNumber: '**********',
      bankName: 'HDFC Bank',
      ifscCode: 'HDFC0001234'
    },
    status: 'INACTIVE' as const,
    verificationStatus: 'REJECTED' as const,
    farms: [],
    createdAt: '2024-01-10T10:00:00.000Z',
    updatedAt: '2024-01-10T10:00:00.000Z',
    statusHistory: [],
    __v: 0
  }
];

const EnhancedSellersPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedSellers, setSelectedSellers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [sellerToDelete, setSellerToDelete] = useState<string | null>(null);

  // ✅ ADDED: Additional modal states for farmer management
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedFarmer, setSelectedFarmer] = useState<any>(null);
  const [statusAction, setStatusAction] = useState<'activate' | 'deactivate' | 'suspend'>('activate');

  // Get current admin user for verification actions
  const currentAdmin = useAppSelector((state) => state.auth.user);

  // ✅ UPDATED: Using real API calls with proper error handling
  const {
    data: sellersResponse,
    isLoading,
    error,
    refetch
  } = useGetAllSellersQuery({
    page,
    limit,
    ...(filterStatus && { verified: filterStatus === 'VERIFIED' }),
    ...(searchQuery && { search: searchQuery })
  }, {
    // ✅ ADDED: Skip query if we want to show mock data when API is down
    skip: false,
    // ✅ ADDED: Retry configuration
    refetchOnMountOrArgChange: true,
  });

  const [verifySeller] = useVerifySellerMutation();
  const [suspendSeller] = useSuspendSellerMutation();
  const [updateSeller] = useUpdateSellerMutation();

  // ✅ FIXED: Memoize sellers to prevent re-renders
  const sellers = useMemo(() => {
    if (error) {
      // Use mock data when API is not available
      return mockSellers;
    }
    // Handle actual API response structure: { success: true, data: { sellers: [...], total, page, totalPages } }
    return Array.isArray(sellersResponse?.data?.sellers) ? sellersResponse.data.sellers : [];
  }, [error, sellersResponse?.data?.sellers]);

  // ✅ FIXED: Memoize pagination to prevent re-renders
  const pagination = useMemo(() => error ? {
    page: 1,
    limit: 10,
    total: mockSellers.length,
    totalPages: 1,
    hasNext: false,
    hasPrev: false
  } : {
    page: sellersResponse?.data?.page || 1,
    limit: 10,
    total: sellersResponse?.data?.total || 0,
    totalPages: sellersResponse?.data?.totalPages || 1,
    hasNext: (sellersResponse?.data?.page || 1) < (sellersResponse?.data?.totalPages || 1),
    hasPrev: (sellersResponse?.data?.page || 1) > 1
  }, [error, sellersResponse?.data?.page, sellersResponse?.data?.total, sellersResponse?.data?.totalPages]);

  // ✅ FIXED: Memoize stats to prevent re-renders
  const stats = useMemo(() => ({
    total: pagination?.total || 0,
    verified: sellers.filter(s => s.verificationStatus === 'VERIFIED').length,
    pending: sellers.filter(s => s.verificationStatus === 'PENDING').length,
    rejected: sellers.filter(s => s.verificationStatus === 'REJECTED').length
  }), [pagination?.total, sellers]);

  // ✅ UPDATED: Filtering is now handled by API query parameters
  // No need for client-side filtering as it's done server-side
  const filteredSellers = sellers;

  // ✅ FIXED: Define action handlers BEFORE columns to prevent circular dependency
  const handleView = useCallback((record: any) => {
    setSelectedFarmer(record);
    setShowViewModal(true);
  }, []);

  const handleEdit = useCallback((record: any) => {
    setSelectedFarmer(record);
    setShowEditModal(true);
  }, []);

  const handleDocuments = useCallback((record: any) => {
    setSelectedFarmer(record);
    setShowDocumentModal(true);
  }, []);

  const handleStatusChange = useCallback((record: any, action: 'activate' | 'deactivate' | 'suspend') => {
    setSelectedFarmer(record);
    setStatusAction(action);
    setShowStatusModal(true);
  }, []);

  const handleViewFarms = useCallback((record: any) => {
    navigate(`/farms?sellerId=${record.sellerId}`);
  }, [navigate]);

  const handleVerify = useCallback(async (record: any) => {
    if (error) {
      // When API is not available, show demo behavior
      alert(`Demo: Seller ${record.personalInfo?.name} would be verified in real system`);
      return;
    }

    if (!currentAdmin) {
      alert('Admin user not found. Please login again.');
      return;
    }

    try {
      await verifySeller({
        sellerId: record.sellerId,
        verified: true,
        verificationNotes: 'Verified by admin',
        verifiedBy: currentAdmin.id
      }).unwrap();

      refetch(); // Refresh the data
    } catch (apiError) {
      console.error('Failed to verify seller:', apiError);
      alert('Failed to verify seller. Please try again.');
    }
  }, [error, currentAdmin, verifySeller, refetch]);

  // ✅ FIXED: Memoize columns to prevent infinite re-renders
  const columns = useMemo(() => [
    {
      key: 'seller',
      label: 'Seller Details',
      render: (value: any, record: any) => {
        // ✅ FIXED: DataTable passes (value, record) - we need the record (second parameter)
        if (!record) {
          return <div className="text-gray-500">No data</div>;
        }

        return (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="font-semibold text-gray-900">
                {record?.personalInfo?.name || 'N/A'}
              </div>
              <div className="flex items-center space-x-1 text-sm text-gray-500">
                <Mail className="w-3 h-3" />
                <span>{record?.personalInfo?.email || 'N/A'}</span>
              </div>
              <div className="flex items-center space-x-1 text-sm text-gray-500">
                <Phone className="w-3 h-3" />
                <span>{record?.personalInfo?.contact || 'N/A'}</span>
              </div>
              <div className="text-xs text-gray-400">ID: {record?.sellerId || 'N/A'}</div>
            </div>
          </div>
        );
      }
    },
    {
      key: 'location',
      label: 'Location',
      render: (value: any, record: any) => {
        if (!record) {
          return <div className="text-gray-500">No data</div>;
        }

        return (
          <div className="flex items-center space-x-1 text-sm">
            <MapPin className="w-4 h-4 text-gray-400" />
            <div>
              <div>{record?.personalInfo?.address?.city || 'N/A'}, {record?.personalInfo?.address?.state || 'N/A'}</div>
              <div className="text-xs text-gray-500">{record?.personalInfo?.address?.pincode || 'N/A'}</div>
              <div className="text-xs text-gray-400">{record?.personalInfo?.address?.country || ''}</div>
            </div>
          </div>
        );
      }
    },
    {
      key: 'verificationStatus',
      label: 'Verification',
      render: (value: any, record: any) => {
        if (!record) {
          return <div className="text-gray-500">No data</div>;
        }

        return (
          <StatusBadge
            status={(record?.verificationStatus || 'PENDING').toLowerCase()}
            className={
              record?.verificationStatus === 'VERIFIED'
                ? 'bg-green-100 text-green-800'
                : record?.verificationStatus === 'PENDING'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800'
            }
          />
        );
      }
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: any, record: any) => {
        if (!record) {
          return <div className="text-gray-500">No data</div>;
        }

        return (
          <StatusBadge
            status={(record?.status || 'INACTIVE').toLowerCase()}
            className={
              record?.status === 'ACTIVE'
                ? 'bg-green-100 text-green-800'
                : record?.status === 'INACTIVE'
                ? 'bg-gray-100 text-gray-800'
                : 'bg-red-100 text-red-800'
            }
          />
        );
      }
    },
    {
      key: 'registrationDate',
      label: 'Registration',
      render: (value: any, record: any) => {
        if (!record) {
          return <div className="text-gray-500">No data</div>;
        }

        return (
          <div className="flex items-center space-x-1 text-sm">
            <Calendar className="w-4 h-4 text-gray-400" />
            <span>{record?.createdAt ? new Date(record.createdAt).toLocaleDateString() : 'N/A'}</span>
          </div>
        );
      }
    },
    {
      key: 'farms',
      label: 'Farms',
      render: (value: any, record: any) => {
        if (!record) {
          return <div className="text-gray-500">No data</div>;
        }

        return (
          <div className="text-sm">
            <span className="font-medium">{record?.farms?.length || 0}</span>
            <span className="text-gray-500 ml-1">farms</span>
          </div>
        );
      }
    },
    {
      key: 'documents',
      label: 'Documents',
      render: (value: any, record: any) => {
        if (!record) {
          return <div className="text-gray-500">No data</div>;
        }

        return (
          <div className="text-sm">
            <div className="flex items-center space-x-1">
              <Shield className="w-3 h-3 text-gray-400" />
              <span className="font-medium">{record?.documents?.certifications?.length || 0}</span>
              <span className="text-gray-500">certs</span>
            </div>
            <div className="text-xs text-gray-400 mt-1">
              {record?.documents?.identityProof ? '✓ ID' : '✗ ID'} |
              {record?.documents?.landOwnership ? ' ✓ Land' : ' ✗ Land'}
            </div>
          </div>
        );
      }
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, record: any) => (
        <div className="flex space-x-1">
          {/* View Details */}
          <button
            onClick={() => handleView(record)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
            title="View Details"
          >
            <Eye className="w-4 h-4" />
          </button>

          {/* Edit Farmer */}
          <button
            onClick={() => handleEdit(record)}
            className="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded"
            title="Edit Farmer"
          >
            <Edit className="w-4 h-4" />
          </button>

          {/* Verify/Unverify */}
          <button
            onClick={() => handleVerify(record)}
            className={`p-1 rounded ${
              record?.verificationStatus === 'VERIFIED'
                ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50'
                : 'text-green-600 hover:text-green-800 hover:bg-green-50'
            }`}
            title={record?.verificationStatus === 'VERIFIED' ? 'Unverify' : 'Verify'}
          >
            {record?.verificationStatus === 'VERIFIED' ? <UserX className="w-4 h-4" /> : <UserCheck className="w-4 h-4" />}
          </button>

          {/* Documents */}
          <button
            onClick={() => handleDocuments(record)}
            className="p-1 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded"
            title="View Documents"
          >
            <Shield className="w-4 h-4" />
          </button>

          {/* Status Actions */}
          {record?.status === 'ACTIVE' ? (
            <button
              onClick={() => handleStatusChange(record, 'suspend')}
              className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
              title="Suspend Farmer"
            >
              <AlertTriangle className="w-4 h-4" />
            </button>
          ) : (
            <button
              onClick={() => handleStatusChange(record, 'activate')}
              className="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded"
              title="Activate Farmer"
            >
              <UserCheck className="w-4 h-4" />
            </button>
          )}

          {/* View Farms */}
          <button
            onClick={() => handleViewFarms(record)}
            className="p-1 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded"
            title="View Farms"
          >
            <Tractor className="w-4 h-4" />
          </button>
        </div>
      )
    }
  ], [handleView, handleEdit, handleVerify, handleDocuments, handleStatusChange, handleViewFarms]); // ✅ FIXED: Add dependencies for memoization

  // ✅ UPDATED: Handle search with API refetch
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(1); // Reset to first page when searching
  }, []);

  // ✅ UPDATED: Handle filter change with API refetch
  const handleFilterChange = useCallback((status: string) => {
    setFilterStatus(status);
    setPage(1); // Reset to first page when filtering
  }, []);

  // Handle row selection
  const handleRowSelection = (selectedRowKeys: string[]) => {
    setSelectedSellers(selectedRowKeys);
  };

  // ✅ FIXED: Add handleDelete for delete functionality
  const handleDelete = useCallback((record: any) => {
    setSellerToDelete(record.sellerId);
    setSelectedFarmer(record);
    setShowDeleteModal(true);
  }, []);

  // ✅ UPDATED: Comprehensive status change functionality
  const confirmStatusChange = async () => {
    if (error) {
      // When API is not available, show demo behavior
      const actionText = statusAction === 'activate' ? 'activated' : statusAction === 'deactivate' ? 'deactivated' : 'suspended';
      alert(`Demo: Seller ${selectedFarmer?.personalInfo?.name} would be ${actionText} in real system`);
      setShowStatusModal(false);
      setSelectedFarmer(null);
      return;
    }

    if (selectedFarmer && currentAdmin) {
      try {
        if (statusAction === 'suspend') {
          await suspendSeller({
            sellerId: selectedFarmer.sellerId,
            status: 'SUSPENDED',
            reason: 'Suspended by admin',
            suspendedBy: currentAdmin.id
          }).unwrap();
        } else {
          await updateSeller({
            sellerId: selectedFarmer.sellerId,
            status: statusAction === 'activate' ? 'ACTIVE' : 'INACTIVE',
            notes: `${statusAction === 'activate' ? 'Activated' : 'Deactivated'} by admin`
          }).unwrap();
        }

        setShowStatusModal(false);
        setSelectedFarmer(null);
        refetch(); // Refresh the data
      } catch (apiError) {
        console.error('Failed to update seller status:', apiError);
        alert('Failed to update seller status. Please try again.');
      }
    }
  };

  // ✅ UPDATED: Using real API for seller suspension with fallback (for delete modal)
  const confirmDelete = async () => {
    if (error) {
      // When API is not available, show demo behavior
      const sellerName = sellers.find(s => s.sellerId === sellerToDelete)?.personalInfo?.name || 'Unknown';
      alert(`Demo: Seller ${sellerName} would be suspended in real system`);
      setShowDeleteModal(false);
      setSellerToDelete(null);
      return;
    }

    if (sellerToDelete && currentAdmin) {
      try {
        await suspendSeller({
          sellerId: sellerToDelete,
          status: 'SUSPENDED',
          reason: 'Suspended by admin',
          suspendedBy: currentAdmin.id
        }).unwrap();

        setShowDeleteModal(false);
        setSellerToDelete(null);
        refetch(); // Refresh the data
      } catch (apiError) {
        console.error('Failed to suspend seller:', apiError);
        alert('Failed to suspend seller. Please try again.');
      }
    }
  };



  // ✅ FIXED: Memoize bulk operations to prevent infinite re-renders
  const handleBulkVerify = useCallback(async () => {
    if (error) {
      alert(`Demo: ${selectedSellers.length} sellers would be verified in real system`);
      setSelectedSellers([]);
      return;
    }

    if (!currentAdmin) {
      alert('Admin user not found. Please login again.');
      return;
    }

    try {
      await Promise.all(
        selectedSellers.map(sellerId =>
          verifySeller({
            sellerId,
            verified: true,
            verificationNotes: 'Bulk verified by admin',
            verifiedBy: currentAdmin.id
          }).unwrap()
        )
      );

      setSelectedSellers([]);
      refetch();
    } catch (apiError) {
      console.error('Failed to bulk verify sellers:', apiError);
      alert('Failed to verify some sellers. Please try again.');
    }
  }, [error, selectedSellers, currentAdmin, verifySeller, refetch]);

  const handleBulkSuspend = useCallback(async () => {
    if (error) {
      alert(`Demo: ${selectedSellers.length} sellers would be suspended in real system`);
      setSelectedSellers([]);
      return;
    }

    if (!currentAdmin) {
      alert('Admin user not found. Please login again.');
      return;
    }

    try {
      await Promise.all(
        selectedSellers.map(sellerId =>
          suspendSeller({
            sellerId,
            status: 'SUSPENDED',
            reason: 'Bulk suspended by admin',
            suspendedBy: currentAdmin.id
          }).unwrap()
        )
      );

      setSelectedSellers([]);
      refetch();
    } catch (apiError) {
      console.error('Failed to bulk suspend sellers:', apiError);
      alert('Failed to suspend some sellers. Please try again.');
    }
  }, [error, selectedSellers, currentAdmin, suspendSeller, refetch]);

  const handleBulkActivate = useCallback(async () => {
    if (error) {
      alert(`Demo: ${selectedSellers.length} sellers would be activated in real system`);
      setSelectedSellers([]);
      return;
    }

    if (!currentAdmin) {
      alert('Admin user not found. Please login again.');
      return;
    }

    try {
      await Promise.all(
        selectedSellers.map(sellerId =>
          updateSeller({
            sellerId,
            status: 'ACTIVE',
            notes: 'Bulk activated by admin'
          }).unwrap()
        )
      );

      setSelectedSellers([]);
      refetch();
    } catch (apiError) {
      console.error('Failed to bulk activate sellers:', apiError);
      alert('Failed to activate some sellers. Please try again.');
    }
  }, [error, selectedSellers, currentAdmin, updateSeller, refetch]);

  // Export functionality
  const handleExport = (format: 'csv' | 'excel' | 'pdf') => {
    console.log(`Exporting sellers as ${format}`);
    alert(`Exporting ${filteredSellers.length} sellers as ${format.toUpperCase()}`);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Farmers Management</h1>
          <p className="text-gray-600 mt-1">Manage farmer accounts, verification, and KYC status</p>
          {error && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                ⚠️ API not available - showing demo data. Start the admin service at localhost:3002
              </p>
            </div>
          )}
        </div>
        <Button
          onClick={() => navigate('/farmers/create')}
          className="flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Farmer</span>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatsCard
          title="Total Farmers"
          value={stats.total.toString()}
          icon={<Users className="h-6 w-6" />}
          color="blue"
        />
        <StatsCard
          title="Verified"
          value={stats.verified.toString()}
          icon={<UserCheck className="h-6 w-6" />}
          color="green"
        />
        <StatsCard
          title="Pending"
          value={stats.pending.toString()}
          icon={<AlertTriangle className="h-6 w-6" />}
          color="yellow"
        />
        <StatsCard
          title="Rejected"
          value={stats.rejected.toString()}
          icon={<UserX className="h-6 w-6" />}
          color="red"
        />
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="p-6 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Search farmers by name, email, phone, or location..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={filterStatus}
              onChange={(e) => handleFilterChange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">All Status</option>
              <option value="VERIFIED">Verified</option>
              <option value="PENDING">Pending</option>
              <option value="REJECTED">Rejected</option>
            </select>
            <Button
              variant="primary"
              onClick={() => handleExport('csv')}
              className="flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </Button>
          </div>
        </div>

        {selectedSellers.length > 0 && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-blue-800">
                {selectedSellers.length} farmer(s) selected
              </span>
              <div className="flex space-x-2">
                <Button
                  variant="primary"
                  size="small"
                  onClick={handleBulkVerify}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <UserCheck className="w-4 h-4 mr-1" />
                  Verify ({selectedSellers.length})
                </Button>

                <Button
                  variant="primary"
                  size="small"
                  onClick={handleBulkActivate}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Activate ({selectedSellers.length})
                </Button>

                <Button
                  variant="primary"
                  size="small"
                  onClick={handleBulkSuspend}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <XCircle className="w-4 h-4 mr-1" />
                  Suspend ({selectedSellers.length})
                </Button>

                <Button
                  variant="primary"
                  size="small"
                  onClick={() => setSelectedSellers([])}
                  className="text-gray-600 border-gray-300 hover:bg-gray-50"
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Enhanced Data Table */}
      <DataTable
        columns={columns}
        data={filteredSellers}
        loading={isLoading}
        onRowClick={(record: any)=>{handleRowSelection([record.sellerId])}}
      />

      {/* ✅ ADDED: Pagination Controls */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-4 mt-6">
          <Button
            variant="primary"
            disabled={!pagination.hasPrev}
            onClick={() => setPage(page - 1)}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {pagination.page} of {pagination.totalPages} ({pagination.total} total)
          </span>
          <Button
            variant="primary"
            disabled={!pagination.hasNext}
            onClick={() => setPage(page + 1)}
          >
            Next
          </Button>
        </div>
      )}

      {/* ✅ ADDED: Error handling */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">
            Failed to load sellers. Please try again.
          </p>
          <Button
            variant="primary"
            onClick={() => refetch()}
            className="mt-2"
          >
            Retry
          </Button>
        </div>
      )}

      {/* ✅ UPDATED: Suspend Confirmation Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Confirm Suspension"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to suspend this farmer? They will not be able to access their account until reactivated.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="primary"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Suspend
            </Button>
          </div>
        </div>
      </Modal>

      {/* ✅ NEW: Status Change Confirmation Modal */}
      <Modal
        open={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        title={`Confirm ${statusAction === 'activate' ? 'Activation' : statusAction === 'deactivate' ? 'Deactivation' : 'Suspension'}`}
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to {statusAction} farmer "{selectedFarmer?.personalInfo?.name}"?
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="primary"
              onClick={() => setShowStatusModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={confirmStatusChange}
              className={
                statusAction === 'activate'
                  ? 'bg-green-600 hover:bg-green-700'
                  : statusAction === 'deactivate'
                  ? 'bg-gray-600 hover:bg-gray-700'
                  : 'bg-red-600 hover:bg-red-700'
              }
            >
              {statusAction === 'activate' ? 'Activate' : statusAction === 'deactivate' ? 'Deactivate' : 'Suspend'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* ✅ NEW: View Farmer Details Modal */}
      <Modal
        open={showViewModal}
        onClose={() => setShowViewModal(false)}
        title="Farmer Details"
        size="large"
      >
        {selectedFarmer && (
          <div className="space-y-6">
            {/* Personal Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Personal Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.name || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.email || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Contact</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.contact || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Farmer ID</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.sellerId || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Address</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Address Line 1</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.address?.addressLine1 || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Address Line 2</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.address?.addressLine2 || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">City</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.address?.city || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">State</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.address?.state || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Pincode</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.address?.pincode || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Country</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.personalInfo?.address?.country || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Status Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Status & Verification</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Account Status</label>
                  <div className="mt-1">
                    <StatusBadge
                      status={(selectedFarmer.status || 'INACTIVE').toLowerCase()}
                      className={
                        selectedFarmer.status === 'ACTIVE'
                          ? 'bg-green-100 text-green-800'
                          : selectedFarmer.status === 'INACTIVE'
                          ? 'bg-gray-100 text-gray-800'
                          : 'bg-red-100 text-red-800'
                      }
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Verification Status</label>
                  <div className="mt-1">
                    <StatusBadge
                      status={(selectedFarmer.verificationStatus || 'PENDING').toLowerCase()}
                      className={
                        selectedFarmer.verificationStatus === 'VERIFIED'
                          ? 'bg-green-100 text-green-800'
                          : selectedFarmer.verificationStatus === 'PENDING'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Registration Date</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedFarmer.createdAt ? new Date(selectedFarmer.createdAt).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            {/* Documents */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Documents</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Identity Proof</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.documents?.identityProof || 'Not provided'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Land Ownership</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.documents?.landOwnership || 'Not provided'}</p>
                </div>
                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700">Certifications</label>
                  <div className="mt-1">
                    {selectedFarmer.documents?.certifications?.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {selectedFarmer.documents.certifications.map((cert: string, index: number) => (
                          <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {cert}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">No certifications</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Bank Details */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Bank Details</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Bank Name</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.bankDetails?.bankName || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Account Number</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedFarmer.bankDetails?.accountNumber ?
                      `****${selectedFarmer.bankDetails.accountNumber.slice(-4)}` : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">IFSC Code</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.bankDetails?.ifscCode || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Farms</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedFarmer.farms?.length || 0} farms registered</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                variant="primary"
                onClick={() => {
                  setShowViewModal(false);
                  handleEdit(selectedFarmer);
                }}
                className="bg-green-600 hover:bg-green-700"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Farmer
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  setShowViewModal(false);
                  handleViewFarms(selectedFarmer);
                }}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Tractor className="w-4 h-4 mr-2" />
                View Farms
              </Button>
              <Button
                variant="primary"
                onClick={() => setShowViewModal(false)}
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default EnhancedSellersPage;
