import React from 'react';
import { Card, DashboardGrid, StatsCard } from '@befarmer-platform/shared-ui';
import { 
  TrendingUp, 
  Users, 
  Sprout, 
  LineChart, 
  BarChart2, 
  PieChart,
  IndianRupee,
  Map,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

const AnalyticsPage: React.FC = () => {
  // Mock data - in real app, this would come from API
  const metrics = {
    totalRevenue: '₹45.8L',
    revenueGrowth: '+22%',
    activeSellers: '156',
    sellerGrowth: '+15%',
    totalFarms: '89',
    farmGrowth: '+8%',
    avgYield: '92%',
    yieldGrowth: '+5%'
  };

  const topPerformers = [
    { name: 'Green Valley Farms', revenue: '₹5.2L', growth: '+28%' },
    { name: 'Sunshine Agro', revenue: '₹4.8L', growth: '+22%' },
    { name: 'Nature\'s Best', revenue: '₹4.2L', growth: '+18%' },
    { name: 'Organic Fields', revenue: '₹3.9L', growth: '+15%' }
  ];

  const cropDistribution = [
    { crop: 'Tomatoes', area: '45 ha', percentage: '35%' },
    { crop: 'Potatoes', area: '35 ha', percentage: '27%' },
    { crop: 'Onions', area: '25 ha', percentage: '19%' },
    { crop: 'Others', area: '24 ha', percentage: '19%' }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <DashboardGrid>
        <StatsCard
          title="Total Revenue"
          value={metrics.totalRevenue}
          trend={metrics.revenueGrowth}
          subtitle="vs last month"
          icon={<IndianRupee className="w-6 h-6" />}
          color="orange"
          trendDirection="up"
        />
        <StatsCard
          title="Active Sellers"
          value={metrics.activeSellers}
          trend={metrics.sellerGrowth}
          subtitle="vs last month"
          icon={<Users className="w-6 h-6" />}
          color="blue"
          trendDirection="up"
        />
        <StatsCard
          title="Total Farms"
          value={metrics.totalFarms}
          trend={metrics.farmGrowth}
          subtitle="vs last month"
          icon={<Sprout className="w-6 h-6" />}
          color="green"
          trendDirection="up"
        />
        <StatsCard
          title="Average Yield"
          value={metrics.avgYield}
          trend={metrics.yieldGrowth}
          subtitle="vs last month"
          icon={<TrendingUp className="w-6 h-6" />}
          color="purple"
          trendDirection="up"
        />
      </DashboardGrid>

      {/* Revenue Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Revenue Analysis</h3>
              <p className="text-sm text-gray-500">Monthly revenue breakdown</p>
            </div>
            <select className="px-3 py-2 border border-gray-200 rounded-lg text-sm">
              <option>Last 7 days</option>
              <option>Last 30 days</option>
              <option>Last 3 months</option>
            </select>
          </div>
          <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
            {/* Revenue Chart will go here */}
            <p className="text-gray-500">Revenue Chart Placeholder</p>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Top Performing Sellers</h3>
              <p className="text-sm text-gray-500">Based on monthly revenue</p>
            </div>
            <LineChart className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {topPerformers.map((seller, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <Users className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{seller.name}</p>
                    <p className="text-sm text-gray-500">{seller.revenue} revenue</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-green-600">
                  <ArrowUpRight className="w-4 h-4" />
                  <span className="text-sm font-medium">{seller.growth}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Crop Distribution</h3>
              <p className="text-sm text-gray-500">By farm area</p>
            </div>
            <PieChart className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {cropDistribution.map((crop, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full" style={{ 
                    backgroundColor: ['#22C55E', '#3B82F6', '#F97316', '#6B7280'][index] 
                  }} />
                  <span className="text-sm font-medium text-gray-900">{crop.crop}</span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-500">{crop.area}</span>
                  <span className="text-sm font-medium text-gray-900">{crop.percentage}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Regional Analysis</h3>
              <p className="text-sm text-gray-500">Performance by region</p>
            </div>
            <Map className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {[
              { region: 'Karnataka', farms: 45, growth: '+12%' },
              { region: 'Maharashtra', farms: 32, growth: '+8%' },
              { region: 'Tamil Nadu', farms: 28, growth: '+15%' },
              { region: 'Gujarat', farms: 24, growth: '+10%' }
            ].map((region, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">{region.region}</span>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-500">{region.farms} farms</span>
                  <div className="flex items-center gap-1 text-green-600">
                    <ArrowUpRight className="w-3 h-3" />
                    <span className="text-sm">{region.growth}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Growth Trends</h3>
              <p className="text-sm text-gray-500">Year over year comparison</p>
            </div>
            <LineChart className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {[
              { metric: 'Revenue Growth', value: '+32%', trend: 'up' },
              { metric: 'Seller Growth', value: '+28%', trend: 'up' },
              { metric: 'Farm Expansion', value: '+18%', trend: 'up' },
              { metric: 'Yield Improvement', value: '+15%', trend: 'up' }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">{item.metric}</span>
                <div className="flex items-center gap-2">
                  {item.trend === 'up' ? (
                    <ArrowUpRight className="w-4 h-4 text-green-600" />
                  ) : (
                    <ArrowDownRight className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm font-medium ${
                    item.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {item.value}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Key Insights */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-800">Top Performing Crop</h4>
            <p className="text-sm text-green-600 mt-1">Tomatoes - 95% yield rate</p>
          </div>
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-800">Most Active Region</h4>
            <p className="text-sm text-blue-600 mt-1">Karnataka - 45 farms</p>
          </div>
          <div className="p-4 bg-orange-50 rounded-lg">
            <h4 className="font-medium text-orange-800">Highest Revenue</h4>
            <p className="text-sm text-orange-600 mt-1">Maharashtra - ₹12.5L</p>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <h4 className="font-medium text-purple-800">Growth Potential</h4>
            <p className="text-sm text-purple-600 mt-1">35% projected increase</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AnalyticsPage; 