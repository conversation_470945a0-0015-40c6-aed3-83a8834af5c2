import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Line<PERSON>hart, 
  BarChart, 
  StatsCard, 
  Button,
  Modal 
} from '@befarmer-platform/shared-ui';
import { 
  analyticsService,
  DashboardAnalyticsResponse,
  AnalyticsFilters
} from '@befarmer-platform/shared-utils';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Package, 
  Download, 
  Filter,
  Calendar,
  MapPin,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Sprout,
  ShoppingCart,
  Target,
  Activity
} from 'lucide-react';

const EnhancedAnalyticsPage: React.FC = () => {
  const [data, setData] = useState<DashboardAnalyticsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');
  const [filters, setFilters] = useState<AnalyticsFilters>({});
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  // Fetch analytics data
  const fetchAnalytics = useCallback(async () => {
    setLoading(true);
    try {
      const analyticsFilters: AnalyticsFilters = {
        ...filters,
        dateRange: getDateRange(timeRange)
      };
      
      const response = await analyticsService.getDashboardAnalytics(analyticsFilters);
      if (response.status === 'success' && response.data) {
        setData(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      // Fallback to mock data for demo
      setData({
        metrics: {
          totalRevenue: { current: 2450000, previous: 2180000, change: 270000, changePercentage: 12.4, trend: 'up' },
          totalOrders: { current: 3420, previous: 2980, change: 440, changePercentage: 14.8, trend: 'up' },
          totalSellers: { current: 1250, previous: 1150, change: 100, changePercentage: 8.7, trend: 'up' },
          totalCrops: { current: 8750, previous: 8200, change: 550, changePercentage: 6.7, trend: 'up' },
          averageOrderValue: { current: 716, previous: 732, change: -16, changePercentage: -2.2, trend: 'down' },
          conversionRate: { current: 3.2, previous: 2.9, change: 0.3, changePercentage: 10.3, trend: 'up' }
        },
        charts: {
          revenue: [
            { date: '2024-01-01', value: 180000 },
            { date: '2024-02-01', value: 195000 },
            { date: '2024-03-01', value: 210000 },
            { date: '2024-04-01', value: 225000 },
            { date: '2024-05-01', value: 240000 },
            { date: '2024-06-01', value: 255000 }
          ],
          orders: [
            { date: '2024-01-01', value: 520 },
            { date: '2024-02-01', value: 580 },
            { date: '2024-03-01', value: 620 },
            { date: '2024-04-01', value: 680 },
            { date: '2024-05-01', value: 720 },
            { date: '2024-06-01', value: 780 }
          ],
          users: [
            { date: '2024-01-01', value: 950 },
            { date: '2024-02-01', value: 1020 },
            { date: '2024-03-01', value: 1080 },
            { date: '2024-04-01', value: 1150 },
            { date: '2024-05-01', value: 1200 },
            { date: '2024-06-01', value: 1250 }
          ],
          crops: [
            { date: '2024-01-01', value: 7800 },
            { date: '2024-02-01', value: 8100 },
            { date: '2024-03-01', value: 8300 },
            { date: '2024-04-01', value: 8500 },
            { date: '2024-05-01', value: 8650 },
            { date: '2024-06-01', value: 8750 }
          ]
        },
        insights: [
          { type: 'positive', title: 'Revenue Growth', description: 'Revenue increased by 12.4% compared to last period', value: 12.4, change: 2.1 },
          { type: 'positive', title: 'New Sellers', description: 'Added 100 new verified sellers this month', value: 100, change: 15 },
          { type: 'neutral', title: 'Order Value', description: 'Average order value slightly decreased', value: -2.2, change: -0.5 }
        ],
        alerts: [
          { severity: 'medium', title: 'Inventory Alert', message: 'Low stock levels detected for popular crops', timestamp: '2024-06-15T10:30:00Z' },
          { severity: 'low', title: 'Performance', message: 'System performance is optimal', timestamp: '2024-06-15T09:15:00Z' }
        ]
      });
    } finally {
      setLoading(false);
    }
  }, [timeRange, filters]);

  // Refresh data
  const refreshData = async () => {
    setRefreshing(true);
    try {
      await fetchAnalytics();
    } finally {
      setRefreshing(false);
    }
  };

  // Get date range based on selection
  const getDateRange = (range: string) => {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (range) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }
    
    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  };

  // Export analytics data
  const handleExport = async (format: 'csv' | 'excel' | 'pdf') => {
    setExportLoading(true);
    try {
      const analyticsFilters: AnalyticsFilters = {
        ...filters,
        dateRange: getDateRange(timeRange)
      };
      
      // For demo purposes, we'll just log the export action
      console.log(`Exporting analytics as ${format} with filters:`, analyticsFilters);
      
      // Simulate export delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In real implementation, this would download the file
      alert(`Analytics exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Failed to export analytics:', error);
    } finally {
      setExportLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!loading && !refreshing) {
        refreshData();
      }
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [loading, refreshing]);

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-80 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Enhanced Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">Real-time insights and performance metrics</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFiltersModal(true)}
            className="flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
          </Button>
          
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={refreshData}
            disabled={refreshing}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </Button>
          
          <div className="relative group">
            <Button
              variant="primary"
              size="sm"
              disabled={exportLoading}
              className="flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>{exportLoading ? 'Exporting...' : 'Export'}</span>
            </Button>
            <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
              <button
                onClick={() => handleExport('csv')}
                className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50"
              >
                Export as CSV
              </button>
              <button
                onClick={() => handleExport('excel')}
                className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50"
              >
                Export as Excel
              </button>
              <button
                onClick={() => handleExport('pdf')}
                className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50"
              >
                Export as PDF
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {data.alerts && data.alerts.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
            <h3 className="font-medium text-yellow-800">System Alerts</h3>
          </div>
          <div className="space-y-2">
            {data.alerts.slice(0, 3).map((alert, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-yellow-700">{alert.message}</span>
                <span className="text-xs text-yellow-600">{new Date(alert.timestamp).toLocaleString()}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <StatsCard
          title="Total Revenue"
          value={`₹${(data.metrics.totalRevenue.current / 100000).toFixed(1)}L`}
          trend={data.metrics.totalRevenue.changePercentage > 0 ? `+${data.metrics.totalRevenue.changePercentage.toFixed(1)}%` : `${data.metrics.totalRevenue.changePercentage.toFixed(1)}%`}
          icon={<DollarSign className="w-6 h-6" />}
          color="green"
        />
        
        <StatsCard
          title="Total Sellers"
          value={data.metrics.totalSellers.current.toString()}
          trend={data.metrics.totalSellers.changePercentage > 0 ? `+${data.metrics.totalSellers.changePercentage.toFixed(1)}%` : `${data.metrics.totalSellers.changePercentage.toFixed(1)}%`}
          icon={<Users className="w-6 h-6" />}
          color="blue"
        />
        
        <StatsCard
          title="Total Orders"
          value={data.metrics.totalOrders.current.toString()}
          trend={data.metrics.totalOrders.changePercentage > 0 ? `+${data.metrics.totalOrders.changePercentage.toFixed(1)}%` : `${data.metrics.totalOrders.changePercentage.toFixed(1)}%`}
          icon={<ShoppingCart className="w-6 h-6" />}
          color="orange"
        />
        
        <StatsCard
          title="Total Crops"
          value={data.metrics.totalCrops.current.toString()}
          trend={data.metrics.totalCrops.changePercentage > 0 ? `+${data.metrics.totalCrops.changePercentage.toFixed(1)}%` : `${data.metrics.totalCrops.changePercentage.toFixed(1)}%`}
          icon={<Sprout className="w-6 h-6" />}
          color="purple"
        />

        <StatsCard
          title="Avg Order Value"
          value={`₹${data.metrics.averageOrderValue.current.toFixed(0)}`}
          trend={data.metrics.averageOrderValue.changePercentage > 0 ? `+${data.metrics.averageOrderValue.changePercentage.toFixed(1)}%` : `${data.metrics.averageOrderValue.changePercentage.toFixed(1)}%`}
          icon={<Target className="w-6 h-6" />}
          color="indigo"
        />

        <StatsCard
          title="Conversion Rate"
          value={`${data.metrics.conversionRate.current.toFixed(1)}%`}
          trend={data.metrics.conversionRate.changePercentage > 0 ? `+${data.metrics.conversionRate.changePercentage.toFixed(1)}%` : `${data.metrics.conversionRate.changePercentage.toFixed(1)}%`}
          icon={<Activity className="w-6 h-6" />}
          color="pink"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <Card className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Revenue Trends</h2>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Revenue</span>
            </div>
          </div>
          <LineChart
            data={data.charts.revenue}
            xKey="date"
            yKey="value"
            color="#10B981"
            height={300}
          />
        </Card>

        {/* Orders Chart */}
        <Card className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Order Trends</h2>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Orders</span>
            </div>
          </div>
          <BarChart
            data={data.charts.orders}
            xKey="date"
            yKey="value"
            color="#F97316"
            height={300}
          />
        </Card>
      </div>

      {/* Insights */}
      {data.insights && data.insights.length > 0 && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Key Insights</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {data.insights.map((insight, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  insight.type === 'positive' ? 'bg-green-100' :
                  insight.type === 'negative' ? 'bg-red-100' : 'bg-yellow-100'
                }`}>
                  {insight.type === 'positive' ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : insight.type === 'negative' ? (
                    <AlertTriangle className="w-4 h-4 text-red-600" />
                  ) : (
                    <Clock className="w-4 h-4 text-yellow-600" />
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{insight.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{insight.description}</p>
                  {insight.value && (
                    <p className={`text-sm font-medium mt-2 ${
                      insight.type === 'positive' ? 'text-green-600' :
                      insight.type === 'negative' ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {insight.value > 0 ? '+' : ''}{insight.value}
                      {insight.title.includes('Growth') || insight.title.includes('Rate') ? '%' : ''}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Filters Modal */}
      <Modal
        isOpen={showFiltersModal}
        onClose={() => setShowFiltersModal(false)}
        title="Analytics Filters"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Location
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="">All Locations</option>
              <option value="punjab">Punjab</option>
              <option value="haryana">Haryana</option>
              <option value="up">Uttar Pradesh</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Crop Types
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="">All Crops</option>
              <option value="vegetables">Vegetables</option>
              <option value="fruits">Fruits</option>
              <option value="grains">Grains</option>
            </select>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowFiltersModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                setShowFiltersModal(false);
                fetchAnalytics();
              }}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedAnalyticsPage;
