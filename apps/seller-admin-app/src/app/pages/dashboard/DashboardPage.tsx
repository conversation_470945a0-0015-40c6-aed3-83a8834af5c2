import React, { useMemo, useCallback } from 'react';
import {
  useGetAdminDashboardQuery,
  useGetSystemHealthQuery,
  useGetSystemStatsQuery
} from '@befarmer-platform/shared-utils';
import { DashboardGrid, StatsCard, StatusBadge, Button } from '@befarmer-platform/shared-ui';
import { Users, Tractor, Sprout, ShoppingBag, Activity, ServerIcon, AlertTriangle, TrendingUp } from 'lucide-react';

const DashboardPage: React.FC = () => {
  // ✅ UPDATED: Using RTK Query hooks for admin dashboard data
  const {
    data: dashboardResponse,
    isLoading: dashboardLoading,
    error: dashboardError,
    refetch: refetchDashboard
  } = useGetAdminDashboardQuery();

  const {
    data: systemHealthResponse,
    isLoading: healthLoading,
    error: healthError
  } = useGetSystemHealthQuery();

  const {
    data: systemStatsResponse,
    isLoading: statsLoading,
    error: statsError
  } = useGetSystemStatsQuery();

  const isLoading = dashboardLoading || healthLoading || statsLoading;
  const hasError = dashboardError || healthError || statsError;

  // ✅ FIXED: Memoize mock data to prevent re-renders
  const mockStats = useMemo(() => ({
    totalUsers: 1250,
    totalSellers: 450,
    totalFarms: 320,
    totalCrops: 890,
    totalOrders: 156,
    revenue: {
      today: 45000,
      thisMonth: 1200000,
      thisYear: 12500000
    },
    growth: {
      users: 12,
      sellers: 8,
      revenue: 15
    }
  }), []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-green-500" />
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="p-6">
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <p className="text-red-600">Failed to load dashboard data</p>
          </div>
          <Button
            onClick={handleRetry}
            className="mt-2"
            variant="primary"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // ✅ FIXED: Memoize computed data to prevent re-renders
  const dashboard = useMemo(() => dashboardResponse?.data, [dashboardResponse?.data]);
  const systemHealth = useMemo(() => systemHealthResponse?.data, [systemHealthResponse?.data]);
  const systemStats = useMemo(() =>
    hasError ? mockStats : (systemStatsResponse?.data || mockStats),
    [hasError, mockStats, systemStatsResponse?.data]
  );

  // ✅ FIXED: Memoize click handlers to prevent re-renders
  const handleRetry = useCallback(() => {
    refetchDashboard();
  }, [refetchDashboard]);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor and manage your agricultural platform</p>
          {hasError && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                ⚠️ API not available - showing demo data. Start the admin service at localhost:3002
              </p>
            </div>
          )}
        </div>
        <div className="flex space-x-4">
          <Button variant="primary" className="bg-green-500 hover:bg-green-600">
            Export Report
          </Button>
          <Button variant="primary" className="bg-orange-500 hover:bg-orange-600">
            System Health
          </Button>
        </div>
      </div>

      {/* ✅ UPDATED: Stats Grid using real API data */}
      <DashboardGrid>
        <StatsCard
          title="Total Users"
          value={systemStats?.totalUsers?.toString() || '0'}
          icon={<Users className="h-6 w-6" />}
          trend={systemStats?.growth?.users ? `+${systemStats.growth.users}%` : undefined}
          color="blue"
        />
        <StatsCard
          title="Total Sellers"
          value={systemStats?.totalSellers?.toString() || '0'}
          icon={<Users className="h-6 w-6" />}
          trend={systemStats?.growth?.sellers ? `+${systemStats.growth.sellers}%` : undefined}
          color="green"
        />
        <StatsCard
          title="Total Farms"
          value={systemStats?.totalFarms?.toString() || '0'}
          icon={<Tractor className="h-6 w-6" />}
          color="orange"
        />
        <StatsCard
          title="Active Crops"
          value={systemStats?.totalCrops?.toString() || '0'}
          icon={<Sprout className="h-6 w-6" />}
          color="green"
        />
        <StatsCard
          title="Total Orders"
          value={systemStats?.totalOrders?.toString() || '0'}
          icon={<ShoppingBag className="h-6 w-6" />}
          color="purple"
        />
        <StatsCard
          title="Revenue (Today)"
          value={systemStats?.revenue?.today ? `₹${systemStats.revenue.today.toLocaleString()}` : '₹0'}
          icon={<TrendingUp className="h-6 w-6" />}
          trend={systemStats?.growth?.revenue ? `+${systemStats.growth.revenue}%` : undefined}
          color="green"
        />
      </DashboardGrid>

      {/* ✅ UPDATED: Recent Activities using real API data */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-4">Recent Activities</h2>
        <div className="space-y-4">
          {dashboard?.recentActivity && dashboard.recentActivity.length > 0 ? (
            dashboard.recentActivity.map((activity) => (
              <div
                key={activity.id}
                className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg"
              >
                <Activity className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium text-gray-900">{activity.description}</p>
                  <p className="text-sm text-gray-500">
                    {new Date(activity.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No recent activities</p>
            </div>
          )}
        </div>
      </div>

      {/* ✅ UPDATED: System Alerts */}
      {dashboard?.alerts && dashboard.alerts.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">System Alerts</h2>
          <div className="space-y-4">
            {dashboard.alerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-4 rounded-lg border-l-4 ${
                  alert.type === 'error'
                    ? 'bg-red-50 border-red-500'
                    : alert.type === 'warning'
                    ? 'bg-yellow-50 border-yellow-500'
                    : 'bg-blue-50 border-blue-500'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <AlertTriangle className={`h-5 w-5 ${
                    alert.type === 'error'
                      ? 'text-red-600'
                      : alert.type === 'warning'
                      ? 'text-yellow-600'
                      : 'text-blue-600'
                  }`} />
                  <div>
                    <p className="font-medium text-gray-900">{alert.title}</p>
                    <p className="text-sm text-gray-600">{alert.message}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* ✅ UPDATED: System Health using real API data */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-4">System Health</h2>
        {systemHealth ? (
          <div className="space-y-4">
            {/* Overall Status */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Overall Status</p>
                  <p className="text-sm text-gray-500">
                    Uptime: {Math.floor(systemHealth.uptime / 3600)}h {Math.floor((systemHealth.uptime % 3600) / 60)}m
                  </p>
                </div>
                <StatusBadge
                  status={systemHealth.status}
                  className={
                    systemHealth.status === 'healthy'
                      ? 'bg-green-100 text-green-800'
                      : systemHealth.status === 'degraded'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }
                />
              </div>
            </div>

            {/* Services Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(systemHealth.services).map(([service, health]) => (
                <div
                  key={service}
                  className="p-4 bg-gray-50 rounded-lg flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <ServerIcon className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">
                        {service.charAt(0).toUpperCase() + service.slice(1)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {health.responseTime ? `${health.responseTime}ms` : 'N/A'}
                      </p>
                    </div>
                  </div>
                  <StatusBadge
                    status={health.status}
                    className={
                      health.status === 'up'
                        ? 'bg-green-100 text-green-800'
                        : health.status === 'degraded'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }
                  />
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <ServerIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>System health data unavailable</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardPage; 