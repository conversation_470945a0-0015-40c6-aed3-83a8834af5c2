import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { 
  useAuth, 
  validators, 
  validateForm, 
  useAppDispatch, 
  selectAuthLoading,
  selectAuthError, 
  selectUserRole, 
  useAdminLoginMutation, 
  setCredentials
} from '@befarmer-platform/shared-utils';
import { Button, Input, Card, Alert, Checkbox } from '@befarmer-platform/shared-ui';
import { useSelector } from 'react-redux';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Define error type to fix linter errors
interface ApiError {
  data?: {
    error?: {
      message?: string;
    };
  };
  message?: string;
}

export const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();
  const isLoading = useSelector(selectAuthLoading);
  const userRole = useSelector(selectUserRole);
  const authError = useSelector(selectAuthError);
  
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get the redirect URL from location state or default to appropriate dashboard
  const from = (location.state as { from?: { pathname: string } })?.from?.pathname || '/';

  // Redirect if already authenticated - only on mount or when auth state changes
  useEffect(() => {
    if (userRole === 'admin' && !isSubmitting) {
      navigate('/', { replace: true });
    }
  }, [userRole, navigate, isSubmitting]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateLoginForm = (): boolean => {
    const rules = {
      email: [
        { required: true },
        { custom: validators.email }
      ],
      password: [
        { required: true },
        { minLength: 6 }
      ],
    };

    const { isValid, errors: validationErrors } = validateForm(formData, rules);
    setErrors(validationErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    if (!validateLoginForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await login(formData.email, formData.password);
      // Navigation will be handled by the useEffect hook
    } catch (error) {
      const apiError = error as ApiError;
      setErrors({
        submit: apiError?.data?.error?.message || apiError?.message || 'Login failed. Please check your credentials and try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="w-24 h-24 bg-white rounded-2xl flex items-center justify-center shadow-lg transform transition-all hover:scale-105 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-green-400 to-green-600"></div>
              <span className="relative text-white text-4xl transform hover:scale-110 transition-transform duration-200" role="img" aria-label="Seedling">🌱</span>
            </div>
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-4 bg-gradient-to-t from-green-100 to-transparent opacity-50 blur-lg"></div>
          </div>
        </div>
        <h2 className="text-center text-3xl font-extrabold text-gray-900 tracking-tight">
          BeFarma Admin Portal
        </h2>
        <p className="mt-3 text-center text-lg text-gray-600 max-w-md mx-auto">
          Sign in to access the admin dashboard
        </p>
      </div>

      {/* Login Form */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md px-4">
        {authError && (
          <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-r-md">
            <div className="flex items-center">
              <span className="text-red-500 mr-3" role="img" aria-label="Error">⚠️</span>
              <p className="text-sm text-red-700">{authError}</p>
            </div>
          </div>
        )}
        <Card className="py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form onSubmit={handleSubmit} className="space-y-6">
            {errors.submit && (
              <Alert variant="error" className="mb-4" message={errors.submit} />
            )}
            <div className="space-y-6">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <div className="relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-400">@</span>
                  </div>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    error={errors.email}
                    helperText={errors.email}
                    className="pl-10 block w-full rounded-md border-gray-300 focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Password Field */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                    Password
                  </label>
                  <Link to="/auth/forgot-password" className="text-xs text-green-600 hover:text-green-500">
                    Forgot password?
                  </Link>
                </div>
                <div className="relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-400" role="img" aria-label="Lock">🔒</span>
                  </div>
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    error={errors.password}
                    helperText={errors.password}
                    className="pl-10 pr-10 block w-full rounded-md border-gray-300 focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    placeholder="••••••••"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? (
                      <span role="img" aria-label="Hide">👁️</span>
                    ) : (
                      <span role="img" aria-label="Show">👁️‍🗨️</span>
                    )}
                  </button>
                </div>
              </div>

              {/* Remember Me */}
              <div className="flex items-center">
                <Checkbox
                  id="rememberMe"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={(e) => setFormData({...formData, rememberMe: e.target.checked})}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>
            </div>

            {/* Submit Button */}
            <div>
              <Button
                type="submit"
                variant="primary"
                size="large"
                className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
                disabled={isLoading || isSubmitting}
                loading={isLoading || isSubmitting}
              >
                {isLoading || isSubmitting ? 'Authenticating...' : 'Sign in to Admin Dashboard'}
              </Button>
            </div>
          </form>
          {/* Admin Notice */}
          <div className="mt-6 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-r-md">
            <p className="text-sm text-blue-700">
              <span className="font-medium">Note:</span> This is the admin portal. For seller access, please use the seller app.
            </p>
          </div>
        </Card>
      </div>

      {/* Footer */}
      <div className="mt-8 text-center text-sm text-gray-600">
        <p>
          By signing in, you agree to our{' '}
          <Link to="/terms" className="text-green-600 hover:text-green-500">
            Terms of Service
          </Link>{' '}
          and{' '}
          <Link to="/privacy" className="text-green-600 hover:text-green-500">
            Privacy Policy
          </Link>
        </p>
      </div>
    </div>
  );
};

export default LoginPage;