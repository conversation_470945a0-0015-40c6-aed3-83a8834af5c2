import React, { useState } from 'react';
import { useN<PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Input, <PERSON><PERSON>, Card, Alert } from '@befarmer-platform/shared-ui';
import { Eye, EyeOff, User, Mail, Phone, MapPin, Key } from 'lucide-react';
import axios from 'axios';

interface AdminRegisterForm {
  name: string;
  email: string;
  contact: string;
  addressLine1: string;
  state: string;
  city: string;
  pincode: string;
  password: string;
}

const initialForm: AdminRegisterForm = {
  name: '',
  email: '',
  contact: '',
  addressLine1: '',
  state: '',
  city: '',
  pincode: '',
  password: '',
};

const API_URL = '/api/v1/admin/sellers';

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const [form, setForm] = useState<AdminRegisterForm>(initialForm);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const validate = () => {
    const newErrors: Record<string, string> = {};
    if (!form.name) newErrors.name = 'Name is required';
    if (!form.email) newErrors.email = 'Email is required';
    else if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email)) newErrors.email = 'Invalid email';
    if (!form.contact) newErrors.contact = 'Contact is required';
    else if (!/^\d{10}$/.test(form.contact)) newErrors.contact = 'Contact must be 10 digits';
    if (!form.addressLine1) newErrors.addressLine1 = 'Address is required';
    if (!form.state) newErrors.state = 'State is required';
    if (!form.city) newErrors.city = 'City is required';
    if (!form.pincode) newErrors.pincode = 'PIN code is required';
    else if (!/^\d{6}$/.test(form.pincode)) newErrors.pincode = 'PIN code must be 6 digits';
    if (!form.password) newErrors.password = 'Password is required';
    else if (form.password.length < 8) newErrors.password = 'Password must be at least 8 characters';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setApiError('');
    setSuccess(false);
    if (!validate()) return;
    setIsSubmitting(true);
    try {
      await axios.post(API_URL, {
        name: form.name,
        email: form.email,
        contact: form.contact,
        address: {
          addressLine1: form.addressLine1,
          state: form.state,
          city: form.city,
          pincode: form.pincode,
        },
        password: form.password,
      });
      setSuccess(true);
      setTimeout(() => navigate('/auth/login'), 2000);
    } catch (err: any) {
      setApiError(err?.response?.data?.error?.message || 'Registration failed.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center space-y-4">
          <div className="mx-auto h-16 w-16 bg-gradient-to-br from-green-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
            <User className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900">Admin Registration</h2>
          <p className="text-gray-600">Create an admin account to manage the AgriTech platform</p>
        </div>
        <Card className="p-8 shadow-md border border-gray-100">
          <form className="space-y-6" onSubmit={handleSubmit} autoComplete="off">
            {apiError && <Alert variant="error" message={apiError} />}
            {success && <Alert variant="success" message="Registration successful! Redirecting..." />}
            <Input
              id="admin-name"
              name="name"
              label="Full Name"
              value={form.name}
              onChange={handleChange}
              error={errors.name}
              required
              placeholder="Enter your name"
              fullWidth
              startIcon={<User className="h-4 w-4" />}
            />
            <Input
              id="admin-email"
              name="email"
              label="Email Address"
              value={form.email}
              onChange={handleChange}
              error={errors.email}
              required
              placeholder="Enter your email"
              fullWidth
              startIcon={<Mail className="h-4 w-4" />}
            />
            <Input
              id="admin-contact"
              name="contact"
              label="Contact Number"
              value={form.contact}
              onChange={handleChange}
              error={errors.contact}
              required
              placeholder="10-digit mobile number"
              fullWidth
              startIcon={<Phone className="h-4 w-4" />}
            />
            <Input
              id="admin-addressLine1"
              name="addressLine1"
              label="Address"
              value={form.addressLine1}
              onChange={handleChange}
              error={errors.addressLine1}
              required
              placeholder="Street, locality, etc."
              fullWidth
              startIcon={<MapPin className="h-4 w-4" />}
            />
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Input
                id="admin-state"
                name="state"
                label="State"
                value={form.state}
                onChange={handleChange}
                error={errors.state}
                required
                placeholder="State"
                fullWidth
              />
              <Input
                id="admin-city"
                name="city"
                label="City"
                value={form.city}
                onChange={handleChange}
                error={errors.city}
                required
                placeholder="City"
                fullWidth
              />
              <Input
                id="admin-pincode"
                name="pincode"
                label="PIN Code"
                value={form.pincode}
                onChange={handleChange}
                error={errors.pincode}
                required
                placeholder="6-digit PIN"
                fullWidth
              />
            </div>
            <Input
              id="admin-password"
              name="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={form.password}
              onChange={handleChange}
              error={errors.password}
              required
              placeholder="Create a strong password"
              fullWidth
              startIcon={<Key className="h-4 w-4" />}
              endIcon={showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              clickableEndIcon
              onEndIconClick={() => setShowPassword(v => !v)}
            />
            <Button
              type="submit"
              variant="primary"
              size="large"
              className="w-full"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Registering...' : 'Register'}
            </Button>
            <div className="text-center pt-2">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <Link to="/auth/login" className="font-medium text-green-600 hover:text-green-500 transition-colors duration-200">
                  Sign in instead
                </Link>
              </p>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default RegisterPage; 