import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { validators, validateForm, ValidationResult } from '@befarmer-platform/shared-utils';
import { Button, Input, Card, Alert } from '@befarmer-platform/shared-ui';

interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

export const ResetPasswordPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const resetToken = searchParams.get('token');
  
  const [formData, setFormData] = useState<ResetPasswordFormData>({
    password: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetStatus, setResetStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [resetMessage, setResetMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Validate reset token on mount
  useEffect(() => {
    if (!resetToken) {
      setResetStatus('error');
      setResetMessage('Invalid or expired reset token. Please request a new password reset.');
    }
  }, [resetToken]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateResetForm = (): boolean => {
    const rules = {
      password: [
        { required: true },
        { minLength: 8 },
        { 
          custom: (value: string): ValidationResult => {
            const hasUppercase = /[A-Z]/.test(value);
            const hasLowercase = /[a-z]/.test(value);
            const hasNumber = /[0-9]/.test(value);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);
            
            if (!hasUppercase) return { isValid: false, message: 'Password must contain at least one uppercase letter' };
            if (!hasLowercase) return { isValid: false, message: 'Password must contain at least one lowercase letter' };
            if (!hasNumber) return { isValid: false, message: 'Password must contain at least one number' };
            if (!hasSpecial) return { isValid: false, message: 'Password must contain at least one special character' };
            
            return { isValid: true };
          }
        }
      ],
      confirmPassword: [
        { required: true },
        { custom: (value: string): ValidationResult => ({ 
          isValid: value === formData.password,
          message: value === formData.password ? undefined : 'Passwords do not match'
        })}
      ],
    };

    const { isValid, errors: validationErrors } = validateForm(formData, rules);
    setErrors(validationErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!resetToken) {
      return;
    }

    if (!validateResetForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual API call to reset password
      // This is a mock implementation
      await new Promise(resolve => setTimeout(resolve, 2000));

      setResetStatus('success');
      setResetMessage(
        'Your password has been successfully reset. ' +
        'You can now log in with your new password.'
      );

      // Redirect to login page after 3 seconds
      setTimeout(() => {
        navigate('/auth/login');
      }, 3000);
    } catch (error) {
      setResetStatus('error');
      setResetMessage(
        error instanceof Error 
          ? error.message 
          : 'Failed to reset password. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
            <span className="text-white text-2xl font-bold">🌱</span>
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
          Reset Your Password
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Enter your new password below
        </p>
      </div>

      {/* Reset Form */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card className="py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {resetStatus === 'success' ? (
            <div className="text-center">
              <div className="mb-4 text-green-500">
                <span className="text-4xl">✅</span>
              </div>
              <Alert variant="success" message={resetMessage} />
              <p className="mt-4 text-sm text-gray-600">
                Redirecting to login page...
              </p>
            </div>
          ) : resetStatus === 'error' && !resetToken ? (
            <div className="text-center">
              <Alert variant="error" message={resetMessage} />
              <div className="mt-6">
                <Link
                  to="/auth/forgot-password"
                  className="text-green-600 hover:text-green-500"
                >
                  Request New Reset Link
                </Link>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {resetStatus === 'error' && (
                <Alert variant="error" message={resetMessage} />
              )}

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  New Password
                </label>
                <div className="mt-1">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    error={errors.password}
                    placeholder="Enter your new password"
                    startIcon={<span>🔒</span>}
                    endIcon={
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? '👁️' : '👁️‍🗨️'}
                      </button>
                    }
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Password must contain at least 8 characters, including uppercase, lowercase,
                  numbers, and special characters.
                </p>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  Confirm New Password
                </label>
                <div className="mt-1">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    error={errors.confirmPassword}
                    placeholder="Confirm your new password"
                    startIcon={<span>🔒</span>}
                  />
                </div>
              </div>

              <div>
                <Button
                  type="submit"
                  variant="primary"
                  size="large"
                  className="w-full"
                  loading={isSubmitting}
                  disabled={isSubmitting || !resetToken}
                >
                  {isSubmitting ? 'Resetting Password...' : 'Reset Password'}
                </Button>
              </div>

              <div className="text-center">
                <Link
                  to="/auth/login"
                  className="text-sm text-green-600 hover:text-green-500"
                >
                  Back to Login
                </Link>
              </div>
            </form>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ResetPasswordPage; 