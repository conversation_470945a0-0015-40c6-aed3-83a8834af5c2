import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { validators, validateForm } from '@befarmer-platform/shared-utils';
import { Button, Input, Card, Alert } from '@befarmer-platform/shared-ui';

interface ForgotPasswordFormData {
  email: string;
}

export const ForgotPasswordPage: React.FC = () => {
  const [formData, setFormData] = useState<ForgotPasswordFormData>({ email: '' });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetStatus, setResetStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [resetMessage, setResetMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateResetForm = (): boolean => {
    const rules = {
      email: [
        { required: true },
        { custom: validators.email }
      ],
    };

    const { isValid, errors: validationErrors } = validateForm(formData, rules);
    setErrors(validationErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetStatus('idle');
    setResetMessage('');

    if (!validateResetForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual API call to request password reset
      // This is a mock implementation
      await new Promise(resolve => setTimeout(resolve, 2000));

      setResetStatus('success');
      setResetMessage(
        'Password reset instructions have been sent to your email. ' +
        'Please check your inbox and follow the instructions to reset your password.'
      );
    } catch (error) {
      setResetStatus('error');
      setResetMessage(
        error instanceof Error 
          ? error.message 
          : 'Failed to send reset instructions. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
            <span className="text-white text-2xl font-bold">🌱</span>
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
          Reset Your Password
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Enter your email address and we'll send you instructions to reset your password
        </p>
      </div>

      {/* Reset Form */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <Card className="py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {resetStatus === 'success' ? (
            <div className="text-center">
              <div className="mb-4 text-green-500">
                <span className="text-4xl">✉️</span>
              </div>
              <Alert variant="success" message={resetMessage} />
              <div className="mt-6">
                <Link
                  to="/auth/login"
                  className="text-green-600 hover:text-green-500"
                >
                  Return to Login
                </Link>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {resetStatus === 'error' && (
                <Alert variant="error" message={resetMessage} />
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <div className="mt-1">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    error={errors.email}
                    placeholder="Enter your email"
                    startIcon={<span>📧</span>}
                  />
                </div>
              </div>

              <div>
                <Button
                  type="submit"
                  variant="primary"
                  size="large"
                  className="w-full"
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending Instructions...' : 'Send Reset Instructions'}
                </Button>
              </div>

              <div className="text-center">
                <Link
                  to="/auth/login"
                  className="text-sm text-green-600 hover:text-green-500"
                >
                  Back to Login
                </Link>
              </div>
            </form>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ForgotPasswordPage; 