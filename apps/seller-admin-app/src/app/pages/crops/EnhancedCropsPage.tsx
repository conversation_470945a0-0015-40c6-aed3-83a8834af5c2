import {
  AdvancedDataTable,
  AdvancedSearch,
  Button,
  Modal,
  StatsCard
} from '@befarmer-platform/shared-ui';
import {
  AdminCropsListParams,
  useApproveCropMutation,
  useGetAllCropsAdminQuery
} from '@befarmer-platform/shared-utils';
import {
  AlertCircle,
  Calendar,
  Leaf,
  MapPin,
  Package,
  Plus,
  Sprout,
  TrendingUp
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface CropResponse {
  _id: string;
  cropId: string;
  plotId: string;
  farmId: string;
  sellerId: string;
  name: string;
  type: string;
  variety: string;
  plantingDate: string;
  expectedHarvestDate: string;
  growthStage: string;
  health: {
    status: string;
    issues: string[];
    lastCheck: string;
  };
  yield: {
    expected: number;
    actual: number;
    unit: string;
  };
  metadata: {
    cropCategory: string;
    farmingMethod: string;
    irrigationMethod: string;
    harvestSeason: string;
    pestDiseaseStatus: string;
    storageMethod: string;
    nutrientManagement: string;
    waterSource: string;
    pesticideUsage: string;
    seedType: string;
    harvestingMethod: string;
  };
  certifications: string[];
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse {
  success: boolean;
  data: {
    crops: CropResponse[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  };
}

const EnhancedCropsPage: React.FC = () => {
  const navigate = useNavigate();
  const [crops, setCrops] = useState<CropResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCrops, setSelectedCrops] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [searchParams, setSearchParams] = useState<AdminCropsListParams>({
    page: 1,
    limit: 20
  });
  const [stats, setStats] = useState({
    total: 0,
    planting: 0,
    growing: 0,
    harvested: 0,
    total_yield: 0
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [cropToDelete, setCropToDelete] = useState<string | null>(null);

  // Use RTK Query hook for fetching crops
  const { data: cropsData, isLoading } = useGetAllCropsAdminQuery({
    page: pagination.current,
    limit: pagination.pageSize,
    ...searchParams
  });

  // Use RTK Query hook for approving/deleting crops
  const [approveCrop] = useApproveCropMutation();

  // Search filters configuration
  const searchFilters = [
    {
      key: 'type',
      label: 'Crop Type',
      type: 'select' as const,
      options: [
        { label: 'Vegetables', value: 'Vegetables' },
        { label: 'Fruits', value: 'Fruits' },
        { label: 'Grains', value: 'Grains' },
        { label: 'Pulses', value: 'Pulses' },
        { label: 'Spices', value: 'Spices' }
      ],
      icon: <Leaf className="h-4 w-4" />
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { label: 'Planted', value: 'PLANTED' },
        { label: 'Growing', value: 'GROWING' },
        { label: 'Flowering', value: 'FLOWERING' },
        { label: 'Ready to Harvest', value: 'READY_TO_HARVEST' },
        { label: 'Harvested', value: 'HARVESTED' },
        { label: 'Sold', value: 'SOLD' }
      ],
      icon: <TrendingUp className="h-4 w-4" />
    },
    {
      key: 'growthStage',
      label: 'Growth Stage',
      type: 'select' as const,
      options: [
        { label: 'Seedling', value: 'SEEDLING' },
        { label: 'Vegetative', value: 'VEGETATIVE' },
        { label: 'Flowering', value: 'FLOWERING' },
        { label: 'Fruiting', value: 'FRUITING' },
        { label: 'Maturity', value: 'MATURITY' }
      ],
      icon: <Sprout className="h-4 w-4" />
    },
    {
      key: 'healthStatus',
      label: 'Health Status',
      type: 'select' as const,
      options: [
        { label: 'Healthy', value: 'HEALTHY' },
        { label: 'Disease', value: 'DISEASE' },
        { label: 'Pest Attack', value: 'PEST_ATTACK' },
        { label: 'Nutrient Deficiency', value: 'NUTRIENT_DEFICIENCY' },
        { label: 'Water Stress', value: 'WATER_STRESS' }
      ],
      icon: <AlertCircle className="h-4 w-4" />
    },
    {
      key: 'farmingMethod',
      label: 'Farming Method',
      type: 'select' as const,
      options: [
        { label: 'Organic', value: 'Organic' },
        { label: 'Conventional', value: 'Conventional' },
        { label: 'Hydroponic', value: 'Hydroponic' },
        { label: 'Greenhouse', value: 'Greenhouse' }
      ]
    },
    {
      key: 'location',
      label: 'Location',
      type: 'location' as const,
      placeholder: 'Enter state or district',
      icon: <MapPin className="h-4 w-4" />
    },
    {
      key: 'plantingDate',
      label: 'Planting Date',
      type: 'daterange' as const,
      icon: <Calendar className="h-4 w-4" />
    },
    {
      key: 'harvestDate',
      label: 'Expected Harvest',
      type: 'daterange' as const,
      icon: <Calendar className="h-4 w-4" />
    },
    {
      key: 'pricing',
      label: 'Price Range (₹/kg)',
      type: 'numberrange' as const,
      placeholder: 'Enter price range'
    }
  ];

  // Table columns configuration
  const columns = [
    {
      key: 'crop',
      title: 'Crop Details',
      dataIndex: 'name',
      width: 250,
      sortable: true,
      render: (name: string, record: CropResponse) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
            <Sprout className="w-5 h-5 text-white" />
          </div>
          <div>
            <div className="font-semibold text-gray-900">{name}</div>
            <div className="text-sm text-gray-500">{record.variety}</div>
            <div className="text-xs text-gray-400">{record.type}</div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Growth Stage',
      dataIndex: 'growthStage',
      width: 120,
      sortable: true,
      filterable: true,
      filterType: 'select' as const,
      filterOptions: [
        { label: 'Planting', value: 'PLANTING' },
        { label: 'Growing', value: 'GROWING' },
        { label: 'Harvested', value: 'HARVESTED' }
      ],
      render: (status: string) => {
        const statusConfig = {
          'PLANTING': { color: 'bg-yellow-100 text-yellow-800', icon: <Sprout className="w-3 h-3" /> },
          'GROWING': { color: 'bg-green-100 text-green-800', icon: <TrendingUp className="w-3 h-3" /> },
          'HARVESTED': { color: 'bg-gray-100 text-gray-800', icon: <Package className="w-3 h-3" /> }
        };
        
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['PLANTING'];
        
        return (
          <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
            {config.icon}
            <span>{status}</span>
          </div>
        );
      }
    },
    {
      key: 'yield',
      title: 'Expected Yield',
      dataIndex: ['yield'],
      width: 150,
      render: (yieldData: { expected: number; unit: string }) => (
        <div className="text-sm">
          <div className="font-medium">{yieldData.expected.toLocaleString()}</div>
          <div className="text-xs text-gray-500">{yieldData.unit}</div>
        </div>
      )
    },
    {
      key: 'farmingMethod',
      title: 'Farming Method',
      dataIndex: ['metadata', 'farmingMethod'],
      width: 150,
      render: (method: string) => (
        <div className="text-sm">
          {method}
        </div>
      )
    },
    {
      key: 'plantingDate',
      title: 'Planted',
      dataIndex: 'plantingDate',
      width: 120,
      sortable: true,
      render: (date: string) => (
        <div className="text-sm">
          {new Date(date).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'expectedHarvestDate',
      title: 'Expected Harvest',
      dataIndex: 'expectedHarvestDate',
      width: 120,
      sortable: true,
      render: (date: string) => (
        <div className="text-sm">
          {new Date(date).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'certifications',
      title: 'Certifications',
      dataIndex: 'certifications',
      width: 150,
      render: (certs: string[]) => (
        <div className="flex flex-wrap gap-1">
          {certs.map((cert, index) => (
            <span 
              key={index}
              className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
            >
              {cert}
            </span>
          ))}
        </div>
      )
    }
  ];

  // Update local state when data is fetched
  useEffect(() => {
    if (cropsData) {
      const response = cropsData as ApiResponse;
      if (response.success && response.data) {
        setCrops(response.data.crops);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total || 0
        }));
        
        // Update stats based on growth stages
        const totalYield = response.data.crops.reduce((sum, crop) => sum + crop.yield.expected, 0);
        setStats({
          total: response.data.pagination.total || 0,
          planting: response.data.crops.filter(c => c.growthStage === 'PLANTING').length,
          growing: response.data.crops.filter(c => c.growthStage === 'GROWING').length,
          harvested: response.data.crops.filter(c => c.growthStage === 'HARVESTED').length,
          total_yield: totalYield
        });
      }
    }
  }, [cropsData]);

  // Handle search
  const handleSearch = (query: string, filters: Record<string, any>) => {
    const newSearchParams: AdminCropsListParams = {
      page: 1,
      limit: pagination.pageSize,
      status: filters.status,
      farmId: filters.farmId,
      sellerId: filters.sellerId
    };
    
    setSearchParams(newSearchParams);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // Handle pagination
  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize
    }));
  };

  // Handle row selection
  const handleRowSelection = (selectedRowKeys: string[], selectedRows: CropResponse[]) => {
    setSelectedCrops(selectedRowKeys);
  };

  // Action handlers
  const handleView = (record: CropResponse) => {
    navigate(`/crops/${record.cropId}`);
  };

  const handleEdit = (record: CropResponse) => {
    navigate(`/crops/${record.cropId}/edit`);
  };

  const handleDelete = (record: CropResponse) => {
    setCropToDelete(record.cropId);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (cropToDelete) {
      try {
        await approveCrop({
          cropId: cropToDelete,
          status: 'DELETED',
          approvalNotes: 'Deleted by admin',
          approvedBy: 'admin' // You should get this from your auth state
        });
        setShowDeleteModal(false);
        setCropToDelete(null);
      } catch (error) {
        console.error('Failed to delete crop:', error);
      }
    }
  };

  // Export functionality
  const handleExport = async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      // TODO: Implement export functionality using admin API
      console.log('Export not implemented yet');
    } catch (error) {
      console.error('Failed to export crops:', error);
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Crops Management</h1>
          <p className="text-gray-600 mt-1">Monitor crop growth, health, and yield tracking</p>
        </div>
        <Button
          onClick={() => navigate('/crops/create')}
          className="flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Crop</span>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <StatsCard
          title="Total Crops"
          value={stats.total.toString()}
          icon={<Sprout className="h-6 w-6" />}
          color="purple"
        />
        <StatsCard
          title="Planting"
          value={stats.planting.toString()}
          icon={<Sprout className="h-6 w-6" />}
          color="yellow"
        />
        <StatsCard
          title="Growing"
          value={stats.growing.toString()}
          icon={<TrendingUp className="h-6 w-6" />}
          color="green"
        />
        <StatsCard
          title="Harvested"
          value={stats.harvested.toString()}
          icon={<Package className="h-6 w-6" />}
          color="blue"
        />
        <StatsCard
          title="Total Yield (kg)"
          value={stats.total_yield.toLocaleString()}
          icon={<Package className="h-6 w-6" />}
          color="indigo"      
        />
      </div>

      {/* Advanced Search */}
      <AdvancedSearch
        placeholder="Search crops by name, variety, type, or location..."
        filters={searchFilters}
        onSearch={handleSearch}
        loading={loading}
        className="mb-6"
      />

      {/* Advanced Data Table */}
      <AdvancedDataTable
        data={crops}
        columns={columns as any[]}
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: handlePaginationChange
        }}
        rowSelection={{
          selectedRowKeys: selectedCrops,
          onChange: handleRowSelection
        }}
        actions={{
          view: handleView,
          edit: handleEdit,
          delete: handleDelete
        }}
        searchable={false}
        exportable={true}
        onExport={handleExport}
        rowKey="cropId"
        emptyText="No crops found. Try adjusting your search criteria."
      />

      {/* Delete Confirmation Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Confirm Delete"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to delete this crop? This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="primary"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedCropsPage;
