import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, Button, StatusBadge } from '@befarmer-platform/shared-ui';
import { 
  Leaf,
  Calendar,
  Droplets,
  ThermometerSun,
  Sprout,
  Edit3,
  ArrowLeft,
  BarChart,
  Map,
  Users,
  Clock,
  Sun,
  CloudRain,
  Wind
} from 'lucide-react';

interface CropDetails {
  id: string;
  name: string;
  type: string;
  season: 'Kharif' | 'Rabi' | 'Zaid';
  growthDuration: number;
  waterRequirement: 'Low' | 'Medium' | 'High';
  temperature: string;
  soilType: string;
  totalPlots: number;
  totalArea: number;
  expectedYield: string;
  status: 'active' | 'upcoming' | 'completed';
  description: string;
  currentStage: string;
  daysRemaining: number;
  healthStatus: 'Healthy' | 'Warning' | 'Critical';
  weather: {
    temperature: string;
    humidity: string;
    rainfall: string;
    windSpeed: string;
  };
  plots: Array<{
    id: string;
    location: string;
    area: number;
    farmer: string;
    plantingDate: string;
    status: string;
  }>;
}

const CropDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // Mock data - replace with API call
  const cropDetails: CropDetails = {
    id: '1',
    name: 'Wheat',
    type: 'Cereal',
    season: 'Rabi',
    growthDuration: 120,
    waterRequirement: 'Medium',
    temperature: '20-25°C',
    soilType: 'Loamy',
    totalPlots: 5,
    totalArea: 25,
    expectedYield: '30 quintals/acre',
    status: 'active',
    description: 'High-yield wheat variety suitable for Rabi season cultivation.',
    currentStage: 'Flowering',
    daysRemaining: 45,
    healthStatus: 'Healthy',
    weather: {
      temperature: '22°C',
      humidity: '65%',
      rainfall: '2.5mm',
      windSpeed: '10km/h'
    },
    plots: [
      {
        id: 'P1',
        location: 'North Field',
        area: 5,
        farmer: 'Rajesh Kumar',
        plantingDate: '2024-01-15',
        status: 'Healthy'
      },
      {
        id: 'P2',
        location: 'South Field',
        area: 8,
        farmer: 'Amit Singh',
        plantingDate: '2024-01-18',
        status: 'Warning'
      }
    ]
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      case 'completed':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="secondary"
              icon={<ArrowLeft className="w-5 h-5" />}
              onClick={() => navigate('/crops')}
            >
              Back to Crops
            </Button>
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-2xl font-semibold text-gray-900">{cropDetails.name}</h1>
                <StatusBadge
                  type="custom"
                  status={cropDetails.status}
                  customColor={getStatusBadgeVariant(cropDetails.status)}
                />
              </div>
              <p className="text-gray-500 mt-1">{cropDetails.type} • {cropDetails.season} Season</p>
            </div>
          </div>
          <Button
            variant="secondary"
            icon={<Edit3 className="w-5 h-5" />}
            onClick={() => navigate(`/crops/${id}/edit`)}
            className="text-green-600 hover:text-green-700"
          >
            Edit Crop
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
            <div className="text-green-600 text-sm font-medium">Growth Stage</div>
            <div className="text-xl font-semibold text-gray-900 mt-1">{cropDetails.currentStage}</div>
            <div className="text-sm text-gray-500 mt-1">{cropDetails.daysRemaining} days remaining</div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <div className="text-blue-600 text-sm font-medium">Total Area</div>
            <div className="text-xl font-semibold text-gray-900 mt-1">{cropDetails.totalArea} acres</div>
            <div className="text-sm text-gray-500 mt-1">Across {cropDetails.totalPlots} plots</div>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg border border-orange-100">
            <div className="text-orange-600 text-sm font-medium">Expected Yield</div>
            <div className="text-xl font-semibold text-gray-900 mt-1">{cropDetails.expectedYield}</div>
            <div className="text-sm text-gray-500 mt-1">Per acre average</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
            <div className="text-purple-600 text-sm font-medium">Health Status</div>
            <div className="text-xl font-semibold text-gray-900 mt-1">{cropDetails.healthStatus}</div>
            <div className="text-sm text-gray-500 mt-1">Overall condition</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Growing Requirements */}
        <Card className="lg:col-span-1">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Growing Requirements</h2>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Calendar className="w-5 h-5 text-gray-400 mt-1" />
                <div>
                  <p className="font-medium text-gray-700">Growing Season</p>
                  <p className="text-gray-600">{cropDetails.season}</p>
                  <p className="text-sm text-gray-500">{cropDetails.growthDuration} days duration</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Droplets className="w-5 h-5 text-blue-500 mt-1" />
                <div>
                  <p className="font-medium text-gray-700">Water Requirement</p>
                  <p className="text-gray-600">{cropDetails.waterRequirement}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <ThermometerSun className="w-5 h-5 text-orange-500 mt-1" />
                <div>
                  <p className="font-medium text-gray-700">Temperature Range</p>
                  <p className="text-gray-600">{cropDetails.temperature}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Sprout className="w-5 h-5 text-green-500 mt-1" />
                <div>
                  <p className="font-medium text-gray-700">Soil Type</p>
                  <p className="text-gray-600">{cropDetails.soilType}</p>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Current Weather */}
        <Card className="lg:col-span-2">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Current Weather Conditions</h2>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 rounded-full bg-orange-50 flex items-center justify-center mx-auto mb-2">
                  <Sun className="w-6 h-6 text-orange-500" />
                </div>
                <p className="font-medium text-gray-900">{cropDetails.weather.temperature}</p>
                <p className="text-sm text-gray-500">Temperature</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mx-auto mb-2">
                  <Droplets className="w-6 h-6 text-blue-500" />
                </div>
                <p className="font-medium text-gray-900">{cropDetails.weather.humidity}</p>
                <p className="text-sm text-gray-500">Humidity</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-full bg-indigo-50 flex items-center justify-center mx-auto mb-2">
                  <CloudRain className="w-6 h-6 text-indigo-500" />
                </div>
                <p className="font-medium text-gray-900">{cropDetails.weather.rainfall}</p>
                <p className="text-sm text-gray-500">Rainfall</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 rounded-full bg-gray-50 flex items-center justify-center mx-auto mb-2">
                  <Wind className="w-6 h-6 text-gray-500" />
                </div>
                <p className="font-medium text-gray-900">{cropDetails.weather.windSpeed}</p>
                <p className="text-sm text-gray-500">Wind Speed</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Plot Details */}
      <Card>
        <div className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Plot Details</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-100">
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Location</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Area</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Farmer</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Planting Date</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Status</th>
                </tr>
              </thead>
              <tbody>
                {cropDetails.plots.map((plot) => (
                  <tr key={plot.id} className="border-b border-gray-50 hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Map className="w-4 h-4 text-gray-400" />
                        <span className="font-medium text-gray-900">{plot.location}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-gray-900">{plot.area} acres</span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{plot.farmer}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900">{new Date(plot.plantingDate).toLocaleDateString()}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <StatusBadge
                        type="custom"
                        status={plot.status}
                        customColor={getStatusBadgeVariant(plot.status)}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CropDetailsPage; 