import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, DataTable, But<PERSON>, StatusBadge, Spinner, type Column } from '@befarmer-platform/shared-ui';
import { 
  Leaf,
  Plus, 
  Filter, 
  Search, 
  Sprout,
  Download,
  BarChart,
  Calendar,
  Droplets,
  Sun,
  ThermometerSun,
  Eye
} from 'lucide-react';

interface Crop {
  id: string;
  name: string;
  type: string;
  season: 'Kharif' | 'Rabi' | 'Zaid';
  growthDuration: number; // in days
  waterRequirement: 'Low' | 'Medium' | 'High';
  temperature: string;
  soilType: string;
  totalPlots: number;
  totalArea: number; // in acres
  expectedYield: string;
  status: 'active' | 'upcoming' | 'completed';
}

const CropsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeason, setSelectedSeason] = useState<string>('all');
  const [isLoading] = useState(false);

  // Mock data - replace with API call
  const crops: Crop[] = [
    {
      id: '1',
      name: 'Wheat',
      type: 'Cereal',
      season: 'Rabi',
      growthDuration: 120,
      waterRequirement: 'Medium',
      temperature: '20-25°C',
      soilType: 'Loamy',
      totalPlots: 5,
      totalArea: 25,
      expectedYield: '30 quintals/acre',
      status: 'active'
    },
    {
      id: '2',
      name: 'Rice',
      type: 'Cereal',
      season: 'Kharif',
      growthDuration: 90,
      waterRequirement: 'High',
      temperature: '25-30°C',
      soilType: 'Clay',
      totalPlots: 8,
      totalArea: 40,
      expectedYield: '25 quintals/acre',
      status: 'upcoming'
    },
    {
      id: '3',
      name: 'Cotton',
      type: 'Fiber',
      season: 'Kharif',
      growthDuration: 150,
      waterRequirement: 'Medium',
      temperature: '25-35°C',
      soilType: 'Black',
      totalPlots: 3,
      totalArea: 15,
      expectedYield: '15 quintals/acre',
      status: 'completed'
    }
  ];

  const getStatusBadgeVariant = (status: Crop['status']) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'upcoming':
        return 'warning';
      case 'completed':
        return 'info';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Crop',
      render: (value: string, crop: Crop) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-50 to-emerald-50 flex items-center justify-center border border-gray-100">
            <Leaf className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{value}</p>
            <p className="text-sm text-gray-500">{crop.type}</p>
          </div>
        </div>
      )
    },
    {
      key: 'season',
      label: 'Growing Season',
      render: (value: string, crop: Crop) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="font-medium text-gray-700">{value}</span>
          <span className="text-sm text-gray-500">({crop.growthDuration} days)</span>
        </div>
      )
    },
    {
      key: 'requirements',
      label: 'Requirements',
      render: (_: any, crop: Crop) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Droplets className="w-4 h-4 text-blue-500" />
            <span>Water: {crop.waterRequirement}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <ThermometerSun className="w-4 h-4 text-orange-500" />
            <span>Temp: {crop.temperature}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Sprout className="w-4 h-4 text-green-500" />
            <span>Soil: {crop.soilType}</span>
          </div>
        </div>
      )
    },
    {
      key: 'plots',
      label: 'Cultivation',
      render: (_: any, crop: Crop) => (
        <div>
          <p className="font-medium text-gray-900">{crop.totalPlots} plots</p>
          <p className="text-sm text-gray-500">{crop.totalArea} acres</p>
        </div>
      )
    },
    {
      key: 'expectedYield',
      label: 'Expected Yield',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <BarChart className="w-4 h-4 text-gray-400" />
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: Crop['status']) => (
        <StatusBadge
          type="custom"
          status={value}
          customColor={getStatusBadgeVariant(value)}
        />
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      width: '100px',
      render: (_: any, crop: Crop) => (
        <div className="w-full flex items-center justify-center gap-2 px-2">
            <Eye className="size-6 cursor-pointer text-gray-500 hover:text-gray-700" onClick={(e) => {
              e.stopPropagation();
              navigate(`/crops/${crop.id}`);
            }}
             />
        </div>
      )
    }
  ];

  const filteredCrops = crops.filter((crop) => {
    const matchesSearch = 
      crop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      crop.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      crop.soilType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSeason = selectedSeason === 'all' || crop.season.toLowerCase() === selectedSeason.toLowerCase();
    return matchesSearch && matchesSeason;
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Crops</h1>
            <p className="text-gray-500 mt-1">Manage and monitor agricultural crops across all farms</p>
          </div>
          <Button
            variant="primary"
            icon={<Plus className="w-5 h-5" />}
            onClick={() => navigate('/crops/create')}
            className="w-full sm:w-auto bg-green-600 hover:bg-green-700 shadow-sm"
          >
            Add New Crop
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
            <div className="text-green-600 text-sm font-medium">Total Crops</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">{crops.length}</div>
          </div>
          <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-100">
            <div className="text-emerald-600 text-sm font-medium">Active Crops</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">
              {crops.filter(c => c.status === 'active').length}
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <div className="text-blue-600 text-sm font-medium">Total Area</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">
              {crops.reduce((acc, c) => acc + c.totalArea, 0)} acres
            </div>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg border border-orange-100">
            <div className="text-orange-600 text-sm font-medium">Total Plots</div>
            <div className="text-2xl font-semibold text-gray-900 mt-1">
              {crops.reduce((acc, c) => acc + c.totalPlots, 0)} plots
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 w-full sm:max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by crop name, type, or soil..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex flex-wrap gap-3 w-full sm:w-auto">
            <select
              value={selectedSeason}
              onChange={(e) => setSelectedSeason(e.target.value)}
              className="px-4 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white min-w-[140px]"
            >
              <option value="all">All Seasons</option>
              <option value="kharif">Kharif</option>
              <option value="rabi">Rabi</option>
              <option value="zaid">Zaid</option>
            </select>
            
            <Button
              variant="secondary"
              icon={<Filter className="w-5 h-5" />}
              className="whitespace-nowrap"
            >
              More Filters
            </Button>
            
            <Button
              variant="secondary"
              icon={<Download className="w-5 h-5" />}
              className="whitespace-nowrap"
            >
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Crops Table */}
      <Card>
        {isLoading ? (
          <div className="flex items-center justify-center p-12">
            <Spinner size="large" className="text-green-500" />
            <p className="ml-3 text-gray-500">Loading crops...</p>
          </div>
        ) : filteredCrops.length === 0 ? (
          <div className="p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-50 flex items-center justify-center">
              <Leaf className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Crops Found</h3>
            <p className="text-gray-500 mb-6">
              {searchTerm || selectedSeason !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first crop'}
            </p>
            {!searchTerm && selectedSeason === 'all' && (
              <Button
                variant="primary"
                icon={<Plus className="w-5 h-5" />}
                onClick={() => navigate('/crops/create')}
                className="bg-green-600 hover:bg-green-700"
              >
                Add Your First Crop
              </Button>
            )}
          </div>
        ) : (
          <>
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  {filteredCrops.length} Crop{filteredCrops.length !== 1 ? 's' : ''}
                </h3>
                <Button
                  variant="secondary"
                  icon={<Plus className="w-5 h-5" />}
                  onClick={() => navigate('/crops/create')}
                  className="text-green-600 hover:text-green-700"
                >
                  Add Crop
                </Button>
              </div>
            </div>
            <DataTable
              columns={columns}
              data={filteredCrops}
              emptyMessage="No crops found"
              sortable
              className="[&_tr:hover]:bg-green-50 [&_td]:py-4 [&_td]:align-middle"
              onRowClick={(item: Crop) => navigate(`/crops/${item.id}`)}
            />
          </>
        )}
      </Card>
    </div>
  );
};

export default CropsPage; 