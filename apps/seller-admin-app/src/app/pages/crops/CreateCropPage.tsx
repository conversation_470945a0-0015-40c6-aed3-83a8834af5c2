import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Card, 
  Button, 
  Input,
  Alert
} from '@befarmer-platform/shared-ui';
import { 
  Leaf,
  Calendar,
  Droplets,
  ThermometerSun,
  Sprout,
  Save,
  ArrowLeft
} from 'lucide-react';

interface FormData {
  name: string;
  type: string;
  season: string;
  growthDuration: string;
  waterRequirement: string;
  temperature: string;
  soilType: string;
  expectedYield: string;
  description: string;
}

const INITIAL_FORM_DATA: FormData = {
  name: '',
  type: '',
  season: '',
  growthDuration: '',
  waterRequirement: '',
  temperature: '',
  soilType: '',
  expectedYield: '',
  description: ''
};

const CROP_TYPES = [
  'Cereal',
  'Pulse',
  'Oilseed',
  'Fiber',
  'Fodder',
  'Commercial',
  'Vegetable',
  'Fruit'
];

const SEASONS = [
  { value: 'kharif', label: '<PERSON>hari<PERSON> (Monsoon)' },
  { value: 'rabi', label: '<PERSON><PERSON> (Winter)' },
  { value: 'zaid', label: '<PERSON><PERSON> (Summer)' }
];

const WATER_REQUIREMENTS = [
  { value: 'low', label: 'Low (0-500mm)' },
  { value: 'medium', label: 'Medium (500-1000mm)' },
  { value: 'high', label: 'High (>1000mm)' }
];

const SOIL_TYPES = [
  'Alluvial',
  'Black',
  'Red',
  'Laterite',
  'Desert',
  'Mountain',
  'Loamy',
  'Clay',
  'Sandy'
];

const CreateCropPage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>(INITIAL_FORM_DATA);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear error when user types
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.name) newErrors.name = 'Crop name is required';
    if (!formData.type) newErrors.type = 'Crop type is required';
    if (!formData.season) newErrors.season = 'Growing season is required';
    if (!formData.growthDuration) newErrors.growthDuration = 'Growth duration is required';
    else if (isNaN(Number(formData.growthDuration)) || Number(formData.growthDuration) <= 0) {
      newErrors.growthDuration = 'Enter a valid number of days';
    }
    if (!formData.waterRequirement) newErrors.waterRequirement = 'Water requirement is required';
    if (!formData.temperature) newErrors.temperature = 'Temperature range is required';
    if (!formData.soilType) newErrors.soilType = 'Soil type is required';
    if (!formData.expectedYield) newErrors.expectedYield = 'Expected yield is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to crops list
      navigate('/crops');
    } catch (err) {
      setError('Failed to create crop. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="secondary"
          icon={<ArrowLeft className="w-5 h-5" />}
          onClick={() => navigate('/crops')}
        >
          Back to Crops
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-semibold text-gray-900">Add New Crop</h1>
          <p className="text-gray-500 mt-1">Enter crop details and requirements</p>
        </div>
      </div>

      {/* Form Card */}
      <Card className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert
              variant="error"
              title="Error"
              message={error}
              className="mb-6"
            />
          )}

          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Input
                label="Crop Name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                error={errors.name}
                placeholder="Enter crop name"
                startIcon={<Leaf className="w-5 h-5 text-gray-400" />}
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Crop Type</label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-2.5 border ${errors.type ? 'border-red-300' : 'border-gray-200'} rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white`}
                >
                  <option value="">Select crop type</option>
                  {CROP_TYPES.map(type => (
                    <option key={type} value={type.toLowerCase()}>{type}</option>
                  ))}
                </select>
                {errors.type && <p className="mt-1 text-sm text-red-500">{errors.type}</p>}
              </div>
            </div>
          </div>

          {/* Growing Requirements */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Growing Requirements</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Growing Season</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <select
                    name="season"
                    value={formData.season}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2.5 border ${errors.season ? 'border-red-300' : 'border-gray-200'} rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white`}
                  >
                    <option value="">Select growing season</option>
                    {SEASONS.map(season => (
                      <option key={season.value} value={season.value}>{season.label}</option>
                    ))}
                  </select>
                </div>
                {errors.season && <p className="mt-1 text-sm text-red-500">{errors.season}</p>}
              </div>
              <Input
                label="Growth Duration (days)"
                name="growthDuration"
                type="number"
                value={formData.growthDuration}
                onChange={handleInputChange}
                error={errors.growthDuration}
                placeholder="Enter number of days"
                min="1"
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Water Requirement</label>
                <div className="relative">
                  <Droplets className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <select
                    name="waterRequirement"
                    value={formData.waterRequirement}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2.5 border ${errors.waterRequirement ? 'border-red-300' : 'border-gray-200'} rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white`}
                  >
                    <option value="">Select water requirement</option>
                    {WATER_REQUIREMENTS.map(req => (
                      <option key={req.value} value={req.value}>{req.label}</option>
                    ))}
                  </select>
                </div>
                {errors.waterRequirement && <p className="mt-1 text-sm text-red-500">{errors.waterRequirement}</p>}
              </div>
              <Input
                label="Temperature Range"
                name="temperature"
                value={formData.temperature}
                onChange={handleInputChange}
                error={errors.temperature}
                placeholder="e.g. 20-25°C"
                startIcon={<ThermometerSun className="w-5 h-5 text-gray-400" />}
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Soil Type</label>
                <div className="relative">
                  <Sprout className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <select
                    name="soilType"
                    value={formData.soilType}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-2.5 border ${errors.soilType ? 'border-red-300' : 'border-gray-200'} rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white`}
                  >
                    <option value="">Select soil type</option>
                    {SOIL_TYPES.map(type => (
                      <option key={type} value={type.toLowerCase()}>{type}</option>
                    ))}
                  </select>
                </div>
                {errors.soilType && <p className="mt-1 text-sm text-red-500">{errors.soilType}</p>}
              </div>
              <Input
                label="Expected Yield"
                name="expectedYield"
                value={formData.expectedYield}
                onChange={handleInputChange}
                error={errors.expectedYield}
                placeholder="e.g. 30 quintals/acre"
              />
            </div>
          </div>

          {/* Additional Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
            <div className="space-y-4">
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter any additional information about the crop..."
                className="w-full h-32 px-4 py-2.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate('/crops')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isLoading}
              icon={<Save className="w-5 h-5" />}
              className="bg-green-600 hover:bg-green-700"
            >
              Create Crop
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default CreateCropPage; 