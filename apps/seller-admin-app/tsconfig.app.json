{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true}, "exclude": ["out-tsc", "dist", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx", "**/*.stories.ts", "**/*.stories.js", "**/*.stories.jsx", "**/*.stories.tsx", "vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx", "../../libs/shared-utils/src/**/*.ts", "../../libs/shared-utils/src/**/*.tsx", "../../libs/shared-ui/src/**/*.ts", "../../libs/shared-ui/src/**/*.tsx"], "references": [{"path": "../../libs/shared-ui/tsconfig.lib.json"}, {"path": "../../libs/shared-utils/tsconfig.lib.json"}]}