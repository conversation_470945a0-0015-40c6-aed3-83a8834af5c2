{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./out-tsc/vitest", "jsx": "react-jsx", "types": ["vitest/globals", "vitest/importMeta", "vite/client", "node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"]}, "include": ["vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts"], "references": [{"path": "./tsconfig.app.json"}]}