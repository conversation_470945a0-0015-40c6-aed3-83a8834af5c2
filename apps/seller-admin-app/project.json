{"name": "@befarmer-platform/seller-admin-app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/seller-admin-app/src", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "apps/seller-admin-app/dist"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "@befarmer-platform/seller-admin-app:build"}, "configurations": {"development": {"buildTarget": "@befarmer-platform/seller-admin-app:build:development", "hmr": true}, "production": {"buildTarget": "@befarmer-platform/seller-admin-app:build:production", "hmr": false}}}, "lint": {"executor": "nx:run-commands", "options": {"cwd": "apps/seller-admin-app", "command": "eslint ."}}, "preview": {"dependsOn": ["build"], "executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "@befarmer-platform/seller-admin-app:build"}, "configurations": {"development": {"buildTarget": "@befarmer-platform/seller-admin-app:build:development"}, "production": {"buildTarget": "@befarmer-platform/seller-admin-app:build:production"}}}}}