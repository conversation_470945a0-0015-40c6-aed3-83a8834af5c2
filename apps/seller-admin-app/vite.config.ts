/// <reference types='vitest' />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { join } from 'path';

export default defineConfig(({ mode }) => ({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/seller-admin-app',
  server: {
    port: 4201,
    host: 'localhost',
  },
  preview: {
    port: 4301,
    host: 'localhost',
  },
  plugins: [react()],
  
  css: {
    postcss: {
      plugins: [
        require('tailwindcss')({
          config: join(__dirname, '../../tailwind.config.js'),
        }),
        require('autoprefixer'),
      ],
    },
  },
  
  // Define environment variables for the browser
  define: {
    // ✅ FIXED: Individual environment variable definitions to prevent double encoding
    'process.env.NODE_ENV': JSON.stringify(mode === 'production' ? 'production' : 'development'),

    // Direct microservice URLs with proper base URLs
    'process.env.REACT_APP_API_URL': JSON.stringify(process.env.REACT_APP_API_URL || 'http://localhost:3000'),
    'process.env.REACT_APP_SELLER_SERVICE_URL': JSON.stringify(process.env.REACT_APP_SELLER_SERVICE_URL || 'http://localhost:3001/api/v1'),
    'process.env.REACT_APP_ADMIN_SERVICE_URL': JSON.stringify(process.env.REACT_APP_ADMIN_SERVICE_URL || 'http://localhost:3002/api/v1'),
    'process.env.REACT_APP_FARM_SERVICE_URL': JSON.stringify(process.env.REACT_APP_FARM_SERVICE_URL || 'http://localhost:3005/api/v1'),
    'process.env.REACT_APP_CROP_SERVICE_URL': JSON.stringify(process.env.REACT_APP_CROP_SERVICE_URL || 'http://localhost:3004/api/v1'),
    'process.env.REACT_APP_PLOT_SERVICE_URL': JSON.stringify(process.env.REACT_APP_PLOT_SERVICE_URL || 'http://localhost:3010/api/v1'),
    'process.env.REACT_APP_ORDER_SERVICE_URL': JSON.stringify(process.env.REACT_APP_ORDER_SERVICE_URL || 'http://localhost:3009/api/v1'),
    'process.env.REACT_APP_FINANCIAL_SERVICE_URL': JSON.stringify(process.env.REACT_APP_FINANCIAL_SERVICE_URL || 'http://localhost:3006/api/v1'),
    'process.env.REACT_APP_INVENTORY_SERVICE_URL': JSON.stringify(process.env.REACT_APP_INVENTORY_SERVICE_URL || 'http://localhost:3007/api/v1'),
    'process.env.REACT_APP_ANALYTICS_SERVICE_URL': JSON.stringify(process.env.REACT_APP_ANALYTICS_SERVICE_URL || 'http://localhost:3003/api/v1'),
    'process.env.REACT_APP_NOTIFICATION_SERVICE_URL': JSON.stringify(process.env.REACT_APP_NOTIFICATION_SERVICE_URL || 'http://localhost:3008/api/v1'),
  },
  build: {
    outDir: './dist',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
  test: {
    watch: false,
    globals: true,
    environment: 'jsdom',
    include: ['{src,tests}/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: './test-output/vitest/coverage',
      provider: 'v8' as const,
    },
  },
})); 