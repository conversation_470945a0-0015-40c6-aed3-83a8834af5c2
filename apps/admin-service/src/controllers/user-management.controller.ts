import { Request, Response } from 'express';
import { dbManager } from '../../../../libs/shared/database-connectors/db-manager';
import { FarmerRepository } from '../../../../libs/shared/database-connectors/repositories/mongo/farmer.repository';
import { FarmRepository } from '../../../../libs/shared/database-connectors/repositories/mongo/farm.repository';
import { FarmerOnboardingService } from '../../../../apps/farmer-service/src/services/farmer-onboarding.service';
import { RegisterFarmerDto } from '../../../../apps/farmer-service/src/dto/farmer.dto';
import { AdminModel } from '../../../../libs/shared/database-connectors/schemas/mongo/admin.schema';

export class UserManagementController {
  private farmerRepository: FarmerRepository;
  private farmRepository: FarmRepository;

  constructor() {
    const mongoConnector = dbManager.getMongoDBConnector();
    this.farmerRepository = new FarmerRepository(mongoConnector);
    this.farmRepository = new FarmRepository(mongoConnector);
  }

  /**
   * Get all users (admins and sellers combined)
   */
  getAllUsers = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, role, status } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Get admins
      const adminQuery: any = {};
      if (role && role !== 'SELLER') {
        adminQuery['personalInfo.role'] = role;
      }

      const admins = await AdminModel.find(adminQuery)
        .select('-password')
        .limit(limitNum)
        .skip((pageNum - 1) * limitNum);

      // Get sellers if role is not specified or is SELLER
      let sellers: any[] = [];
      if (!role || role === 'SELLER') {
        const sellerQuery: any = {};
        if (status) {
          sellerQuery.status = status;
        }
        const farmerResult = await this.farmerRepository.findAll(pageNum, limitNum);
        sellers = farmerResult.farmers.filter(farmer => !status || farmer.status === status);
      }

      // Combine and format users
      const users = [
        ...admins.map(admin => ({
          id: admin.adminId,
          type: 'ADMIN',
          name: admin.personalInfo.name,
          email: admin.personalInfo.email,
          role: admin.personalInfo.role,
          status: 'ACTIVE',
          lastLogin: admin.activity.lastLogin,
          createdAt: admin.createdAt
        })),
        ...sellers.map(seller => ({
          id: seller.sellerId,
          type: 'SELLER',
          name: seller.personalInfo.name,
          email: seller.personalInfo.email,
          role: 'SELLER',
          status: seller.status,
          lastLogin: seller.activity?.lastLogin,
          createdAt: seller.createdAt
        }))
      ];

      const total = admins.length + sellers.length;

      res.status(200).json({
        success: true,
        data: {
          users,
          pagination: {
            total,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(total / limitNum)
          }
        }
      });
    } catch (error) {
      console.error('Error getting all users:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to fetch users'
        }
      });
    }
  };

  /**
   * Get user by ID
   */
  getUserById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;

      // Try to find admin first
      let user = await AdminModel.findOne({ adminId: userId }).select('-password');

      if (user) {
        res.status(200).json({
          success: true,
          data: {
            id: user.adminId,
            type: 'ADMIN',
            name: user.personalInfo.name,
            email: user.personalInfo.email,
            role: user.personalInfo.role,
            status: 'ACTIVE',
            lastLogin: user.activity.lastLogin,
            permissions: user.personalInfo.permissions,
            createdAt: user.createdAt
          }
        });
        return;
      }

      // Try to find farmer
      const farmer = await this.farmerRepository.findById(userId);
      if (farmer) {
        res.status(200).json({
          success: true,
          data: {
            id: farmer.farmerId,
            type: 'FARMER',
            name: farmer.personalInfo.name,
            email: farmer.personalInfo.email,
            role: 'FARMER',
            status: farmer.status,
            verificationStatus: farmer.verificationStatus,
            lastLogin: null, // Farmer schema doesn't have lastLogin
            createdAt: farmer.createdAt
          }
        });
        return;
      }

      res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    } catch (error) {
      console.error('Error getting user:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get user'
        }
      });
    }
  };

  /**
   * Update user status
   */
  updateUserStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const { status } = req.body;
      
      res.status(200).json({
        success: true,
        data: {
          message: `User ${userId} status updated to ${status}`,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error updating user status:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update user status'
        }
      });
    }
  };

  /**
   * Delete user
   */
  deleteUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      
      res.status(200).json({
        success: true,
        data: {
          message: `User ${userId} deleted successfully`,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to delete user'
        }
      });
    }
  };

  /**
   * Get all farmers
   */
  getAllFarmers = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const mongoConnector = dbManager.getMongoDBConnector();
      if (!mongoConnector) {
        res.status(500).json({
          success: false,
          error: {
            code: 'DB_NOT_CONNECTED',
            message: 'MongoDB connector not initialized.'
          }
        });
        return;
      }
      const farmerRepo = new FarmerRepository(mongoConnector);
      const { farmers, total, pages } = await farmerRepo.findAll(Number(page), Number(limit));
      res.status(200).json({
        success: true,
        data: {
          farmers,
          total,
          page: Number(page),
          totalPages: pages
        }
      });
    } catch (error) {
      console.error('Error getting farmers:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farmers'
        }
      });
    }
  };

  /**
   * Get seller by ID
   */
  getSellerById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmerId } = req.params;

      const farmer = await this.farmerRepository.findById(farmerId);

      if (!farmer) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARMER_NOT_FOUND',
            message: 'Farmer not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          id: farmer.farmerId,
          name: farmer.personalInfo.name,
          email: farmer.personalInfo.email,
          contact: farmer.personalInfo.contact,
          address: farmer.personalInfo.address,
          status: farmer.status,
          verificationStatus: farmer.verificationStatus,
          documents: farmer.documents,
          bankDetails: farmer.bankDetails,
          farms: farmer.farms,
          statusHistory: farmer.statusHistory,
          createdAt: farmer.createdAt,
          updatedAt: farmer.updatedAt
        }
      });
    } catch (error) {
      console.error('Error getting seller:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get seller'
        }
      });
    }
  };

  /**
   * Verify seller
   */
  verifySeller = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmerId } = req.params;
      const { approved, notes, adminId } = req.body;

      const farmer = await this.farmerRepository.findById(farmerId);

      if (!farmer) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARMER_NOT_FOUND',
            message: 'Farmer not found'
          }
        });
        return;
      }

      const newStatus = approved ? 'VERIFIED' : 'REJECTED';
      const newFarmerStatus = approved ? 'ACTIVE' : 'SUSPENDED';

      // Update farmer verification status and status
      const updatedFarmer = await this.farmerRepository.update(farmerId, {
        verificationStatus: newStatus,
        status: newFarmerStatus,
        statusHistory: [
          ...farmer.statusHistory,
          {
            status: newStatus,
            updatedBy: adminId || 'ADMIN',
            reason: notes || `Farmer ${approved ? 'verified' : 'rejected'} by admin`,
            updatedAt: new Date()
          }
        ]
      });

      res.status(200).json({
        success: true,
        data: {
          message: `Farmer ${approved ? 'verified' : 'rejected'} successfully`,
          farmer: updatedFarmer,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error verifying farmer:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to verify seller'
        }
      });
    }
  };

  /**
   * Update seller status
   */
  updateSellerStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmerId } = req.params;
      const { status, reason, adminId } = req.body;

      const farmer = await this.farmerRepository.findById(farmerId);

      if (!farmer) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARMER_NOT_FOUND',
            message: 'Farmer not found'
          }
        });
        return;
      }

      // Update farmer status with history
      const updatedFarmer = await this.farmerRepository.update(farmerId, {
        status,
        statusHistory: [
          ...farmer.statusHistory,
          {
            status,
            updatedBy: adminId || 'ADMIN',
            reason: reason || `Status updated to ${status}`,
            updatedAt: new Date()
          }
        ]
      });

      res.status(200).json({
        success: true,
        data: {
          message: `Farmer status updated to ${status}`,
          farmer: updatedFarmer,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error updating seller status:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update seller status'
        }
      });
    }
  };

  /**
   * Update farmer details
   */
  updateFarmer = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmerId } = req.params;
      const updateData = req.body;

      const farmer = await this.farmerRepository.findById(farmerId);

      if (!farmer) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARMER_NOT_FOUND',
            message: 'Farmer not found'
          }
        });
        return;
      }

      // Remove fields that shouldn't be updated directly
      delete updateData.farmerId;
      delete updateData.createdAt;
      delete updateData.password;

      const updatedFarmer = await this.farmerRepository.update(farmerId, updateData);

      res.status(200).json({
        success: true,
        data: {
          message: 'Farmer updated successfully',
          farmer: updatedFarmer
        }
      });
    } catch (error) {
      console.error('Error updating seller:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update seller'
        }
      });
    }
  };

  /**
   * Get all farms
   */
  getAllFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, farmerId, status } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      const { farms } = await this.farmRepository.findAll(pageNum, limitNum);

      // Apply filters if provided
      let filteredFarms = farms;
      if (farmerId) {
        filteredFarms = filteredFarms.filter(farm => farm.farmerId === farmerId);
      }
      if (status) {
        filteredFarms = filteredFarms.filter(farm => farm.status === status);
      }

      res.status(200).json({
        success: true,
        data: {
          farms: filteredFarms,
          pagination: {
            total: filteredFarms.length,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(filteredFarms.length / limitNum)
          }
        }
      });
    } catch (error) {
      console.error('Error getting farms:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farms'
        }
      });
    }
  };

  /**
   * Get farm by ID
   */
  getFarmById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;

      const farm = await this.farmRepository.findById(farmId);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARM_NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: farm
      });
    } catch (error) {
      console.error('Error getting farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farm'
        }
      });
    }
  };

  /**
   * Update farm details
   */
  updateFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const updateData = req.body;

      const farm = await this.farmRepository.findById(farmId);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARM_NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      // Remove fields that shouldn't be updated directly
      delete updateData.farmId;
      delete updateData.createdAt;

      const updatedFarm = await this.farmRepository.update(farmId, updateData);

      res.status(200).json({
        success: true,
        data: {
          message: 'Farm updated successfully',
          farm: updatedFarm
        }
      });
    } catch (error) {
      console.error('Error updating farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update farm'
        }
      });
    }
  };

  /**
   * Verify farm
   */
  verifyFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const { approved, notes, adminId } = req.body;

      const farm = await this.farmRepository.findById(farmId);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARM_NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      // Since farm schema doesn't have verification fields, we'll add them to certifications
      // and update the status based on approval
      const currentCertifications = farm.certifications || [];
      let verificationCert = `${approved ? 'VERIFIED' : 'REJECTED'}_BY_ADMIN_${adminId || 'ADMIN'}_${new Date().toISOString()}`;
      if (notes) {
        verificationCert += `_NOTES_${notes.replace(/\s+/g, '_')}`;
      }

      const verificationData = {
        certifications: [...currentCertifications, verificationCert],
        status: (approved ? 'ACTIVE' : 'INACTIVE') as 'ACTIVE' | 'INACTIVE'
      };

      const updatedFarm = await this.farmRepository.update(farmId, verificationData);

      res.status(200).json({
        success: true,
        data: {
          message: `Farm ${approved ? 'verified' : 'rejected'} successfully`,
          farm: updatedFarm,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error verifying farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to verify farm'
        }
      });
    }
  };

  /**
   * Onboard a new seller (admin)
   */
  onboardSeller = async (req: Request, res: Response): Promise<void> => {
    try {
      const registerDto: RegisterFarmerDto = req.body;
      // Validate required fields
      if (!registerDto.name || !registerDto.email || !registerDto.contact || !registerDto.address || !registerDto.password) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Missing required fields for farmer onboarding.'
          }
        });
        return;
      }
      const mongoConnector = dbManager.getMongoDBConnector();
      if (!mongoConnector) {
        res.status(500).json({
          success: false,
          error: {
            code: 'DB_NOT_CONNECTED',
            message: 'MongoDB connector not initialized.'
          }
        });
        return;
      }
      const onboardingService = new FarmerOnboardingService(mongoConnector);
      const farmer = await onboardingService.registerFarmer(registerDto);
      
      // If additional details are provided, update the seller with those details
      if (registerDto.documents || registerDto.bankDetails || registerDto.farmingInfo) {
        const additionalDetails: any = {};
        
        if (registerDto.documents) {
          additionalDetails.documents = registerDto.documents;
        }
        
        if (registerDto.bankDetails) {
          additionalDetails.bankDetails = registerDto.bankDetails;
        }
        
        if (registerDto.farmingInfo) {
          additionalDetails.farmingInfo = registerDto.farmingInfo;
        }
        
        // Update the farmer with additional details
        await this.farmerRepository.update(farmer.farmerId, additionalDetails);

        // Fetch the updated farmer
        const updatedFarmer = await this.farmerRepository.findById(farmer.farmerId);
        res.status(201).json({
          success: true,
          data: updatedFarmer
        });
        return;
      }

      res.status(201).json({
        success: true,
        data: farmer
      });
    } catch (error: any) {
      console.error('Error onboarding farmer:', error);
      if (error.message === 'Email already registered') {
        res.status(409).json({
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: error.message
          }
        });
      } else {
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Failed to onboard seller.'
          }
        });
      }
    }
  };
} 
