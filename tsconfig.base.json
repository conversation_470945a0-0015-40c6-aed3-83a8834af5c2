{"compilerOptions": {"baseUrl": ".", "composite": true, "declarationMap": true, "emitDeclarationOnly": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022", "dom"], "module": "esnext", "moduleResolution": "bundler", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2022", "customConditions": ["development"], "paths": {"@befarmer-platform/admin-features": ["libs/admin-features/src/index.ts"], "@befarmer-platform/seller-features": ["libs/seller-features/src/index.ts"], "@befarmer-platform/shared-ui": ["libs/shared-ui/src/index.ts"], "@befarmer-platform/shared-utils": ["libs/shared-utils/src/index.ts"]}}}