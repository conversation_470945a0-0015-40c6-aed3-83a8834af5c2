{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/tsconfig.storybook.json"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/cypress/plugin", "options": {"targetName": "e2e", "openTargetName": "open-cypress", "componentTestingTargetName": "component-test", "ciTargetName": "e2e-ci"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}, {"plugin": "@nx/rollup/plugin", "options": {"buildTargetName": "build", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "generators": {"@nx/react": {"application": {"babel": true, "style": "scss", "linter": "eslint", "bundler": "webpack"}, "component": {"style": "scss"}, "library": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest"}}}, "targetDefaults": {"test": {"dependsOn": ["^build"]}, "@nx/vite:test": {"cache": true, "inputs": ["default", "^production"]}, "@nx/vite:build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}}