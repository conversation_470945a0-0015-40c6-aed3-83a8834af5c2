# BeFarma Seller Frontend - Project Structure

## Root Directory Structure

```
seller_frontend/
├── apps/
│   ├── seller-app/                          # Main seller application
│   │   ├── src/
│   │   │   ├── app/
│   │   │   │   ├── app.tsx
│   │   │   │   └── nx-welcome.tsx
│   │   │   ├── assets/
│   │   │   ├── main.tsx                     # App entry point
│   │   │   └── styles.scss
│   │   ├── .babelrc
│   │   ├── eslint.config.mjs
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   ├── tsconfig.app.json
│   │   └── webpack.config.js
│   │
│   └── seller-admin-app/                    # Admin application
│       ├── src/
│       │   ├── app/
│       │   │   ├── app.spec.tsx
│       │   │   ├── app.tsx
│       │   │   └── nx-welcome.tsx
│       │   ├── assets/
│       │   └── main.tsx
│       ├── .babelrc
│       ├── eslint.config.mjs
│       ├── jest.config.ts
│       ├── package.json
│       ├── tsconfig.json
│       ├── tsconfig.app.json
│       ├── tsconfig.spec.json
│       └── webpack.config.js
│
├── libs/                                    # Shared libraries
│   ├── admin-features/                      # Admin-specific features
│   │   ├── src/
│   │   │   ├── lib/
│   │   │   │   ├── admin-features.spec.tsx
│   │   │   │   └── admin-features.tsx
│   │   │   └── index.ts
│   │   ├── .babelrc
│   │   ├── eslint.config.mjs
│   │   ├── jest.config.ts
│   │   ├── package.json
│   │   ├── project.json
│   │   ├── README.md
│   │   ├── rollup.config.cjs
│   │   ├── tsconfig.json
│   │   ├── tsconfig.lib.json
│   │   └── tsconfig.spec.json
│   │
│   ├── seller-features/                     # Seller-specific features
│   │   ├── src/
│   │   │   ├── lib/
│   │   │   │   ├── seller-features.spec.tsx
│   │   │   │   └── seller-features.tsx
│   │   │   └── index.ts
│   │   ├── .babelrc
│   │   ├── eslint.config.mjs
│   │   ├── jest.config.ts
│   │   ├── package.json
│   │   ├── project.json
│   │   ├── README.md
│   │   ├── rollup.config.cjs
│   │   ├── tsconfig.json
│   │   ├── tsconfig.lib.json
│   │   └── tsconfig.spec.json
│   │
│   ├── shared-ui/                           # Shared UI components and design system
│   │   ├── src/
│   │   │   ├── lib/
│   │   │   │   ├── components/              # UI component library
│   │   │   │   │   ├── Header/             # App shell header (Phase 2.2)
│   │   │   │   │   ├── Sidebar/            # App shell sidebar (Phase 2.2)
│   │   │   │   │   ├── Footer/             # App shell footer (Phase 2.2)
│   │   │   │   │   ├── Navigation/         # App shell navigation (Phase 2.2)
│   │   │   │   │   ├── Layout/             # App shell layout (Phase 2.2)
│   │   │   │   │   └── index.ts             # Component barrel exports
│   │   │   │   ├── theme/                   # Design system (Phase 1.1 ✅)
│   │   │   │   │   ├── colors.ts            # Agricultural color palette
│   │   │   │   │   ├── typography.ts        # Font and text system
│   │   │   │   │   ├── spacing.ts           # 4px grid spacing system
│   │   │   │   │   └── index.ts             # Theme barrel exports
│   │   │   │   ├── shared-ui.module.scss
│   │   │   │   ├── shared-ui.spec.tsx
│   │   │   │   └── shared-ui.tsx
│   │   │   └── index.ts
│   │   ├── .babelrc
│   │   ├── eslint.config.mjs
│   │   ├── jest.config.ts
│   │   ├── package.json
│   │   ├── project.json
│   │   ├── README.md
│   │   ├── rollup.config.cjs
│   │   ├── tsconfig.json
│   │   ├── tsconfig.lib.json
│   │   └── tsconfig.spec.json
│   │
│   └── shared-utils/                        # Shared utilities and services
│       ├── src/
│       │   ├── lib/
│       │   │   ├── environment/             # Environment configuration (Phase 1.1 ✅)
│       │   │   │   ├── environment.config.ts  # Dev/Prod environment configs
│       │   │   │   └── environment.types.ts   # TypeScript environment types
│       │   │   ├── axios/                   # API client configuration (✅ Enhanced)
│       │   │   │   └── instance.ts          # Multi-service Axios instances with Redux integration
│       │   │   ├── redux/                   # State management system (✅ Complete)
│       │   │   │   ├── slices/              # Redux feature slices
│       │   │   │   │   ├── authSlice.ts     # Authentication & user management
│       │   │   │   │   ├── farmsSlice.ts    # Farm management & monitoring
│       │   │   │   │   ├── cropsSlice.ts    # Crop planning & management
│       │   │   │   │   ├── plotsSlice.ts    # Plot management & availability
│       │   │   │   │   ├── ordersSlice.ts   # Order processing & tracking
│       │   │   │   │   ├── notificationsSlice.ts # System notifications & alerts
│       │   │   │   │   └── uiSlice.ts       # UI state & user preferences
│       │   │   │   ├── api/                 # RTK Query API layer
│       │   │   │   │   └── apiSlice.ts      # Centralized API endpoints for all microservices
│       │   │   │   ├── store.ts             # Redux store configuration with persistence
│       │   │   │   ├── hooks.ts             # Typed Redux hooks (useAppDispatch, useAppSelector)
│       │   │   │   └── index.ts             # Redux barrel exports
│       │   │   └── shared-utils.ts
│       │   └── index.ts                     # Updated barrel exports (Redux + Axios + Utils)
│       ├── package.json
│       ├── project.json
│       ├── README.md
│       ├── tsconfig.json
│       └── tsconfig.lib.json
│
├── .vscode/                                 # VS Code workspace settings
├── .editorconfig                           # Editor configuration
├── .gitignore                              # Git ignore rules
├── .prettierignore                         # Prettier ignore rules (Updated Phase 1.1 ✅)
├── .prettierrc                             # Prettier formatting config (Phase 1.1 ✅)
├── businees requirement document.md        # Business requirements
├── development-planning-sheet.md           # Development planning (Phase 1.1 ✅)
├── eslint.config.mjs                       # ESLint configuration (Phase 1.1 ✅)
├── graph.json                              # Nx project graph
├── jest.config.ts                          # Jest testing config
├── jest.preset.js                          # Jest preset
├── nx.json                                 # Nx workspace config
├── package.json                            # Root dependencies (Updated with Redux packages)
├── package-lock.json                       # Locked dependencies
├── postcss.config.js                       # PostCSS config
├── projectStructure.md                     # This file
├── README.md                               # Project documentation
├── tailwind.config.js                      # Tailwind CSS config
├── tsconfig.base.json                      # Base TypeScript config
└── tsconfig.json                           # Root TypeScript config
```

## Phase 1.1 Completion Status ✅

### Environment Configuration System
- `libs/shared-utils/src/lib/environment/environment.config.ts` - Dev/Prod configurations
- `libs/shared-utils/src/lib/environment/environment.types.ts` - TypeScript type definitions
- Service port mappings for all 10 microservices (3001-3010)
- Feature flags and authentication configuration

### Design System Foundation  
- `libs/shared-ui/src/lib/theme/colors.ts` - Agricultural color palette
- `libs/shared-ui/src/lib/theme/typography.ts` - Inter font system with agricultural sizing
- `libs/shared-ui/src/lib/theme/spacing.ts` - 4px grid-based spacing system
- `libs/shared-ui/src/lib/theme/index.ts` - Theme barrel exports

### Code Quality Configuration
- Enhanced ESLint configuration with agricultural platform rules
- Comprehensive Prettier formatting with overrides
- TypeScript strict mode configuration
- Module boundary enforcement for Nx monorepo

## Redux/Axios Enhancement Completion ✅

### Comprehensive Redux State Management
- **Store Configuration:** Redux Toolkit + Redux Persist + RTK Query integration
- **Authentication Slice:** JWT management, user roles (seller/admin), verification status
- **Farm Management Slice:** Farm registration, monitoring, verification with async thunks
- **Crop Management Slice:** Crop planning, health monitoring, growth stages
- **Plot Management Slice:** Plot configuration, availability, revenue tracking
- **Order Management Slice:** Order processing, status tracking, customer management
- **Notifications Slice:** Alert system, notification preferences, read status
- **UI State Slice:** Theme management, sidebar, modals, user preferences

### Enhanced Axios Configuration
- **Multi-Service Architecture:** Dedicated Axios instances for all 10 microservices
- **Redux Integration:** Automatic token management from Redux store
- **Service-Specific Handling:** Individual error handling and logging per service
- **Development Logging:** Service-specific request/response logging
- **Authentication Flow:** Automatic token refresh and logout on 401 errors

### RTK Query API Layer
- **Centralized API Management:** Single API slice for all microservices
- **Type-Safe Hooks:** Generated hooks for all endpoints with TypeScript support
- **Cache Management:** Intelligent cache invalidation and data synchronization
- **Optimistic Updates:** Built-in optimistic updates for better UX

### Production-Ready Features
- **Type Safety:** Full TypeScript integration with strict mode
- **Performance:** Optimized bundle size and lazy loading support
- **Error Handling:** Comprehensive error boundaries and user-friendly messages
- **Accessibility:** Agricultural domain-specific ARIA labels and screen reader support

## Upcoming Phase 1.2 Structure

### Implemented UI Components (Phase 1.2 ✅)
```
libs/shared-ui/src/lib/components/
├── Alert/                                  # Notification component (✅ Complete)
│   ├── Alert.tsx
│   ├── Alert.stories.tsx
│   ├── Alert.test.tsx
│   └── index.ts
├── Button/                                 # Small/large variants (✅ Complete)
│   ├── Button.tsx
│   ├── Button.stories.tsx
│   ├── Button.test.tsx
│   └── index.ts
├── Card/                                   # White cards with subtle shadows (✅ Complete)
│   ├── Card.tsx
│   ├── Card.stories.tsx
│   ├── Card.test.tsx
│   └── index.ts
├── Input/                                  # Form field components (✅ Complete)
│   ├── Input.tsx
│   ├── Input.stories.tsx
│   ├── Input.test.tsx
│   └── index.ts
├── Modal/                                  # Dialog/modal component (✅ Complete)
│   ├── Modal.tsx
│   ├── Modal.stories.tsx
│   ├── Modal.test.tsx
│   └── index.ts
└── Spinner/                                # Agricultural-themed loading spinner (✅ Complete)
    ├── Spinner.tsx
    ├── Spinner.stories.tsx
    ├── Spinner.test.tsx
    └── index.ts
```

### Planned Utilities (Phase 1.2)
```
libs/shared-utils/src/lib/
├── auth/                                   # Authentication utilities (Phase 2.1)
│   ├── auth-context.tsx                    # React auth context
│   ├── auth-hooks.ts                       # Authentication hooks
│   ├── route-protection.tsx                # Protected route components
│   └── permissions.ts                      # Permission checking utilities
├── forms/                                  # Form utilities
│   ├── validation.ts                       # Form validation helpers
│   └── form-utils.ts                       # Form helper functions
├── files/                                  # File utilities
│   ├── file-upload.ts                      # File upload utilities
│   └── image-optimization.ts               # Image optimization utilities
└── date/                                   # Date utilities
    └── date-utils.ts                       # Date/time utilities
```

## New Files Added
- apps/seller-admin-app/project.json       # ✅ Added (Nx Vite targets)
- apps/seller-admin-app/vite.config.ts     # ✅ Added (Vite config)