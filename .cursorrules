# BeFarma Seller Frontend - Cursor Rules
# Agricultural Technology Platform - UI/UX Development Guidelines

## Project Context
- **Project Name:** BeFarma - Agricultural Technology Platform
- **Architecture:** Nx Monorepo with React + TypeScript
- **Applications:** seller-app (farmers) + seller-admin-app (administrators)
- **Domain:** Agricultural technology, farm management, crop monitoring
- **Target Users:** Farmers/Sellers and Agricultural Administrators
- **Business Model:** Plot-based farming investment and revenue sharing

## 🔄 **CRITICAL: Development Synchronization Rules**
### **MANDATORY: All development MUST stay synchronized with planning documents**

# IMPORTANT

1. Needed to always stay updated and for every change you have to check and update the documents @development-planning-sheet.md @projectStructure.md @businees requirement document.md 

#### Document Synchronization Requirements:
1. **Before ANY development work:**
   - ✅ Check `development-planning-sheet.md` for current phase
   - ✅ Verify task dependencies are completed
   - ✅ Confirm alignment with `projectStructure.md`

2. **During development:**
   - 🔄 Update checkbox status in `development-planning-sheet.md` when tasks start
   - 📝 Document any structural changes in `projectStructure.md`
   - 🎯 Ensure backend integration aligns with specified service ports

3. **After completing tasks:**
   - ✅ Mark tasks as completed `[x]` in `development-planning-sheet.md`
   - 📋 Update progress tracking
   - 🔍 Validate next phase dependencies

#### Phase Progression Rules:
```typescript
// Current Phase Tracking System
interface PhaseStatus {
  currentPhase: string;
  completedTasks: string[];
  blockedBy: string[];
  readyForNext: boolean;
}

// ALWAYS verify phase dependencies before proceeding
const validatePhaseProgression = (currentPhase: number, targetPhase: number) => {
  // Implementation must check development-planning-sheet.md
  // Ensure all dependencies are satisfied
};
```

#### File Structure Alignment:
```bash
# BEFORE creating any new files/directories:
# 1. Check projectStructure.md for correct location
# 2. Update projectStructure.md if adding new structure
# 3. Ensure alignment with Nx monorepo conventions

# Example validation:
libs/shared-ui/src/lib/components/Button/  # ✅ Aligned with projectStructure.md
apps/seller-app/src/features/farm/       # ✅ Follows planning sheet Phase 3.3
```

#### Backend Integration Verification:
```typescript
// MANDATORY: Verify backend service alignment
const BACKEND_SERVICES = {
  SELLER_SERVICE: 3001,    // Phase 3.1, 3.2 (development-planning-sheet.md)
  ADMIN_SERVICE: 3002,     // Phase 8.1-8.4 
  ANALYTICS_SERVICE: 3003, // Phase 6.1, 6.2
  CROP_SERVICE: 3004,      // Phase 4.1-4.4
  FARM_SERVICE: 3005,      // Phase 3.3
  FINANCIAL_SERVICE: 3006, // Phase 5.2
  INVENTORY_SERVICE: 3007, // Phase 5.3
  NOTIFICATION_SERVICE: 3008, // Phase 7.1, 7.2
  ORDER_SERVICE: 3009,     // Phase 5.1
  PLOT_SERVICE: 3010       // Phase 3.4
};

// Before any API integration, verify:
// 1. Correct phase in development-planning-sheet.md
// 2. Dependencies are completed
// 3. Service port matches planning document
```

## Technology Stack
- **Framework:** React 18+ with TypeScript
- **Build System:** Nx monorepo
- **Styling:** Tailwind CSS
- **State Management:** React Context API / Redux (for complex states)
- **Routing:** React Router
- **HTTP Client:** Axios
- **Testing:** Jest + React Testing Library + Cypress
- **Package Manager:** npm

## Design System & Brand Guidelines

### Color Palette (Agricultural Theme)
**Primary Colors:**
- Text: `#000000` (black) - All primary text content
- Accent Green: `#22C55E` (light green) - Small buttons, icons, symbols (leaf color)
- Primary Orange: `#F97316` (light orange) - Large buttons, primary actions (fruit color)
- Background: `#FFFFFF` (white) - Primary background
- Secondary Background: `#F8F9FA` (light gray) - Cards and sections

**Supporting Colors:**
- Success: `#10B981` (green)
- Warning: `#F59E0B` (amber)
- Error: `#EF4444` (red)
- Info: `#3B82F6` (blue)
- Neutral Gray: `#6B7280`
- Light Gray: `#E5E7EB`
- Dark Gray: `#374151`

### Typography Guidelines
- **Primary Font:** Inter or system fonts
- **Headings:** font-weight: 600-700 (semibold to bold)
- **Body Text:** font-weight: 400 (regular)
- **Labels:** font-weight: 500 (medium)
- **Captions:** font-weight: 400, smaller size

### Component Design Patterns

#### Button Variants
```typescript
// Large buttons: orange background, white text
<Button variant="primary" size="large">Plant Crop</Button>

// Small buttons: green background, white text  
<Button variant="secondary" size="small">Add Plot</Button>

// Icon buttons: green for active, gray for inactive
<IconButton icon="heart" active={isLiked} />
```

#### Card Components
```typescript
// Clean white cards with subtle shadows
<Card className="bg-white shadow-sm border border-gray-200">
  <CardHeader>Farm Overview</CardHeader>
  <CardContent>...</CardContent>
</Card>
```

### Responsive Design Rules
- **Mobile-First:** Design for 320px+ screens first
- **Breakpoints:** sm:640px, md:768px, lg:1024px, xl:1280px
- **Touch Targets:** Minimum 44px x 44px for mobile
- **Spacing:** Use consistent 4px grid system (space-1, space-2, etc.)

## Agricultural UX Patterns

### Farm Management Interface
- **Farm Cards:** Display farm name, location, total area, active crops
- **Plot Grid:** Visual grid showing plot status (Available/Leased/Cultivating)
- **Crop Timeline:** Horizontal timeline showing growth stages
- **Status Indicators:** Color-coded badges for health (Healthy/Warning/Critical)

### Crop Monitoring Dashboard
- **Growth Stages:** Planting → Growing → Maturing → Ready
- **Health Status:** Green (Healthy), Yellow (Warning), Red (Critical)
- **Progress Bars:** Visual indicators for crop maturity percentage
- **Weather Integration:** Current conditions + 7-day forecast

### Data Visualization
- **Charts:** Use agricultural green (#22C55E) as primary color
- **Maps:** GPS coordinates display with farm boundaries
- **Revenue Charts:** Orange (#F97316) for positive trends
- **Resource Usage:** Water drops, fertilizer icons, pesticide symbols

## File Structure & Naming Conventions

### Component Organization
```
libs/shared-ui/src/lib/
├── components/
│   ├── Button/              # Phase 1.2 - Core UI Components
│   │   ├── Button.tsx
│   │   ├── Button.stories.tsx
│   │   ├── Button.test.tsx
│   │   └── index.ts
│   ├── Card/               # Phase 1.2 - Core UI Components
│   ├── Form/               # Phase 1.2 - Core UI Components
│   └── Navigation/         # Phase 2.2 - Application Shell
├── icons/
├── theme/                  # Phase 1.2 - Design System
└── utils/
```

### Naming Conventions
- **Components:** PascalCase (e.g., `FarmCard`, `CropMonitor`)
- **Files:** PascalCase for components, camelCase for utils
- **Props:** camelCase with descriptive names
- **CSS Classes:** Tailwind utilities, BEM for custom styles
- **State Variables:** camelCase, descriptive (e.g., `isPlantingComplete`)

## 📋 **Phase-Specific Development Guidelines**

### Phase 1: Foundation & Infrastructure (Current Priority)
```typescript
// ✅ COMPLETED (as per development-planning-sheet.md):
// - [x] Initialize Nx monorepo structure
// - [x] Configure TypeScript and build system  
// - [x] Set up Tailwind CSS configuration

// 🔄 IN PROGRESS (update planning sheet when working on):
// - [ ] Configure shared workspace libraries
// - [ ] Set up environment configuration files
// - [ ] Configure linting and formatting rules

// 📍 NEXT TASKS (Phase 1.2 - Shared Library Development):
interface SharedUITasks {
  designSystem: {
    colorPalette: boolean;      // Must use agricultural theme colors
    typography: boolean;        // Inter font, specified weights
    spacing: boolean;           // 4px grid system
    baseStyles: boolean;        // Component base styles
  };
  coreComponents: {
    button: boolean;            // Small/large variants (green/orange)
    input: boolean;             // Form field components
    card: boolean;              // White cards with subtle shadows
    modal: boolean;             // Dialog/modal component
    spinner: boolean;           // Agricultural-themed loading spinner
    alert: boolean;             // Notification component
  };
}
```

### Phase 2: Authentication & Core Infrastructure
```typescript
// 🎯 DEPENDENCIES: Phase 1.2 (Shared Utils) must be completed
// 📍 FOCUS: Both seller-app & seller-admin-app

interface AuthenticationTasks {
  authSystem: {
    login: boolean;             // JWT-based authentication
    logout: boolean;            // Session management
    tokenMgmt: boolean;         // JWT token handling
    passwordReset: boolean;     // Reset functionality
    roleBasedAccess: boolean;   // Farmer vs Admin roles
    protectedRoutes: boolean;   // Route protection
  };
  applicationShell: {
    navigation: boolean;        // Main nav structure
    header: boolean;            // Header with user menu
    sidebar: boolean;           // Responsive sidebar
    footer: boolean;            // Footer component
    layouts: boolean;           // Page layout components
    routing: boolean;           // Route configuration
  };
}
```

### Phase 3: Seller App Core Features
```typescript
// 🎯 DEPENDENCIES: Phase 2 must be completed
// 🔌 BACKEND: Seller Service (Port 3001), Farm Service (Port 3005), Plot Service (Port 3010)

interface SellerCoreTasks {
  registration: {            // Phase 3.1 - Seller Service (3001)
    multiStepForm: boolean;   // 6-step registration process
    docVerification: boolean; // Document upload & verification
    progressTracking: boolean; // Step indicators & progress
  };
  profileMgmt: {            // Phase 3.2 - Seller Service (3001)
    dashboard: boolean;       // Profile dashboard
    editing: boolean;         // Information editing
    docMgmt: boolean;         // Document management
    bankInfo: boolean;        // Bank account details
  };
  farmMgmt: {              // Phase 3.3 - Farm Service (3005)
    registration: boolean;    // Farm registration with GPS
    dashboard: boolean;       // Farm overview & mapping
    photoGallery: boolean;    // Farm photo management
  };
  plotMgmt: {              // Phase 3.4 - Plot Service (3010)
    configuration: boolean;   // Plot mapping & boundaries
    availability: boolean;    // Status management
    revenue: boolean;         // Revenue tracking per plot
  };
}
```

## Component Development Guidelines

### Form Components
```typescript
// Agricultural form patterns (align with Phase 3.1 requirements)
interface FarmRegistrationProps {
  onSubmit: (data: FarmData) => void;
  isLoading?: boolean;
  currentStep: number;        // Must track step for planning sheet progress
  totalSteps: number;         // Must be 6 steps as per Phase 3.1
}

// Multi-step forms for complex processes
<StepIndicator currentStep={2} totalSteps={6} />
<FormStep title="Farm Location">
  <GPSLocationPicker onChange={handleLocationChange} />
  <SoilTypeSelector options={soilTypes} />
</FormStep>
```

### Dashboard Widgets
```typescript
// Agricultural dashboard components (Phase 3.2, 3.3, 3.4)
<DashboardGrid>
  <StatsCard 
    title="Total Revenue" 
    value="₹45,000" 
    trend="+12%" 
    icon="rupee"
    color="orange"          // Must use primary orange (#F97316)
  />
  <CropHealthCard 
    crops={activeCrops} 
    onAlertClick={handleHealthAlert}
  />
  <WeatherWidget location={farmLocation} />
</DashboardGrid>
```

### Data Display Patterns
```typescript
// Agricultural data tables (Phase 4+ requirements)
<DataTable 
  columns={[
    { key: 'cropName', label: 'Crop' },
    { key: 'plantingDate', label: 'Planted' },
    { key: 'expectedHarvest', label: 'Expected Harvest' },
    { key: 'status', label: 'Health Status', render: StatusBadge }
  ]}
  data={crops}
/>
```

## API Integration Patterns

### Service Integration
```typescript
// Backend service integration (aligned with microservices & planning phases)
const API_ENDPOINTS = {
  SELLER_SERVICE: 'http://localhost:3001', // Phase 3.1, 3.2
  FARM_SERVICE: 'http://localhost:3005',   // Phase 3.3
  CROP_SERVICE: 'http://localhost:3004',   // Phase 4.1-4.4
  PLOT_SERVICE: 'http://localhost:3010',   // Phase 3.4
  ORDER_SERVICE: 'http://localhost:3009',  // Phase 5.1
  FINANCIAL_SERVICE: 'http://localhost:3006', // Phase 5.2
  INVENTORY_SERVICE: 'http://localhost:3007', // Phase 5.3
  ANALYTICS_SERVICE: 'http://localhost:3003', // Phase 6.1, 6.2
  NOTIFICATION_SERVICE: 'http://localhost:3008', // Phase 7.1, 7.2
  ADMIN_SERVICE: 'http://localhost:3002'   // Phase 8.1-8.4
};

// ⚠️ VALIDATION: Before using any service, verify:
// 1. Current phase allows this integration (check development-planning-sheet.md)
// 2. Dependencies are completed
// 3. Correct service port is used

// API service with agricultural context
class CropService {
  async updateGrowthStage(cropId: string, stage: GrowthStage) {
    // ✅ Available from Phase 4.1 onwards
    // Implementation with proper error handling
  }
  
  async getHealthMetrics(cropId: string) {
    // ✅ Available from Phase 4.2 onwards
    // Return crop health data
  }
}
```

## Agricultural Domain Logic

### Status Management
```typescript
// Crop growth stages (Phase 4.1+ implementation)
type GrowthStage = 'Planting' | 'Growing' | 'Maturing' | 'Ready';

// Crop health status (Phase 4.2+ implementation)
type HealthStatus = 'Healthy' | 'Warning' | 'Critical';

// Plot availability (Phase 3.4+ implementation)
type PlotStatus = 'Available' | 'Leased' | 'Under Cultivation';

// Order processing (Phase 5.1+ implementation)
type OrderStatus = 'Pending' | 'Confirmed' | 'Processing' | 'Completed';
```

### Agricultural Data Types
```typescript
// 📍 PHASE ALIGNMENT: Implement based on development planning phases

interface Farm {                    // Phase 3.3 - Farm Management
  id: string;
  name: string;
  location: GPSCoordinates;         // GPS integration required
  totalArea: number;                // in acres
  soilType: SoilType;
  waterSource: WaterSource;
  infrastructure: Infrastructure[];
  certifications: Certification[];
}

interface Crop {                    // Phase 4.1 - Crop Planning & Setup
  id: string;
  type: CropType;
  plantingDate: Date;
  expectedHarvestDate: Date;
  growthStage: GrowthStage;         // Phase 4.2 - Growth Monitoring
  healthStatus: HealthStatus;       // Phase 4.2 - Health Monitoring
  yieldExpected: number;
  yieldActual?: number;
  resourceUsage: ResourceUsage;     // Phase 4.3 - Resource Management
}

interface Plot {                    // Phase 3.4 - Plot Management
  id: string;
  farmId: string;
  area: number;
  boundaries: GPSCoordinates[];
  status: PlotStatus;
  leasingTerms: LeasingTerms;
  revenue: PlotRevenue;
}
```

## 📊 **Progress Tracking & Validation**

### Development Checkpoint System
```typescript
// 🔍 MANDATORY: Use this system to validate progress alignment

interface DevelopmentCheckpoint {
  phase: string;
  task: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'blocked';
  dependencies: string[];
  backendIntegration?: {
    service: string;
    port: number;
    phase: string;
  };
}

// Example checkpoint validation:
const validateCheckpoint = (task: string): boolean => {
  // 1. Check development-planning-sheet.md for task status
  // 2. Verify all dependencies are completed
  // 3. Confirm backend service alignment
  // 4. Update planning sheet if task status changes
};
```

### File Creation Validation
```typescript
// 🔧 BEFORE creating any new files, validate:

interface FileCreationValidation {
  path: string;
  phase: string;
  alignsWithStructure: boolean;
  updatesRequired: string[];
}

// Example validation for new component:
const validateNewComponent = (componentPath: string) => {
  // 1. Check if path aligns with projectStructure.md
  // 2. Verify current phase allows this component (development-planning-sheet.md)
  // 3. Update projectStructure.md if new directory structure
  // 4. Mark relevant task in development-planning-sheet.md as in-progress
};
```

## Accessibility Guidelines
- **WCAG 2.1 AA Compliance:** Ensure color contrast ratios meet standards
- **Keyboard Navigation:** All interactive elements accessible via keyboard
- **Screen Readers:** Proper ARIA labels, especially for agricultural data
- **Focus Management:** Clear focus indicators for form navigation
- **Alt Text:** Descriptive alt text for crop images, farm photos

## Performance Optimization
- **Image Optimization:** Compress farm/crop photos, use WebP format
- **Lazy Loading:** Implement for crop monitoring dashboard (Phase 4.2+)
- **Code Splitting:** Split by feature (farm-management, crop-monitoring)
- **API Caching:** Cache weather data, crop type information
- **Bundle Analysis:** Keep bundle size under 300KB for mobile users

## Testing Guidelines
```typescript
// Component testing patterns (Phase 10.1 - Unit Testing)
describe('CropHealthCard', () => {
  it('shows warning status for crops needing attention', () => {
    const crop = { healthStatus: 'Warning', name: 'Tomatoes' };
    render(<CropHealthCard crop={crop} />);
    expect(screen.getByText('Warning')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toHaveClass('bg-amber-100');
  });
});

// Agricultural domain testing (Phase 10.1 - Unit Testing)
describe('Revenue Calculation', () => {
  it('calculates plot revenue sharing correctly', () => {
    const plotRevenue = calculatePlotRevenue(plotData, sharePercentage);
    expect(plotRevenue).toEqual(expectedRevenue);
  });
});
```

## Error Handling Patterns
```typescript
// Agricultural-specific error handling
class AgriculturalError extends Error {
  constructor(
    message: string,
    public context: 'crop' | 'farm' | 'plot' | 'weather',
    public severity: 'low' | 'medium' | 'high'
  ) {
    super(message);
  }
}

// User-friendly error messages
const ERROR_MESSAGES = {
  CROP_HEALTH_CRITICAL: 'Your crop needs immediate attention. Please check water and nutrient levels.',
  WEATHER_ALERT: 'Severe weather warning for your area. Consider protective measures for your crops.',
  PLOT_UNAVAILABLE: 'This plot is currently under cultivation and not available for lease.'
};
```

## Animation & Microinteractions
- **Growth Progress:** Smooth animations for crop growth timelines (Phase 4.2+)
- **Status Changes:** Gentle transitions when health status changes (Phase 4.2+)
- **Loading States:** Agricultural-themed loading spinners (Phase 1.2)
- **Success Feedback:** Celebratory animations for harvest completion (Phase 5.1+)
- **Hover Effects:** Subtle farm card hover effects with elevation

## Mobile-Specific Considerations
- **Touch-Friendly:** Large tap targets for farm management on mobile
- **Swipe Gestures:** Swipe between crop monitoring screens (Phase 4.2+)
- **Location Services:** GPS integration for farm location selection (Phase 3.3)
- **Camera Integration:** Photo capture for crop documentation (Phase 3.3, 4.2)
- **Offline Support:** Basic functionality when internet is limited (Phase 9.1)

## Code Quality Rules
1. **TypeScript Strict Mode:** Enable strict type checking
2. **Component Props:** Always define proper TypeScript interfaces
3. **Error Boundaries:** Implement for each major feature area
4. **Loading States:** Always provide loading feedback for API calls
5. **Empty States:** Design empty states for new farmers (no farms/crops yet)
6. **Consistent Naming:** Use agricultural terminology consistently
7. **📋 Planning Sync:** Update development-planning-sheet.md for all task changes
8. **📁 Structure Sync:** Update projectStructure.md for any new file/directory

## Common Agricultural UI Patterns to Implement
- **Seasonal Calendars:** Visual crop planning calendars (Phase 4.1+)
- **Resource Meters:** Water usage, fertilizer levels as progress bars (Phase 4.3+)
- **Map Integration:** GPS farm locations with plot boundaries (Phase 3.3, 3.4)
- **Timeline Components:** Crop lifecycle visualization (Phase 4.2+)
- **Alert Banners:** Weather warnings, crop health alerts (Phase 7.1+)
- **Photo Galleries:** Farm and crop image management (Phase 3.3, 4.2)
- **Status Dashboards:** Overall farm health at a glance (Phase 4.2+)

## Development Priorities
1. **Mobile-First:** Rural farmers primarily use smartphones
2. **Performance:** Fast loading for users with slower internet
3. **Intuitive Navigation:** Simple, clear navigation for farmers
4. **Visual Feedback:** Clear status indicators for all agricultural data
5. **Accessibility:** Support for users with varying technical literacy
6. **📋 Documentation Sync:** Always keep planning documents updated
7. **🔄 Phase Progression:** Follow sequential development as per planning sheet

## 🚨 **CRITICAL REMINDERS:**
1. **BEFORE starting any task:** Check development-planning-sheet.md
2. **DURING development:** Update task status to `[x]` when completed
3. **AFTER creating files:** Update projectStructure.md if needed
4. **FOR backend integration:** Verify correct service port & phase alignment
5. **PHASE DEPENDENCIES:** Never skip to later phases without completing dependencies

Remember: This is an agricultural platform - prioritize clarity, simplicity, and practical functionality over complex features. The end users are farmers who need efficient, reliable tools to manage their agricultural operations. 